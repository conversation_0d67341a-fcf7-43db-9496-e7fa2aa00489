<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SharedConnectionPool.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">SharedConnectionPool.java</span></div><h1>SharedConnectionPool.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import io.grpc.Channel;
import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannel;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.npci.rustyclient.client.interceptor.AuthenticationInterceptor;
import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rustycluster.KeyValueServiceGrpc;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


/**
 * Shared connection pool that provides both synchronous and asynchronous gRPC stubs
 * from the same underlying connection pool, optimizing resource usage.
 */
public class SharedConnectionPool implements AutoCloseable {
<span class="nc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(SharedConnectionPool.class);</span>

    private final GrpcChannelFactory channelFactory;
    private final AuthenticationManager authenticationManager;
    private final Map&lt;String, GenericObjectPool&lt;SharedConnection&gt;&gt; connectionPools;

    /**
     * Create a new SharedConnectionPool.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
<span class="nc" id="L41">    public SharedConnectionPool(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {</span>
<span class="nc" id="L42">        this.channelFactory = new GrpcChannelFactory(config);</span>
<span class="nc" id="L43">        this.authenticationManager = authenticationManager;</span>
<span class="nc" id="L44">        this.connectionPools = new HashMap&lt;&gt;();</span>

        // Initialize connection pools for each node
<span class="nc bnc" id="L47" title="All 2 branches missed.">        for (NodeConfig nodeConfig : config.getNodes()) {</span>
<span class="nc" id="L48">            GenericObjectPoolConfig&lt;SharedConnection&gt; poolConfig = new GenericObjectPoolConfig&lt;&gt;();</span>
            
            // Optimized settings for shared pool
<span class="nc" id="L51">            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());</span>
<span class="nc" id="L52">            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());</span>
<span class="nc" id="L53">            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm</span>
            
            // Performance-oriented validation
<span class="nc" id="L56">            poolConfig.setTestOnBorrow(false); // Disable for performance</span>
<span class="nc" id="L57">            poolConfig.setTestOnReturn(false); // Disable expensive validation</span>
<span class="nc" id="L58">            poolConfig.setTestWhileIdle(true); // Only validate idle connections</span>
<span class="nc" id="L59">            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30));</span>
            
            // High-throughput settings
<span class="nc" id="L62">            poolConfig.setBlockWhenExhausted(false); // Fail fast instead of blocking</span>
<span class="nc" id="L63">            poolConfig.setMaxWait(java.time.Duration.ofMillis(100)); // Quick timeout</span>
<span class="nc" id="L64">            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer</span>
<span class="nc" id="L65">            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead</span>
            
            // JMX monitoring for production
<span class="nc" id="L68">            poolConfig.setJmxEnabled(true);</span>
<span class="nc" id="L69">            poolConfig.setJmxNamePrefix(&quot;RustyClusterShared-&quot; + nodeConfig.getAddress());</span>

<span class="nc" id="L71">            GenericObjectPool&lt;SharedConnection&gt; pool =</span>
                new GenericObjectPool&lt;&gt;(new SharedConnectionFactory(nodeConfig), poolConfig);

<span class="nc" id="L74">            connectionPools.put(nodeConfig.getAddress(), pool);</span>
<span class="nc" id="L75">            logger.info(&quot;Created shared connection pool for node: {}&quot;, nodeConfig);</span>
<span class="nc" id="L76">        }</span>
<span class="nc" id="L77">    }</span>

    /**
     * Borrow a connection wrapper for blocking operations.
     *
     * @param nodeConfig The node configuration
     * @return A connection wrapper for blocking operations
     * @throws Exception If borrowing fails
     */
    public SharedConnectionWrapper borrowBlockingConnection(NodeConfig nodeConfig) throws Exception {
<span class="nc" id="L87">        SharedConnection connection = borrowConnection(nodeConfig);</span>
<span class="nc" id="L88">        return new SharedConnectionWrapper(connection, nodeConfig, this, true);</span>
    }

    /**
     * Borrow a connection wrapper for async operations.
     *
     * @param nodeConfig The node configuration
     * @return CompletableFuture that completes with a connection wrapper for async operations
     */
    public CompletableFuture&lt;SharedConnectionWrapper&gt; borrowAsyncConnection(NodeConfig nodeConfig) {
<span class="nc" id="L98">        return CompletableFuture.supplyAsync(() -&gt; {</span>
            try {
<span class="nc" id="L100">                SharedConnection connection = borrowConnection(nodeConfig);</span>
<span class="nc" id="L101">                return new SharedConnectionWrapper(connection, nodeConfig, this, false);</span>
<span class="nc" id="L102">            } catch (Exception e) {</span>
<span class="nc" id="L103">                throw new RuntimeException(&quot;Failed to borrow connection from shared pool&quot;, e);</span>
            }
        });
    }

    /**
     * Get the authentication manager.
     *
     * @return The authentication manager
     */
    public AuthenticationManager getAuthenticationManager() {
<span class="nc" id="L114">        return authenticationManager;</span>
    }

    private SharedConnection borrowConnection(NodeConfig nodeConfig) throws Exception {
<span class="nc" id="L118">        GenericObjectPool&lt;SharedConnection&gt; pool = connectionPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L120">            throw new IllegalArgumentException(&quot;No pool found for node: &quot; + nodeConfig);</span>
        }
<span class="nc" id="L122">        return pool.borrowObject();</span>
    }

    /**
     * Return a connection to the pool.
     *
     * @param nodeConfig The node configuration
     * @param connection The connection to return
     */
    void returnConnection(NodeConfig nodeConfig, SharedConnection connection) {
<span class="nc" id="L132">        GenericObjectPool&lt;SharedConnection&gt; pool = connectionPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L134">            logger.warn(&quot;No pool found for node: {}&quot;, nodeConfig);</span>
<span class="nc" id="L135">            return;</span>
        }

<span class="nc" id="L138">        pool.returnObject(connection);</span>
<span class="nc" id="L139">        logger.debug(&quot;Connection returned to pool for node: {}&quot;, nodeConfig);</span>
<span class="nc" id="L140">    }</span>

    /**
     * Close the connection pool and release all resources.
     */
    @Override
    public void close() {
<span class="nc bnc" id="L147" title="All 2 branches missed.">        for (GenericObjectPool&lt;SharedConnection&gt; pool : connectionPools.values()) {</span>
<span class="nc" id="L148">            pool.close();</span>
<span class="nc" id="L149">        }</span>
<span class="nc" id="L150">        connectionPools.clear();</span>
<span class="nc" id="L151">        channelFactory.close();</span>
<span class="nc" id="L152">        logger.info(&quot;Shared connection pool closed&quot;);</span>
<span class="nc" id="L153">    }</span>

    /**
     * Wrapper class that holds both blocking and future stubs created from the same channel.
     */
    public static class SharedConnection {
        private final KeyValueServiceGrpc.KeyValueServiceBlockingStub blockingStub;
        private final KeyValueServiceGrpc.KeyValueServiceFutureStub futureStub;

        public SharedConnection(KeyValueServiceGrpc.KeyValueServiceBlockingStub blockingStub,
<span class="nc" id="L163">                               KeyValueServiceGrpc.KeyValueServiceFutureStub futureStub) {</span>
<span class="nc" id="L164">            this.blockingStub = blockingStub;</span>
<span class="nc" id="L165">            this.futureStub = futureStub;</span>
<span class="nc" id="L166">        }</span>

        public KeyValueServiceGrpc.KeyValueServiceBlockingStub getBlockingStub() {
<span class="nc" id="L169">            return blockingStub;</span>
        }

        public KeyValueServiceGrpc.KeyValueServiceFutureStub getFutureStub() {
<span class="nc" id="L173">            return futureStub;</span>
        }
    }

    /**
     * Factory for creating and validating shared connections.
     */
    private class SharedConnectionFactory extends BasePooledObjectFactory&lt;SharedConnection&gt; {
        private final NodeConfig nodeConfig;

<span class="nc" id="L183">        SharedConnectionFactory(NodeConfig nodeConfig) {</span>
<span class="nc" id="L184">            this.nodeConfig = nodeConfig;</span>
<span class="nc" id="L185">        }</span>

        @Override
        public SharedConnection create() {
<span class="nc" id="L189">            ManagedChannel channel = channelFactory.createChannel(nodeConfig);</span>

            // Create channel with authentication interceptor
<span class="nc" id="L192">            Channel interceptedChannel = ClientInterceptors.intercept(channel,</span>
                    new AuthenticationInterceptor(authenticationManager));

            // Create both stub types from the same channel
<span class="nc" id="L196">            KeyValueServiceGrpc.KeyValueServiceBlockingStub blockingStub =</span>
<span class="nc" id="L197">                KeyValueServiceGrpc.newBlockingStub(interceptedChannel);</span>
<span class="nc" id="L198">            KeyValueServiceGrpc.KeyValueServiceFutureStub futureStub =</span>
<span class="nc" id="L199">                KeyValueServiceGrpc.newFutureStub(interceptedChannel);</span>

<span class="nc" id="L201">            return new SharedConnection(blockingStub, futureStub);</span>
        }

        @Override
        public PooledObject&lt;SharedConnection&gt; wrap(SharedConnection connection) {
<span class="nc" id="L206">            return new DefaultPooledObject&lt;&gt;(connection);</span>
        }

        @Override
        public boolean validateObject(PooledObject&lt;SharedConnection&gt; pooledObject) {
            // Basic validation - check if stubs are not null
<span class="nc" id="L212">            SharedConnection connection = pooledObject.getObject();</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">            return connection != null &amp;&amp;</span>
<span class="nc bnc" id="L214" title="All 2 branches missed.">                   connection.getBlockingStub() != null &amp;&amp;</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">                   connection.getFutureStub() != null;</span>
        }
    }

    /**
     * Wrapper that automatically returns connections to the pool when closed.
     */
    public static class SharedConnectionWrapper implements AutoCloseable {
        private final SharedConnection connection;
        private final NodeConfig nodeConfig;
        private final SharedConnectionPool pool;
        private final boolean isBlocking;
<span class="nc" id="L227">        private volatile boolean closed = false;</span>

        SharedConnectionWrapper(SharedConnection connection, NodeConfig nodeConfig,
<span class="nc" id="L230">                               SharedConnectionPool pool, boolean isBlocking) {</span>
<span class="nc" id="L231">            this.connection = connection;</span>
<span class="nc" id="L232">            this.nodeConfig = nodeConfig;</span>
<span class="nc" id="L233">            this.pool = pool;</span>
<span class="nc" id="L234">            this.isBlocking = isBlocking;</span>
<span class="nc" id="L235">        }</span>

        public KeyValueServiceGrpc.KeyValueServiceBlockingStub getBlockingStub() {
<span class="nc bnc" id="L238" title="All 2 branches missed.">            if (closed) {</span>
<span class="nc" id="L239">                throw new IllegalStateException(&quot;Connection wrapper is closed&quot;);</span>
            }
<span class="nc" id="L241">            return connection.getBlockingStub();</span>
        }

        public KeyValueServiceGrpc.KeyValueServiceFutureStub getFutureStub() {
<span class="nc bnc" id="L245" title="All 2 branches missed.">            if (closed) {</span>
<span class="nc" id="L246">                throw new IllegalStateException(&quot;Connection wrapper is closed&quot;);</span>
            }
<span class="nc" id="L248">            return connection.getFutureStub();</span>
        }

        @Override
        public void close() {
<span class="nc bnc" id="L253" title="All 2 branches missed.">            if (!closed) {</span>
<span class="nc" id="L254">                closed = true;</span>
<span class="nc" id="L255">                pool.returnConnection(nodeConfig, connection);</span>
            }
<span class="nc" id="L257">        }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
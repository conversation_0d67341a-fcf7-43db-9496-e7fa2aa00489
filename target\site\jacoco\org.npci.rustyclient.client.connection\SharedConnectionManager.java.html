<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SharedConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">SharedConnectionManager.java</span></div><h1>SharedConnectionManager.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import com.google.common.util.concurrent.ListenableFuture;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.exception.NoAvailableNodesException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rustycluster.KeyValueServiceGrpc;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Unified connection manager that uses a shared connection pool for both
 * synchronous and asynchronous operations, optimizing resource usage.
 */
public class SharedConnectionManager implements AutoCloseable {
<span class="nc" id="L22">    private static final Logger logger = LoggerFactory.getLogger(SharedConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final SharedConnectionPool connectionPool;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final SharedFailbackManager failbackManager;

    /**
     * Create a new SharedConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public SharedConnectionManager(RustyClusterClientConfig config, 
<span class="nc" id="L37">                                 org.npci.rustyclient.client.auth.AuthenticationManager authenticationManager) {</span>
<span class="nc" id="L38">        this.config = config;</span>
<span class="nc" id="L39">        this.connectionPool = new SharedConnectionPool(config, authenticationManager);</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="nc" id="L42">        this.sortedNodes = config.getNodes().stream()</span>
<span class="nc" id="L43">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="nc" id="L44">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="nc" id="L47">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

        // Initialize failback manager - we'll create a custom one for shared pool
<span class="nc" id="L50">        this.failbackManager = new SharedFailbackManager(config, connectionPool, sortedNodes, currentNode);</span>
<span class="nc" id="L51">        this.failbackManager.start();</span>

<span class="nc" id="L53">        logger.info(&quot;SharedConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="nc" id="L54">    }</span>

    /**
     * Execute a synchronous operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(BlockingOperation&lt;T&gt; operation) throws NoAvailableNodesException {
<span class="nc" id="L65">        return executeWithFailover(operation, OperationType.READ);</span>
    }

    /**
     * Execute a synchronous operation with automatic failover and operation type.
     *
     * @param operation The operation to execute
     * @param operationType The type of operation (READ or WRITE)
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(BlockingOperation&lt;T&gt; operation, OperationType operationType) 
            throws NoAvailableNodesException {
        
<span class="nc" id="L80">        NodeConfig node = currentNode.get();</span>
<span class="nc bnc" id="L81" title="All 2 branches missed.">        long timeoutMs = operationType == OperationType.READ ? config.getReadTimeoutMs() : config.getWriteTimeoutMs();</span>

<span class="nc bnc" id="L83" title="All 2 branches missed.">        for (int attempt = 0; attempt &lt;= config.getMaxRetries(); attempt++) {</span>
<span class="nc" id="L84">            try (SharedConnectionPool.SharedConnectionWrapper wrapper = </span>
<span class="nc" id="L85">                     connectionPool.borrowBlockingConnection(node)) {</span>
                
                // Apply deadline per operation
<span class="nc" id="L88">                KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline =</span>
<span class="nc" id="L89">                    wrapper.getBlockingStub().withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
                
<span class="nc" id="L91">                return operation.execute(stubWithDeadline);</span>
                
<span class="nc" id="L93">            } catch (Exception e) {</span>
<span class="nc" id="L94">                logger.warn(&quot;Operation failed on node {} (attempt {}/{}): {}&quot;, </span>
<span class="nc" id="L95">                           node, attempt + 1, config.getMaxRetries() + 1, e.getMessage());</span>

<span class="nc bnc" id="L97" title="All 2 branches missed.">                if (attempt &lt; config.getMaxRetries()) {</span>
                    try {
<span class="nc" id="L99">                        Thread.sleep(config.getRetryDelayMs());</span>
<span class="nc" id="L100">                    } catch (InterruptedException ie) {</span>
<span class="nc" id="L101">                        Thread.currentThread().interrupt();</span>
<span class="nc" id="L102">                        throw new RuntimeException(&quot;Interrupted during retry delay&quot;, ie);</span>
<span class="nc" id="L103">                    }</span>
                } else {
                    // Try to find the next available node
<span class="nc" id="L106">                    var nextNode = findNextAvailableNode(node);</span>
<span class="nc bnc" id="L107" title="All 2 branches missed.">                    if (nextNode != null) {</span>
<span class="nc" id="L108">                        currentNode.set(nextNode);</span>
<span class="nc" id="L109">                        logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>
                        
                        // Clear authentication state when switching nodes
<span class="nc bnc" id="L112" title="All 2 branches missed.">                        if (config.hasAuthentication()) {</span>
<span class="nc" id="L113">                            connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="nc" id="L114">                            logger.debug(&quot;Cleared authentication state for node switch to: {}&quot;, nextNode);</span>
                        }
                    } else {
<span class="nc" id="L117">                        logger.warn(&quot;No available nodes found after failure&quot;);</span>
                    }
                }
            }
        }

<span class="nc" id="L123">        throw new NoAvailableNodesException(&quot;All nodes are unavailable after retries&quot;);</span>
    }

    /**
     * Execute an asynchronous operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncOperation&lt;T&gt; operation) {
<span class="nc" id="L134">        return executeWithFailoverAsync(operation, OperationType.READ);</span>
    }

    /**
     * Execute an asynchronous operation with automatic failover and operation type.
     *
     * @param operation The operation to execute
     * @param operationType The type of operation (READ or WRITE)
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncOperation&lt;T&gt; operation, OperationType operationType) {
<span class="nc" id="L146">        NodeConfig node = currentNode.get();</span>
<span class="nc bnc" id="L147" title="All 2 branches missed.">        long timeoutMs = operationType == OperationType.READ ? config.getReadTimeoutMs() : config.getWriteTimeoutMs();</span>

<span class="nc" id="L149">        return connectionPool.borrowAsyncConnection(node)</span>
<span class="nc" id="L150">            .thenCompose(wrapper -&gt; {</span>
                try {
                    // Apply deadline per operation
<span class="nc" id="L153">                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =</span>
<span class="nc" id="L154">                        wrapper.getFutureStub().withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
<span class="nc" id="L155">                    ListenableFuture&lt;T&gt; listenableFuture = operation.execute(stubWithDeadline);</span>
<span class="nc" id="L156">                    return toCompletableFuture(listenableFuture)</span>
<span class="nc" id="L157">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="nc" id="L158">                            wrapper.close(); // Return connection to pool</span>
<span class="nc" id="L159">                        });</span>
<span class="nc" id="L160">                } catch (Exception e) {</span>
<span class="nc" id="L161">                    wrapper.close(); // Return connection to pool</span>
<span class="nc" id="L162">                    return CompletableFuture.failedFuture(e);</span>
                }
            })
<span class="nc" id="L165">            .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L166">                logger.warn(&quot;Async operation failed on node {}: {}&quot;, node, throwable.getMessage());</span>
                // For now, we don't implement async retry logic - that would be more complex
                // The caller can handle retries if needed
<span class="nc" id="L169">                throw new RuntimeException(throwable);</span>
            });
    }

    private NodeConfig findNextAvailableNode(NodeConfig currentNode) {
        // Simple round-robin to next node
<span class="nc" id="L175">        int currentIndex = sortedNodes.indexOf(currentNode);</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">        for (int i = 1; i &lt; sortedNodes.size(); i++) {</span>
<span class="nc" id="L177">            int nextIndex = (currentIndex + i) % sortedNodes.size();</span>
<span class="nc" id="L178">            NodeConfig nextNode = sortedNodes.get(nextIndex);</span>
            // In a real implementation, we'd check if the node is healthy
<span class="nc" id="L180">            return nextNode;</span>
        }
<span class="nc" id="L182">        return null;</span>
    }

    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(ListenableFuture&lt;T&gt; listenableFuture) {
<span class="nc" id="L186">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L187">        listenableFuture.addListener(() -&gt; {</span>
            try {
<span class="nc" id="L189">                completableFuture.complete(listenableFuture.get());</span>
<span class="nc" id="L190">            } catch (Exception e) {</span>
<span class="nc" id="L191">                completableFuture.completeExceptionally(e);</span>
<span class="nc" id="L192">            }</span>
<span class="nc" id="L193">        }, Runnable::run);</span>
<span class="nc" id="L194">        return completableFuture;</span>
    }

    @Override
    public void close() {
<span class="nc bnc" id="L199" title="All 2 branches missed.">        if (failbackManager != null) {</span>
<span class="nc" id="L200">            failbackManager.stop();</span>
        }
<span class="nc bnc" id="L202" title="All 2 branches missed.">        if (connectionPool != null) {</span>
<span class="nc" id="L203">            connectionPool.close();</span>
        }
<span class="nc" id="L205">        logger.info(&quot;SharedConnectionManager closed&quot;);</span>
<span class="nc" id="L206">    }</span>

    /**
     * Functional interface for blocking operations.
     */
    @FunctionalInterface
    public interface BlockingOperation&lt;T&gt; {
        T execute(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) throws Exception;
    }

    /**
     * Functional interface for async operations.
     */
    @FunctionalInterface
    public interface AsyncOperation&lt;T&gt; {
        ListenableFuture&lt;T&gt; execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }

    /**
     * Operation type enumeration.
     */
<span class="nc" id="L227">    public enum OperationType {</span>
<span class="nc" id="L228">        READ, WRITE</span>
    }

    /**
     * Custom failback manager for shared connection pool.
     */
    private static class SharedFailbackManager implements AutoCloseable {
<span class="nc" id="L235">        private static final Logger logger = LoggerFactory.getLogger(SharedFailbackManager.class);</span>

        private final RustyClusterClientConfig config;
        private final SharedConnectionPool connectionPool;
        private final List&lt;NodeConfig&gt; sortedNodes;
        private final AtomicReference&lt;NodeConfig&gt; currentNode;
        private final java.util.concurrent.ScheduledExecutorService scheduler;
<span class="nc" id="L242">        private volatile boolean running = false;</span>

        public SharedFailbackManager(RustyClusterClientConfig config,
                                   SharedConnectionPool connectionPool,
                                   List&lt;NodeConfig&gt; sortedNodes,
<span class="nc" id="L247">                                   AtomicReference&lt;NodeConfig&gt; currentNode) {</span>
<span class="nc" id="L248">            this.config = config;</span>
<span class="nc" id="L249">            this.connectionPool = connectionPool;</span>
<span class="nc" id="L250">            this.sortedNodes = sortedNodes;</span>
<span class="nc" id="L251">            this.currentNode = currentNode;</span>
<span class="nc" id="L252">            this.scheduler = java.util.concurrent.Executors.newSingleThreadScheduledExecutor(r -&gt; {</span>
<span class="nc" id="L253">                Thread t = new Thread(r, &quot;SharedFailbackManager&quot;);</span>
<span class="nc" id="L254">                t.setDaemon(true);</span>
<span class="nc" id="L255">                return t;</span>
            });
<span class="nc" id="L257">        }</span>

        public void start() {
<span class="nc bnc" id="L260" title="All 2 branches missed.">            if (!config.isEnableFailback()) {</span>
<span class="nc" id="L261">                logger.debug(&quot;Failback is disabled, not starting SharedFailbackManager&quot;);</span>
<span class="nc" id="L262">                return;</span>
            }

<span class="nc bnc" id="L265" title="All 2 branches missed.">            if (running) {</span>
<span class="nc" id="L266">                logger.warn(&quot;SharedFailbackManager is already running&quot;);</span>
<span class="nc" id="L267">                return;</span>
            }

<span class="nc" id="L270">            running = true;</span>
<span class="nc" id="L271">            scheduler.scheduleWithFixedDelay(</span>
                this::checkForFailback,
<span class="nc" id="L273">                config.getFailbackCheckIntervalMs(),</span>
<span class="nc" id="L274">                config.getFailbackCheckIntervalMs(),</span>
                TimeUnit.MILLISECONDS
            );

<span class="nc" id="L278">            logger.info(&quot;SharedFailbackManager started with check interval: {}ms&quot;, config.getFailbackCheckIntervalMs());</span>
<span class="nc" id="L279">        }</span>

        public void stop() {
<span class="nc" id="L282">            running = false;</span>
<span class="nc" id="L283">            scheduler.shutdown();</span>
            try {
<span class="nc bnc" id="L285" title="All 2 branches missed.">                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {</span>
<span class="nc" id="L286">                    scheduler.shutdownNow();</span>
                }
<span class="nc" id="L288">            } catch (InterruptedException e) {</span>
<span class="nc" id="L289">                scheduler.shutdownNow();</span>
<span class="nc" id="L290">                Thread.currentThread().interrupt();</span>
<span class="nc" id="L291">            }</span>
<span class="nc" id="L292">            logger.info(&quot;SharedFailbackManager stopped&quot;);</span>
<span class="nc" id="L293">        }</span>

        private void checkForFailback() {
<span class="nc bnc" id="L296" title="All 2 branches missed.">            if (!running) {</span>
<span class="nc" id="L297">                return;</span>
            }

            try {
<span class="nc" id="L301">                NodeConfig current = currentNode.get();</span>
<span class="nc bnc" id="L302" title="All 2 branches missed.">                if (current == null) {</span>
<span class="nc" id="L303">                    return;</span>
                }

                // Find the highest priority node that's available
<span class="nc" id="L307">                NodeConfig bestAvailableNode = findBestAvailableNode();</span>

<span class="nc bnc" id="L309" title="All 2 branches missed.">                if (bestAvailableNode != null &amp;&amp;</span>
<span class="nc bnc" id="L310" title="All 2 branches missed.">                    bestAvailableNode.role().getPriority() &lt; current.role().getPriority()) {</span>

<span class="nc" id="L312">                    logger.info(&quot;Failing back from {} (priority {}) to {} (priority {})&quot;,</span>
<span class="nc" id="L313">                        current, current.role().getPriority(),</span>
<span class="nc" id="L314">                        bestAvailableNode, bestAvailableNode.role().getPriority());</span>

<span class="nc" id="L316">                    currentNode.set(bestAvailableNode);</span>

                    // Clear authentication state when switching nodes during failback
<span class="nc bnc" id="L319" title="All 2 branches missed.">                    if (config.hasAuthentication()) {</span>
<span class="nc" id="L320">                        connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="nc" id="L321">                        logger.debug(&quot;Cleared authentication state for failback to: {}&quot;, bestAvailableNode);</span>
                    }
                }
<span class="nc" id="L324">            } catch (Exception e) {</span>
<span class="nc" id="L325">                logger.warn(&quot;Error during failback check: {}&quot;, e.getMessage());</span>
<span class="nc" id="L326">            }</span>
<span class="nc" id="L327">        }</span>

        private NodeConfig findBestAvailableNode() {
<span class="nc" id="L330">            NodeConfig current = currentNode.get();</span>
<span class="nc bnc" id="L331" title="All 2 branches missed.">            if (current == null) {</span>
<span class="nc" id="L332">                return null;</span>
            }

            // Only check nodes with higher priority (lower priority number) than current
<span class="nc bnc" id="L336" title="All 2 branches missed.">            for (NodeConfig node : sortedNodes) {</span>
<span class="nc bnc" id="L337" title="All 2 branches missed.">                if (node.role().getPriority() &lt; current.role().getPriority()) {</span>
<span class="nc bnc" id="L338" title="All 2 branches missed.">                    if (isNodeHealthy(node)) {</span>
<span class="nc" id="L339">                        return node;</span>
                    }
                } else {
                    break; // Nodes are sorted by priority
                }
<span class="nc" id="L344">            }</span>
<span class="nc" id="L345">            return null;</span>
        }

        private boolean isNodeHealthy(NodeConfig node) {
<span class="nc" id="L349">            try (SharedConnectionPool.SharedConnectionWrapper wrapper =</span>
<span class="nc" id="L350">                     connectionPool.borrowBlockingConnection(node)) {</span>

                // Perform a simple health check
                rustycluster.Rustycluster.GetRequest healthCheckRequest =
<span class="nc" id="L354">                    rustycluster.Rustycluster.GetRequest.newBuilder()</span>
<span class="nc" id="L355">                        .setKey(&quot;__health_check__&quot;)</span>
<span class="nc" id="L356">                        .build();</span>

<span class="nc" id="L358">                wrapper.getBlockingStub()</span>
<span class="nc" id="L359">                    .withDeadlineAfter(1000, TimeUnit.MILLISECONDS)</span>
<span class="nc" id="L360">                    .get(healthCheckRequest);</span>

<span class="nc" id="L362">                return true;</span>
<span class="nc" id="L363">            } catch (Exception e) {</span>
<span class="nc" id="L364">                logger.debug(&quot;Health check failed for node {}: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L365">                return false;</span>
            }
        }

        @Override
        public void close() {
<span class="nc" id="L371">            stop();</span>
<span class="nc" id="L372">        }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
package org.npci.rustyclient.client.connection;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.NodeRole;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

import rustycluster.KeyValueServiceGrpc;
import rustycluster.Rustycluster;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Test for FailbackManager functionality.
 */
@ExtendWith(MockitoExtension.class)
class FailbackManagerTest {

    @Mock
    private ConnectionPool connectionPool;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStub;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStubWithDeadline;

    private RustyClusterClientConfig config;
    private List<NodeConfig> sortedNodes;
    private AtomicReference<NodeConfig> currentNode;
    private FailbackManager failbackManager;

    @BeforeEach
    void setUp() {
        // Create test nodes
        NodeConfig primaryNode = new NodeConfig("primary", 50051, NodeRole.PRIMARY);
        NodeConfig secondaryNode = new NodeConfig("secondary", 50052, NodeRole.SECONDARY);
        NodeConfig tertiaryNode = new NodeConfig("tertiary", 50053, NodeRole.TERTIARY);
        
        sortedNodes = List.of(primaryNode, secondaryNode, tertiaryNode);
        currentNode = new AtomicReference<>(secondaryNode); // Start with secondary node

        config = RustyClusterClientConfig.builder()
                .addNode("primary", 50051, NodeRole.PRIMARY)
                .addNode("secondary", 50052, NodeRole.SECONDARY)
                .addNode("tertiary", 50053, NodeRole.TERTIARY)
                .enableFailback(true)
                .failbackCheckInterval(100, TimeUnit.MILLISECONDS) // Fast interval for testing
                .failbackHealthCheckRetries(1)
                .build();

        failbackManager = new FailbackManager(config, connectionPool, sortedNodes, currentNode);
    }

    @Test
    @DisplayName("Should failback to primary node when it becomes healthy")
    void shouldFailbackToPrimaryNodeWhenHealthy() throws Exception {
        // Given - currently on secondary node
        NodeConfig primaryNode = sortedNodes.get(0);
        NodeConfig secondaryNode = sortedNodes.get(1);
        
        assertThat(currentNode.get()).isEqualTo(secondaryNode);

        // Mock primary node as healthy
        when(connectionPool.borrowStub(primaryNode)).thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(eq(1000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);
        when(mockStubWithDeadline.get(any(Rustycluster.GetRequest.class)))
                .thenReturn(Rustycluster.GetResponse.newBuilder().build());

        // When - start failback manager
        failbackManager.start();
        
        // Wait for failback check to run
        Thread.sleep(200);

        // Then - should have failed back to primary
        assertThat(currentNode.get()).isEqualTo(primaryNode);
        
        verify(connectionPool).borrowStub(primaryNode);
        verify(connectionPool).returnStub(primaryNode, mockStub);
        
        failbackManager.stop();
    }

    @Test
    @DisplayName("Should not failback when primary node is unhealthy")
    void shouldNotFailbackWhenPrimaryNodeUnhealthy() throws Exception {
        // Given - currently on secondary node
        NodeConfig primaryNode = sortedNodes.get(0);
        NodeConfig secondaryNode = sortedNodes.get(1);
        
        assertThat(currentNode.get()).isEqualTo(secondaryNode);

        // Mock primary node as unhealthy
        when(connectionPool.borrowStub(primaryNode))
                .thenThrow(new RuntimeException("Connection failed"));

        // When - start failback manager
        failbackManager.start();
        
        // Wait for failback check to run
        Thread.sleep(200);

        // Then - should remain on secondary
        assertThat(currentNode.get()).isEqualTo(secondaryNode);

        // Should have attempted to check primary node (may be called multiple times due to scheduler)
        verify(connectionPool, atLeast(1)).borrowStub(primaryNode);
        
        failbackManager.stop();
    }

    @Test
    @DisplayName("Should not failback when already on highest priority node")
    void shouldNotFailbackWhenAlreadyOnHighestPriorityNode() throws Exception {
        // Given - currently on primary node (highest priority)
        NodeConfig primaryNode = sortedNodes.get(0);
        currentNode.set(primaryNode);

        try (FailbackManager testFailbackManager = new FailbackManager(config, connectionPool, sortedNodes, currentNode)) {
            // When - start failback manager
            testFailbackManager.start();

            // Wait for failback check to run
            Thread.sleep(200);

            // Then - should remain on primary
            assertThat(currentNode.get()).isEqualTo(primaryNode);

            // Should not perform any health checks since already on highest priority
            // The FailbackManager should not check any nodes when already on the highest priority node
            verify(connectionPool, never()).borrowStub(any());
        }
    }

    @Test
    @DisplayName("Should not start when failback is disabled")
    void shouldNotStartWhenFailbackDisabled() throws Exception {
        // Given - failback disabled config
        RustyClusterClientConfig disabledConfig = RustyClusterClientConfig.builder()
                .addNode("primary", 50051, NodeRole.PRIMARY)
                .addNode("secondary", 50052, NodeRole.SECONDARY)
                .enableFailback(false)
                .build();

        try (FailbackManager disabledFailbackManager = new FailbackManager(
            disabledConfig, connectionPool, sortedNodes, currentNode)) {

            // When - start failback manager
            disabledFailbackManager.start();

            // Then - should not perform any operations
            verify(connectionPool, never()).borrowStub(any());
        }
    }

    @Test
    @DisplayName("Should require multiple successful health checks")
    void shouldRequireMultipleSuccessfulHealthChecks() throws Exception {
        // Given - config with multiple health check retries
        RustyClusterClientConfig multiCheckConfig = RustyClusterClientConfig.builder()
                .addNode("primary", 50051, NodeRole.PRIMARY)
                .addNode("secondary", 50052, NodeRole.SECONDARY)
                .enableFailback(true)
                .failbackCheckInterval(100, TimeUnit.MILLISECONDS)
                .failbackHealthCheckRetries(3) // Require 3 successful checks
                .build();

        try (FailbackManager multiCheckFailbackManager = new FailbackManager(
            multiCheckConfig, connectionPool, sortedNodes, currentNode)) {

            NodeConfig primaryNode = sortedNodes.get(0);
            NodeConfig secondaryNode = sortedNodes.get(1);

            assertThat(currentNode.get()).isEqualTo(secondaryNode);

            // Mock primary node as healthy for all checks
            when(connectionPool.borrowStub(primaryNode)).thenReturn(mockStub);
            when(mockStub.withDeadlineAfter(eq(1000L), eq(TimeUnit.MILLISECONDS)))
                    .thenReturn(mockStubWithDeadline);
            when(mockStubWithDeadline.get(any(Rustycluster.GetRequest.class)))
                    .thenReturn(Rustycluster.GetResponse.newBuilder().build());

            // When - start failback manager
            multiCheckFailbackManager.start();

            // Wait for failback check to run
            Thread.sleep(500);

            // Then - should have failed back to primary after multiple successful checks
            assertThat(currentNode.get()).isEqualTo(primaryNode);

            // Should have performed at least 3 health checks (may be more due to background scheduler)
            verify(connectionPool, atLeast(3)).borrowStub(primaryNode);
            verify(connectionPool, atLeast(3)).returnStub(primaryNode, mockStub);
        }
    }
}

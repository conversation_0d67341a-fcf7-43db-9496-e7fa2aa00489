# Redis Client

A high-performance, production-ready Redis client for Java that provides both synchronous and asynchronous operations with automatic failover, connection pooling, and comprehensive configuration options.

## Features

- **Dual Operation Modes**: Both synchronous and asynchronous operations
- **Automatic Failover**: Seamless failover between primary, secondary, and tertiary nodes
- **Connection Pooling**: High-performance connection pooling with Apache Commons Pool2
- **Authentication Support**: Redis ACL and legacy password authentication
- **SSL/TLS Support**: Secure connections with custom certificate support
- **Cluster Mode**: Redis Cluster support with automatic redirections
- **Performance Presets**: Optimized configurations for different use cases
- **Comprehensive Monitoring**: JMX metrics and connection pool statistics
- **Production Ready**: Extensive error handling, logging, and resource management

## Quick Start

### Basic Usage

```java
import org.npci.rustyclient.redis.RedisClient;
import org.npci.rustyclient.redis.config.RedisClientConfig;

// Create configuration
RedisClientConfig config = RedisClientConfig.builder()
    .addPrimaryNode("localhost", 6379)
    .build();

// Use the client
try (RedisClient client = new RedisClient(config)) {
    // String operations
    client.set("key1", "value1");
    String value = client.get("key1");
    
    // Hash operations
    client.hSet("user:1001", "name", "John Doe");
    String name = client.hGet("user:1001", "name");
    
    // Async operations
    CompletableFuture<Boolean> setFuture = client.setAsync("key2", "value2");
    CompletableFuture<String> getFuture = client.getAsync("key2");
}
```

## Configuration

### Multi-Node Setup with Authentication

```java
RedisClientConfig config = RedisClientConfig.builder()
    // Add nodes with priorities
    .addPrimaryNode("redis-primary.example.com", 6379)
    .addSecondaryNode("redis-secondary.example.com", 6379)
    .addNode("redis-tertiary.example.com", 6379, RedisNodeRole.TERTIARY)
    
    // Authentication
    .authentication("redis-user", "redis-password")
    .database(1)
    
    // Connection settings
    .maxConnectionsPerNode(20)
    .connectionTimeout(5, TimeUnit.SECONDS)
    .readTimeout(3, TimeUnit.SECONDS)
    .writeTimeout(3, TimeUnit.SECONDS)
    
    // Retry settings
    .maxRetries(3)
    .retryDelay(500, TimeUnit.MILLISECONDS)
    
    // SSL/TLS
    .useSecureConnection("/path/to/certificate.pem")
    
    .build();
```

### Connection String Format

```java
RedisClientConfig config = RedisClientConfig.builder()
    .addNodes("localhost:6379", "localhost:6380", "localhost:6381")
    .build();
```

### Performance Presets

#### High Throughput
```java
RedisClientConfig config = RedisClientConfig.builder()
    .addNodes("node1:6379", "node2:6379", "node3:6379")
    .highThroughputPreset()
    .maxConnectionsPerNode(50)
    .build();
```

#### Low Latency
```java
RedisClientConfig config = RedisClientConfig.builder()
    .addPrimaryNode("localhost", 6379)
    .lowLatencyPreset()
    .build();
```

### Redis Cluster Mode

```java
RedisClientConfig config = RedisClientConfig.builder()
    .addNodes("cluster-node1:7000", "cluster-node2:7000", "cluster-node3:7000")
    .enableClusterMode()
    .maxRedirections(5)
    .enableReadFromReplicas()
    .build();
```

## Operations

### String Operations

```java
// Basic operations
client.set("key", "value");
String value = client.get("key");
boolean exists = client.exists("key");
client.delete("key");

// With expiration
client.setEx("session:123", "session_data", 3600); // 1 hour TTL
client.expire("existing_key", 1800); // 30 minutes TTL

// Conditional set
boolean wasSet = client.setNX("lock:resource", "locked"); // Only if not exists

// Numeric operations
long newValue = client.incr("counter");
long incrementedBy = client.incrBy("counter", 5);
long decremented = client.decrBy("counter", 2);
```

### Hash Operations

```java
// Single field operations
client.hSet("user:1001", "name", "John Doe");
String name = client.hGet("user:1001", "name");
boolean fieldExists = client.hExists("user:1001", "name");
client.hDel("user:1001", "name");

// Multiple fields
Map<String, String> userData = new HashMap<>();
userData.put("name", "John Doe");
userData.put("email", "<EMAIL>");
userData.put("age", "30");

client.hMSet("user:1001", userData);
Map<String, String> allData = client.hGetAll("user:1001");
```

### Asynchronous Operations

```java
// Async string operations
CompletableFuture<Boolean> setFuture = client.setAsync("key", "value");
CompletableFuture<String> getFuture = client.getAsync("key");

// Async hash operations
CompletableFuture<Boolean> hSetFuture = client.hSetAsync("hash", "field", "value");
CompletableFuture<String> hGetFuture = client.hGetAsync("hash", "field");

// Chaining operations
client.setAsync("key1", "value1")
    .thenCompose(result -> client.getAsync("key1"))
    .thenAccept(value -> System.out.println("Retrieved: " + value));

// Parallel operations
CompletableFuture<Boolean> op1 = client.setAsync("key1", "value1");
CompletableFuture<Boolean> op2 = client.setAsync("key2", "value2");
CompletableFuture<Boolean> op3 = client.setAsync("key3", "value3");

CompletableFuture.allOf(op1, op2, op3)
    .thenRun(() -> System.out.println("All operations completed"));
```

## Error Handling and Failover

The client automatically handles:

- **Connection failures**: Automatic retry with exponential backoff
- **Node failures**: Failover to secondary and tertiary nodes
- **Network timeouts**: Configurable read/write timeouts
- **Connection pool exhaustion**: Fail-fast behavior with quick timeouts

```java
try (RedisClient client = new RedisClient(config)) {
    // Operations will automatically failover on node failures
    client.set("key", "value");
} catch (NoAvailableRedisNodesException e) {
    // All configured nodes are unavailable
    logger.error("Redis cluster is completely unavailable", e);
}
```

## Monitoring and Diagnostics

### Health Checks

```java
// Check if any Redis node is available
boolean isHealthy = client.healthCheck();

// Simple connectivity test
boolean canConnect = client.ping();
```

### Connection Pool Statistics

```java
String stats = client.getPoolStats();
System.out.println(stats);
// Output:
// Redis Connection Pool Statistics:
// Node: localhost:6379, Active: 2, Idle: 8, Total: 10, Borrowed: 150, Returned: 148
```

### JMX Monitoring

Connection pools are automatically registered with JMX for monitoring:
- Pool name: `RedisClient-<node-address>`
- Metrics: Active connections, idle connections, borrowed/returned counts

## Best Practices

### Connection Management

```java
// Always use try-with-resources
try (RedisClient client = new RedisClient(config)) {
    // Perform operations
} // Client is automatically closed
```

### High-Throughput Applications

```java
RedisClientConfig config = RedisClientConfig.builder()
    .addNodes("node1:6379", "node2:6379")
    .highThroughputPreset()
    .maxConnectionsPerNode(50)  // Increase pool size
    .maxRetries(1)              // Reduce retries for faster failover
    .retryDelay(50, TimeUnit.MILLISECONDS)  // Quick retry
    .build();
```

### Low-Latency Applications

```java
RedisClientConfig config = RedisClientConfig.builder()
    .addPrimaryNode("localhost", 6379)
    .lowLatencyPreset()
    .maxConnectionsPerNode(5)   // Smaller pool for lower latency
    .maxRetries(0)              // No retries for minimum latency
    .build();
```

### Microservices Architecture

For microservices using only Redis (not RustyCluster):

```java
// Service-specific configuration
RedisClientConfig config = RedisClientConfig.builder()
    .addPrimaryNode("redis.service.local", 6379)
    .authentication("service-user", "service-password")
    .database(2)  // Service-specific database
    .maxConnectionsPerNode(10)
    .build();

@Component
public class CacheService {
    private final RedisClient redisClient;
    
    public CacheService() {
        this.redisClient = new RedisClient(config);
    }
    
    @PreDestroy
    public void cleanup() {
        redisClient.close();
    }
}
```

## Configuration Reference

| Setting | Default | Description |
|---------|---------|-------------|
| `maxConnectionsPerNode` | 20 | Maximum connections per Redis node |
| `connectionTimeoutMs` | 3000 | Connection timeout in milliseconds |
| `readTimeoutMs` | 2000 | Read timeout in milliseconds |
| `writeTimeoutMs` | 2000 | Write timeout in milliseconds |
| `maxRetries` | 2 | Maximum retry attempts |
| `retryDelayMs` | 100 | Delay between retries in milliseconds |
| `database` | 0 | Redis database number (0-15) |
| `useClusterMode` | false | Enable Redis Cluster mode |
| `maxRedirections` | 5 | Maximum cluster redirections |
| `enableReadFromReplicas` | true | Allow reads from replica nodes |

## Dependencies

The Redis client requires the following dependencies:

```xml
<dependency>
    <groupId>redis.clients</groupId>
    <artifactId>jedis</artifactId>
    <version>5.1.0</version>
</dependency>
<dependency>
    <groupId>io.lettuce</groupId>
    <artifactId>lettuce-core</artifactId>
    <version>6.3.0.RELEASE</version>
</dependency>
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-pool2</artifactId>
    <version>2.11.1</version>
</dependency>
```

## Examples

See the `examples/RedisClientExample.java` file for comprehensive usage examples including:
- Basic operations
- Multi-node setup with authentication
- High-throughput scenarios
- Redis cluster mode
- Asynchronous operations

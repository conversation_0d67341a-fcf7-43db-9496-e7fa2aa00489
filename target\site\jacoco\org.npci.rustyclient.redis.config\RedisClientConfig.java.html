<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RedisClientConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.redis.config</a> &gt; <span class="el_source">RedisClientConfig.java</span></div><h1>RedisClientConfig.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.redis.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Configuration for the Redis client.
 */
<span class="fc" id="L11">public class RedisClientConfig {</span>
<span class="fc" id="L12">    private List&lt;RedisNodeConfig&gt; nodes = new ArrayList&lt;&gt;();</span>
    // High-throughput optimized defaults
<span class="fc" id="L14">    private int maxConnectionsPerNode = 20;</span>
<span class="fc" id="L15">    private long connectionTimeoutMs = 3000;</span>
<span class="fc" id="L16">    private long readTimeoutMs = 2000;</span>
<span class="fc" id="L17">    private long writeTimeoutMs = 2000;</span>
<span class="fc" id="L18">    private int maxRetries = 2;</span>
<span class="fc" id="L19">    private long retryDelayMs = 100;</span>
<span class="fc" id="L20">    private boolean useSecureConnection = false;</span>
<span class="fc" id="L21">    private String tlsCertPath = null;</span>
<span class="fc" id="L22">    private String username = null;</span>
<span class="fc" id="L23">    private String password = null;</span>
<span class="fc" id="L24">    private int database = 0;</span>
    
    // Redis-specific configuration
<span class="fc" id="L27">    private boolean useClusterMode = false;</span>
<span class="fc" id="L28">    private int maxRedirections = 5;</span>
<span class="fc" id="L29">    private boolean enableReadFromReplicas = true;</span>
    
    // Failback configuration
<span class="fc" id="L32">    private boolean enableFailback = true;</span>
<span class="fc" id="L33">    private long failbackCheckIntervalMs = 30000;</span>
<span class="fc" id="L34">    private int failbackHealthCheckRetries = 2;</span>

    /**
     * Builder for RedisClientConfig.
     */
<span class="fc" id="L39">    public static class Builder {</span>
<span class="fc" id="L40">        private final RedisClientConfig config = new RedisClientConfig();</span>

        /**
         * Add a Redis node to the configuration with a specific role.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @param role The role of the node
         * @return The builder instance
         */
        public Builder addNode(String host, int port, RedisNodeRole role) {
<span class="fc" id="L51">            config.nodes.add(new RedisNodeConfig(host, port, role));</span>
<span class="fc" id="L52">            return this;</span>
        }

        /**
         * Add a primary Redis node.
         *
         * @param host The host address
         * @param port The port number
         * @return The builder instance
         */
        public Builder addPrimaryNode(String host, int port) {
<span class="fc" id="L63">            return addNode(host, port, RedisNodeRole.PRIMARY);</span>
        }

        /**
         * Add a secondary Redis node.
         *
         * @param host The host address
         * @param port The port number
         * @return The builder instance
         */
        public Builder addSecondaryNode(String host, int port) {
<span class="fc" id="L74">            return addNode(host, port, RedisNodeRole.SECONDARY);</span>
        }

        /**
         * Add nodes from connection strings.
         *
         * @param connectionStrings Connection strings in format &quot;host:port&quot;
         * @return The builder instance
         */
        public Builder addNodes(String... connectionStrings) {
<span class="fc bfc" id="L84" title="All 2 branches covered.">            for (String connectionString : connectionStrings) {</span>
<span class="fc" id="L85">                String[] parts = connectionString.split(&quot;:&quot;);</span>
<span class="pc bpc" id="L86" title="1 of 2 branches missed.">                if (parts.length != 2) {</span>
<span class="nc" id="L87">                    throw new IllegalArgumentException(&quot;Invalid connection string format: &quot; + connectionString);</span>
                }
                
<span class="fc" id="L90">                String host = parts[0];</span>
<span class="fc" id="L91">                int port = Integer.parseInt(parts[1]);</span>
                
                // Auto-assign roles based on order
<span class="fc bfc" id="L94" title="All 3 branches covered.">                RedisNodeRole role = switch (config.nodes.size()) {</span>
<span class="fc" id="L95">                    case 0 -&gt; RedisNodeRole.PRIMARY;</span>
<span class="fc" id="L96">                    case 1 -&gt; RedisNodeRole.SECONDARY;</span>
<span class="fc" id="L97">                    default -&gt; RedisNodeRole.TERTIARY;</span>
                };
                
<span class="fc" id="L100">                addNode(host, port, role);</span>
            }
<span class="fc" id="L102">            return this;</span>
        }

        /**
         * Set the maximum number of connections per node.
         *
         * @param maxConnections The maximum number of connections
         * @return The builder instance
         */
        public Builder maxConnectionsPerNode(int maxConnections) {
<span class="fc" id="L112">            config.maxConnectionsPerNode = maxConnections;</span>
<span class="fc" id="L113">            return this;</span>
        }

        /**
         * Set the connection timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder connectionTimeout(long timeout, TimeUnit unit) {
<span class="nc" id="L124">            config.connectionTimeoutMs = unit.toMillis(timeout);</span>
<span class="nc" id="L125">            return this;</span>
        }

        /**
         * Set the read timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder readTimeout(long timeout, TimeUnit unit) {
<span class="nc" id="L136">            config.readTimeoutMs = unit.toMillis(timeout);</span>
<span class="nc" id="L137">            return this;</span>
        }

        /**
         * Set the write timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder writeTimeout(long timeout, TimeUnit unit) {
<span class="nc" id="L148">            config.writeTimeoutMs = unit.toMillis(timeout);</span>
<span class="nc" id="L149">            return this;</span>
        }

        /**
         * Set the maximum number of retries.
         *
         * @param maxRetries The maximum number of retries
         * @return The builder instance
         */
        public Builder maxRetries(int maxRetries) {
<span class="nc" id="L159">            config.maxRetries = maxRetries;</span>
<span class="nc" id="L160">            return this;</span>
        }

        /**
         * Set the retry delay.
         *
         * @param delay The delay value
         * @param unit  The time unit
         * @return The builder instance
         */
        public Builder retryDelay(long delay, TimeUnit unit) {
<span class="nc" id="L171">            config.retryDelayMs = unit.toMillis(delay);</span>
<span class="nc" id="L172">            return this;</span>
        }

        /**
         * Enable secure connection with TLS.
         *
         * @param certPath Path to the TLS certificate (optional)
         * @return The builder instance
         */
        public Builder useSecureConnection(String certPath) {
<span class="nc" id="L182">            config.useSecureConnection = true;</span>
<span class="nc" id="L183">            config.tlsCertPath = certPath;</span>
<span class="nc" id="L184">            return this;</span>
        }

        /**
         * Enable secure connection with TLS.
         *
         * @return The builder instance
         */
        public Builder useSecureConnection() {
<span class="nc" id="L193">            return useSecureConnection(null);</span>
        }

        /**
         * Set authentication credentials.
         *
         * @param username The username for authentication
         * @param password The password for authentication
         * @return The builder instance
         */
        public Builder authentication(String username, String password) {
<span class="fc" id="L204">            config.username = username;</span>
<span class="fc" id="L205">            config.password = password;</span>
<span class="fc" id="L206">            return this;</span>
        }

        /**
         * Set Redis database number.
         *
         * @param database The database number (0-15)
         * @return The builder instance
         */
        public Builder database(int database) {
<span class="pc bpc" id="L216" title="2 of 4 branches missed.">            if (database &lt; 0 || database &gt; 15) {</span>
<span class="nc" id="L217">                throw new IllegalArgumentException(&quot;Database number must be between 0 and 15&quot;);</span>
            }
<span class="fc" id="L219">            config.database = database;</span>
<span class="fc" id="L220">            return this;</span>
        }

        /**
         * Enable Redis cluster mode.
         *
         * @return The builder instance
         */
        public Builder enableClusterMode() {
<span class="fc" id="L229">            config.useClusterMode = true;</span>
<span class="fc" id="L230">            return this;</span>
        }

        /**
         * Set maximum redirections for cluster mode.
         *
         * @param maxRedirections The maximum number of redirections
         * @return The builder instance
         */
        public Builder maxRedirections(int maxRedirections) {
<span class="fc" id="L240">            config.maxRedirections = maxRedirections;</span>
<span class="fc" id="L241">            return this;</span>
        }

        /**
         * Enable reading from replica nodes.
         *
         * @return The builder instance
         */
        public Builder enableReadFromReplicas() {
<span class="fc" id="L250">            config.enableReadFromReplicas = true;</span>
<span class="fc" id="L251">            return this;</span>
        }

        /**
         * Disable reading from replica nodes.
         *
         * @return The builder instance
         */
        public Builder disableReadFromReplicas() {
<span class="nc" id="L260">            config.enableReadFromReplicas = false;</span>
<span class="nc" id="L261">            return this;</span>
        }

        /**
         * Apply high-throughput optimized settings.
         *
         * @return The builder instance
         */
        public Builder highThroughputPreset() {
<span class="fc" id="L270">            config.maxConnectionsPerNode = 50;</span>
<span class="fc" id="L271">            config.connectionTimeoutMs = 1000;</span>
<span class="fc" id="L272">            config.readTimeoutMs = 1000;</span>
<span class="fc" id="L273">            config.writeTimeoutMs = 1000;</span>
<span class="fc" id="L274">            config.maxRetries = 1;</span>
<span class="fc" id="L275">            config.retryDelayMs = 50;</span>
<span class="fc" id="L276">            return this;</span>
        }

        /**
         * Apply low-latency optimized settings.
         *
         * @return The builder instance
         */
        public Builder lowLatencyPreset() {
<span class="fc" id="L285">            config.maxConnectionsPerNode = 10;</span>
<span class="fc" id="L286">            config.connectionTimeoutMs = 500;</span>
<span class="fc" id="L287">            config.readTimeoutMs = 500;</span>
<span class="fc" id="L288">            config.writeTimeoutMs = 500;</span>
<span class="fc" id="L289">            config.maxRetries = 0;</span>
<span class="fc" id="L290">            config.retryDelayMs = 0;</span>
<span class="fc" id="L291">            return this;</span>
        }

        /**
         * Build the configuration.
         *
         * @return The built configuration
         */
        public RedisClientConfig build() {
<span class="pc bpc" id="L300" title="1 of 2 branches missed.">            if (config.nodes.isEmpty()) {</span>
<span class="nc" id="L301">                throw new IllegalStateException(&quot;At least one Redis node must be configured&quot;);</span>
            }
<span class="fc" id="L303">            return config;</span>
        }
    }

    /**
     * Create a new builder instance.
     *
     * @return A new builder
     */
    public static Builder builder() {
<span class="fc" id="L313">        return new Builder();</span>
    }

    // Getters
    public List&lt;RedisNodeConfig&gt; getNodes() {
<span class="fc" id="L318">        return Collections.unmodifiableList(nodes);</span>
    }

    public int getMaxConnectionsPerNode() {
<span class="fc" id="L322">        return maxConnectionsPerNode;</span>
    }

    public long getConnectionTimeoutMs() {
<span class="fc" id="L326">        return connectionTimeoutMs;</span>
    }

    public long getReadTimeoutMs() {
<span class="nc" id="L330">        return readTimeoutMs;</span>
    }

    public long getWriteTimeoutMs() {
<span class="nc" id="L334">        return writeTimeoutMs;</span>
    }

    public int getMaxRetries() {
<span class="fc" id="L338">        return maxRetries;</span>
    }

    public long getRetryDelayMs() {
<span class="nc" id="L342">        return retryDelayMs;</span>
    }

    public boolean isUseSecureConnection() {
<span class="nc" id="L346">        return useSecureConnection;</span>
    }

    public String getTlsCertPath() {
<span class="nc" id="L350">        return tlsCertPath;</span>
    }

    public String getUsername() {
<span class="fc" id="L354">        return username;</span>
    }

    public String getPassword() {
<span class="fc" id="L358">        return password;</span>
    }

    public int getDatabase() {
<span class="fc" id="L362">        return database;</span>
    }

    public boolean isUseClusterMode() {
<span class="fc" id="L366">        return useClusterMode;</span>
    }

    public int getMaxRedirections() {
<span class="fc" id="L370">        return maxRedirections;</span>
    }

    public boolean isEnableReadFromReplicas() {
<span class="fc" id="L374">        return enableReadFromReplicas;</span>
    }

    public boolean isEnableFailback() {
<span class="nc" id="L378">        return enableFailback;</span>
    }

    public long getFailbackCheckIntervalMs() {
<span class="nc" id="L382">        return failbackCheckIntervalMs;</span>
    }

    public int getFailbackHealthCheckRetries() {
<span class="nc" id="L386">        return failbackHealthCheckRetries;</span>
    }

    /**
     * Check if authentication is configured.
     *
     * @return True if authentication is configured
     */
    public boolean hasAuthentication() {
<span class="pc bpc" id="L395" title="2 of 4 branches missed.">        return username != null &amp;&amp; password != null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
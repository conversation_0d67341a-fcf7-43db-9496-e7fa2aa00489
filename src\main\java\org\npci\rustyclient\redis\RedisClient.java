package org.npci.rustyclient.redis;

import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.connection.RedisConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Main client for interacting with Redis directly.
 * This client provides both synchronous and asynchronous operations.
 *
 * Synchronous methods: set(), get(), delete(), hSet(), etc.
 * Asynchronous methods: setAsync(), getAsync(), deleteAsync(), hSetAsync(), etc.
 *
 * Example usage:
 * <pre>
 * RedisClientConfig config = RedisClientConfig.builder()
 *     .addPrimaryNode("localhost", 6379)
 *     .authentication("username", "password")
 *     .database(0)
 *     .build();
 *
 * try (RedisClient client = new RedisClient(config)) {
 *     // Synchronous operations
 *     client.set("key1", "value1");
 *     String value = client.get("key1");
 *
 *     // Asynchronous operations
 *     CompletableFuture&lt;Boolean&gt; setFuture = client.setAsync("key2", "value2");
 *     CompletableFuture&lt;String&gt; getFuture = client.getAsync("key2");
 * }
 * </pre>
 */
public class RedisClient implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(RedisClient.class);

    private final RedisClientConfig config;
    private final RedisConnectionManager connectionManager;

    /**
     * Create a new RedisClient with the provided configuration.
     *
     * @param config The client configuration
     */
    public RedisClient(RedisClientConfig config) {
        this.config = config;
        this.connectionManager = new RedisConnectionManager(config);
        logger.info("RedisClient initialized with {} nodes", config.getNodes().size());
    }

    // ==================== STRING OPERATIONS ====================

    /**
     * Set a key-value pair.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
        logger.debug("Setting key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                String result = connection.set(key, value);
                return "OK".equals(result);
            });
        } catch (Exception e) {
            logger.error("Error setting key: {}", key, e);
            throw e;
        }
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key The key
     * @param value The value
     * @param ttlSeconds The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttlSeconds) {
        logger.debug("Setting key with expiry: {}, ttl: {}", key, ttlSeconds);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                String result = connection.setex(key, ttlSeconds, value);
                return "OK".equals(result);
            });
        } catch (Exception e) {
            logger.error("Error setting key with expiry: {}", key, e);
            throw e;
        }
    }

    /**
     * Set a key-value pair only if the key does not exist.
     *
     * @param key   The key
     * @param value The value
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value) {
        logger.debug("Setting key if not exists: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.setnx(key, value);
                return result != null && result == 1;
            });
        } catch (Exception e) {
            logger.error("Error setting key if not exists: {}", key, e);
            throw e;
        }
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found or if all nodes are unavailable
     */
    public String get(String key) {
        logger.debug("Getting key: {}", key);
        
        return connectionManager.executeWithFailoverSilent(connection -> 
            connection.get(key));
    }

    /**
     * Delete a key.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
        logger.debug("Deleting key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.del(key);
                return result != null && result > 0;
            });
        } catch (Exception e) {
            logger.error("Error deleting key: {}", key, e);
            throw e;
        }
    }

    /**
     * Check if a key exists.
     *
     * @param key The key to check
     * @return True if the key exists, false otherwise or if all nodes are unavailable
     */
    public boolean exists(String key) {
        logger.debug("Checking if key exists: {}", key);
        
        Boolean result = connectionManager.executeWithFailoverSilent(connection -> 
            connection.exists(key));
        
        return result != null && result;
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key The key
     * @param ttlSeconds The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean expire(String key, long ttlSeconds) {
        logger.debug("Setting expiry on key: {}, ttl: {}", key, ttlSeconds);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.expire(key, ttlSeconds);
                return result != null && result == 1;
            });
        } catch (Exception e) {
            logger.error("Error setting expiry on key: {}", key, e);
            throw e;
        }
    }

    /**
     * Increment a numeric value.
     *
     * @param key The key
     * @return The new value
     */
    public long incr(String key) {
        logger.debug("Incrementing key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.incr(key));
        } catch (Exception e) {
            logger.error("Error incrementing key: {}", key, e);
            throw e;
        }
    }

    /**
     * Increment a numeric value by a specific amount.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
        logger.debug("Incrementing key: {} by {}", key, value);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.incrBy(key, value));
        } catch (Exception e) {
            logger.error("Error incrementing key: {} by {}", key, value, e);
            throw e;
        }
    }

    /**
     * Decrement a numeric value.
     *
     * @param key The key
     * @return The new value
     */
    public long decr(String key) {
        logger.debug("Decrementing key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.decr(key));
        } catch (Exception e) {
            logger.error("Error decrementing key: {}", key, e);
            throw e;
        }
    }

    /**
     * Decrement a numeric value by a specific amount.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
        logger.debug("Decrementing key: {} by {}", key, value);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.decrBy(key, value));
        } catch (Exception e) {
            logger.error("Error decrementing key: {} by {}", key, value, e);
            throw e;
        }
    }

    // ==================== HASH OPERATIONS ====================

    /**
     * Set a field in a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
        logger.debug("Setting hash field: {}.{}", key, field);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.hset(key, field, value);
                return result != null && result >= 0;
            });
        } catch (Exception e) {
            logger.error("Error setting hash field: {}.{}", key, field, e);
            throw e;
        }
    }

    /**
     * Set multiple fields in a hash.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map<String, String> fields) {
        logger.debug("Setting multiple hash fields for key: {}, fields count: {}", key, fields.size());
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                String result = connection.hmset(key, fields);
                return "OK".equals(result);
            });
        } catch (Exception e) {
            logger.error("Error setting multiple hash fields for key: {}", key, e);
            throw e;
        }
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found or if all nodes are unavailable
     */
    public String hGet(String key, String field) {
        logger.debug("Getting hash field: {}.{}", key, field);
        
        return connectionManager.executeWithFailoverSilent(connection -> 
            connection.hget(key, field));
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map<String, String> hGetAll(String key) {
        logger.debug("Getting all hash fields for key: {}", key);
        
        return connectionManager.executeWithFailoverSilent(connection -> 
            connection.hgetAll(key));
    }

    /**
     * Check if a hash field exists.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if field exists, false otherwise or if all nodes are unavailable
     */
    public boolean hExists(String key, String field) {
        logger.debug("Checking if hash field exists: {}.{}", key, field);
        
        Boolean result = connectionManager.executeWithFailoverSilent(connection -> 
            connection.hexists(key, field));
        
        return result != null && result;
    }

    /**
     * Delete a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if successful, false otherwise
     */
    public boolean hDel(String key, String field) {
        logger.debug("Deleting hash field: {}.{}", key, field);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.hdel(key, field);
                return result != null && result > 0;
            });
        } catch (Exception e) {
            logger.error("Error deleting hash field: {}.{}", key, field, e);
            throw e;
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Ping the Redis server to check connectivity.
     *
     * @return True if ping successful, false otherwise
     */
    public boolean ping() {
        logger.debug("Pinging Redis server");
        
        try {
            String result = connectionManager.executeWithFailover(connection -> 
                connection.ping());
            return "PONG".equals(result);
        } catch (Exception e) {
            logger.warn("Ping failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Perform a health check on the Redis cluster.
     *
     * @return True if healthy, false otherwise
     */
    public boolean healthCheck() {
        logger.debug("Performing health check");
        return connectionManager.healthCheck();
    }

    /**
     * Get connection pool statistics.
     *
     * @return Pool statistics for all nodes
     */
    public String getPoolStats() {
        return connectionManager.getPoolStats();
    }

    // ==================== ASYNCHRONOUS OPERATIONS ====================

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setAsync(String key, String value) {
        return CompletableFuture.supplyAsync(() -> set(key, value));
    }

    /**
     * Set a key-value pair with expiration asynchronously.
     *
     * @param key        The key
     * @param value      The value
     * @param ttlSeconds The time-to-live in seconds
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setExAsync(String key, String value, long ttlSeconds) {
        return CompletableFuture.supplyAsync(() -> setEx(key, value, ttlSeconds));
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture<String> getAsync(String key) {
        return CompletableFuture.supplyAsync(() -> get(key));
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> deleteAsync(String key) {
        return CompletableFuture.supplyAsync(() -> delete(key));
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key to check
     * @return CompletableFuture that completes with true if the key exists, false otherwise
     */
    public CompletableFuture<Boolean> existsAsync(String key) {
        return CompletableFuture.supplyAsync(() -> exists(key));
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> hSetAsync(String key, String field, String value) {
        return CompletableFuture.supplyAsync(() -> hSet(key, field, value));
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found
     */
    public CompletableFuture<String> hGetAsync(String key, String field) {
        return CompletableFuture.supplyAsync(() -> hGet(key, field));
    }

    /**
     * Get all hash fields asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture<Map<String, String>> hGetAllAsync(String key) {
        return CompletableFuture.supplyAsync(() -> hGetAll(key));
    }

    /**
     * Ping the Redis server asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture<Boolean> pingAsync() {
        return CompletableFuture.supplyAsync(this::ping);
    }

    @Override
    public void close() {
        logger.info("Closing RedisClient");
        connectionManager.close();
        logger.info("RedisClient closed");
    }
}

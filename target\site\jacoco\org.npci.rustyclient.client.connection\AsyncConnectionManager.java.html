<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">AsyncConnectionManager.java</span></div><h1>AsyncConnectionManager.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import com.google.common.util.concurrent.ListenableFuture;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.exception.NoAvailableNodesException;
import rustycluster.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages asynchronous connections to RustyCluster nodes, handling prioritization and failover.
 */
public class AsyncConnectionManager implements AutoCloseable {
<span class="fc" id="L25">    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final Executor executor;
    private final AsyncFailbackManager failbackManager;

    /**
     * Create a new AsyncConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public AsyncConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="fc" id="L41">        this(config, new AsyncConnectionPool(config, authenticationManager));</span>
<span class="fc" id="L42">    }</span>

    /**
     * Create a new AsyncConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="fc" id="L50">    AsyncConnectionManager(RustyClusterClientConfig config, AsyncConnectionPool connectionPool) {</span>
<span class="fc" id="L51">        this.config = config;</span>
<span class="fc" id="L52">        this.connectionPool = connectionPool;</span>
<span class="fc" id="L53">        this.executor = ForkJoinPool.commonPool(); // Use common pool for async operations</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="fc" id="L56">        this.sortedNodes = config.getNodes().stream()</span>
<span class="pc" id="L57">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="fc" id="L58">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="fc" id="L61">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

        // For async operations, we'll use a synchronous FailbackManager with the async pool
        // The health checks will be performed synchronously but the failback manager itself
        // runs in a separate thread, so it won't block async operations
<span class="fc" id="L66">        this.failbackManager = new AsyncFailbackManager(config, connectionPool, sortedNodes, currentNode);</span>
<span class="fc" id="L67">        this.failbackManager.start();</span>

<span class="fc" id="L69">        logger.info(&quot;AsyncConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="fc" id="L70">    }</span>

    /**
     * Execute an operation asynchronously with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation) {
<span class="nc" id="L80">        return executeWithFailoverAsync(operation, OperationType.READ, 0, false);</span>
    }

    /**
     * Execute an operation asynchronously with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param &lt;T&gt;           The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType) {
<span class="nc" id="L92">        return executeWithFailoverAsync(operation, operationType, 0, false);</span>
    }

    /**
     * Execute a read operation with automatic failover that returns null on failure instead of throwing exceptions.
     * This method is designed for read operations where returning null is acceptable when all nodes are unavailable.
     *
     * @param operation The read operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture that completes with the result of the operation, or null if all nodes are unavailable
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsyncSilent(AsyncClientOperation&lt;T&gt; operation) {
<span class="nc" id="L104">        return executeWithFailoverAsync(operation, OperationType.READ, 0, true);</span>
    }

    private &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType, int retryCount, boolean silent) {
<span class="nc bnc" id="L108" title="All 2 branches missed.">        if (retryCount &gt; config.getMaxRetries()) {</span>
<span class="nc bnc" id="L109" title="All 2 branches missed.">            if (silent) {</span>
<span class="nc" id="L110">                logger.warn(&quot;Async read operation failed after {} retries, returning null&quot;, retryCount);</span>
<span class="nc" id="L111">                return CompletableFuture.completedFuture(null);</span>
            } else {
<span class="nc" id="L113">                return CompletableFuture.failedFuture(</span>
                    new NoAvailableNodesException(&quot;Operation failed after &quot; + retryCount + &quot; retries&quot;));
            }
        }

        // Determine timeout based on operation type
<span class="nc bnc" id="L119" title="All 4 branches missed.">        long timeoutMs = switch (operationType) {</span>
<span class="nc" id="L120">            case READ -&gt; config.getReadTimeoutMs();</span>
<span class="nc" id="L121">            case WRITE -&gt; config.getWriteTimeoutMs();</span>
<span class="nc" id="L122">            case AUTH -&gt; config.getConnectionTimeoutMs();</span>
        };

<span class="nc" id="L125">        NodeConfig node = currentNode.get();</span>

        // For async operations, we'll handle authentication differently
        // We'll check authentication state and clear it if needed, but won't pre-authenticate
        // Authentication will happen automatically when the operation fails with auth error

<span class="nc" id="L131">        return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L132">            .thenCompose(stub -&gt; {</span>
                try {
                    // Apply deadline per operation to avoid expired deadline issues
<span class="nc" id="L135">                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =</span>
<span class="nc" id="L136">                        stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
<span class="nc" id="L137">                    ListenableFuture&lt;T&gt; listenableFuture = operation.execute(stubWithDeadline);</span>
<span class="nc" id="L138">                    return toCompletableFuture(listenableFuture)</span>
<span class="nc" id="L139">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="nc" id="L140">                            connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L141">                        });</span>
<span class="nc" id="L142">                } catch (Exception e) {</span>
<span class="nc" id="L143">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L144">                    return CompletableFuture.failedFuture(e);</span>
                }
            })
<span class="nc" id="L147">            .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L148">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, throwable.getMessage());</span>

                // Check if this is an authentication error and try to re-authenticate
<span class="nc bnc" id="L151" title="All 6 branches missed.">                if (isAuthenticationError(throwable) &amp;&amp; operationType != OperationType.AUTH &amp;&amp; config.hasAuthentication()) {</span>
<span class="nc" id="L152">                    logger.info(&quot;Authentication error detected, clearing auth state and will retry&quot;);</span>
<span class="nc" id="L153">                    connectionPool.getAuthenticationManager().clearAuthentication();</span>
                }

                // Try to find the next available node
<span class="nc" id="L157">                var nextNode = findNextAvailableNode(node);</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">                if (nextNode != null) {</span>
<span class="nc" id="L159">                    currentNode.set(nextNode);</span>
<span class="nc" id="L160">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>

                    // Clear authentication state when switching nodes
                    // This will force re-authentication on the next operation
<span class="nc bnc" id="L164" title="All 2 branches missed.">                    if (config.hasAuthentication()) {</span>
<span class="nc" id="L165">                        connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="nc" id="L166">                        logger.debug(&quot;Cleared authentication state for node switch to: {}&quot;, nextNode);</span>
                    }
                }

<span class="nc" id="L170">                throw new RuntimeException(throwable);</span>
            })
<span class="nc" id="L172">            .handle((result, throwable) -&gt; {</span>
<span class="nc bnc" id="L173" title="All 2 branches missed.">                if (throwable != null) {</span>
                    // Retry with delay
<span class="nc" id="L175">                    CompletableFuture&lt;Void&gt; delay = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L176">                    CompletableFuture.delayedExecutor(config.getRetryDelayMs(),</span>
                            java.util.concurrent.TimeUnit.MILLISECONDS, executor)
<span class="nc" id="L178">                        .execute(() -&gt; delay.complete(null));</span>
<span class="nc" id="L179">                    return delay.thenCompose(v -&gt; executeWithFailoverAsync(operation, operationType, retryCount + 1, silent));</span>
                }
<span class="nc" id="L181">                return CompletableFuture.completedFuture(result);</span>
            })
<span class="nc bnc" id="L183" title="All 2 branches missed.">            .thenCompose(future -&gt; future instanceof CompletableFuture ?</span>
<span class="nc" id="L184">                (CompletableFuture&lt;T&gt;) future : CompletableFuture.completedFuture((T) future));</span>
    }

    /**
     * Convert ListenableFuture to CompletableFuture.
     */
    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(ListenableFuture&lt;T&gt; listenableFuture) {
<span class="nc" id="L191">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>

<span class="nc" id="L193">        listenableFuture.addListener(() -&gt; {</span>
            try {
<span class="nc" id="L195">                completableFuture.complete(listenableFuture.get());</span>
<span class="nc" id="L196">            } catch (Exception e) {</span>
<span class="nc" id="L197">                completableFuture.completeExceptionally(e);</span>
<span class="nc" id="L198">            }</span>
<span class="nc" id="L199">        }, executor);</span>

<span class="nc" id="L201">        return completableFuture;</span>
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="nc" id="L212">        var samePriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L213" title="All 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="nc" id="L214">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L215">                .findFirst();</span>

<span class="nc bnc" id="L217" title="All 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L218">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="nc" id="L222">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L223" title="All 2 branches missed.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="nc" id="L224">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L225">                .findFirst();</span>

<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="nc" id="L228">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="nc" id="L232">        return sortedNodes.stream()</span>
<span class="nc bnc" id="L233" title="All 2 branches missed.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="nc" id="L234">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L235">                .findFirst()</span>
<span class="nc" id="L236">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
        try {
            // For async operations, we'll do a simple check
            // In a real implementation, you might want to perform an async health check
<span class="nc" id="L249">            return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L250">                .thenApply(stub -&gt; {</span>
<span class="nc" id="L251">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L252">                    return true;</span>
                })
<span class="nc" id="L254">                .exceptionally(e -&gt; {</span>
<span class="nc" id="L255">                    logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L256">                    return false;</span>
                })
<span class="nc" id="L258">                .join(); // This is not ideal for async, but needed for this interface</span>
<span class="nc" id="L259">        } catch (Exception e) {</span>
<span class="nc" id="L260">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L261">            return false;</span>
        }
    }

    /**
     * Check if an exception indicates an authentication error.
     *
     * @param throwable The throwable to check
     * @return True if this is an authentication error, false otherwise
     */
    private boolean isAuthenticationError(Throwable throwable) {
<span class="nc bnc" id="L272" title="All 2 branches missed.">        if (throwable == null) {</span>
<span class="nc" id="L273">            return false;</span>
        }

<span class="nc" id="L276">        String message = throwable.getMessage();</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">        if (message == null) {</span>
<span class="nc" id="L278">            return false;</span>
        }

        // Check for common authentication error patterns
<span class="nc" id="L282">        String lowerMessage = message.toLowerCase();</span>
<span class="nc bnc" id="L283" title="All 2 branches missed.">        return lowerMessage.contains(&quot;unauthenticated&quot;) ||</span>
<span class="nc bnc" id="L284" title="All 2 branches missed.">               lowerMessage.contains(&quot;authentication failed&quot;) ||</span>
<span class="nc bnc" id="L285" title="All 2 branches missed.">               lowerMessage.contains(&quot;invalid token&quot;) ||</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">               lowerMessage.contains(&quot;unauthorized&quot;) ||</span>
<span class="nc bnc" id="L287" title="All 4 branches missed.">               lowerMessage.contains(&quot;permission denied&quot;) ||</span>
               (throwable instanceof io.grpc.StatusRuntimeException &amp;&amp;
<span class="nc bnc" id="L289" title="All 2 branches missed.">                ((io.grpc.StatusRuntimeException) throwable).getStatus().getCode() == io.grpc.Status.Code.UNAUTHENTICATED);</span>
    }

    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L297">        failbackManager.close();</span>
<span class="fc" id="L298">        connectionPool.close();</span>
<span class="fc" id="L299">        logger.info(&quot;AsyncConnectionManager closed&quot;);</span>
<span class="fc" id="L300">    }</span>

    /**
     * Functional interface for async client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface AsyncClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return ListenableFuture with the result of the operation
         * @throws Exception If the operation fails
         */
        ListenableFuture&lt;T&gt; execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
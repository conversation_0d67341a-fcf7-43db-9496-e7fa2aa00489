<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Rustycluster.HIncrByFloatRequest</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">rustycluster</a> &gt; <span class="el_class">Rustycluster.HIncrByFloatRequest</span></div><h1>Rustycluster.HIncrByFloatRequest</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">565 of 565</td><td class="ctr2">0%</td><td class="bar">54 of 54</td><td class="ctr2">0%</td><td class="ctr1">66</td><td class="ctr2">66</td><td class="ctr1">152</td><td class="ctr2">152</td><td class="ctr1">39</td><td class="ctr2">39</td></tr></tfoot><tbody><tr><td id="a13"><a href="Rustycluster.java.html#L18726" class="el_method">hashCode()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="99" alt="99"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h0">20</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="Rustycluster.java.html#L18672" class="el_method">getSerializedSize()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="92" height="10" title="76" alt="76"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h1">19</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="Rustycluster.java.html#L18701" class="el_method">equals(Object)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="65" alt="65"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h2">18</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a38"><a href="Rustycluster.java.html#L18652" class="el_method">writeTo(CodedOutputStream)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="50" alt="50"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h3">12</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a34"><a href="Rustycluster.java.html#L18502" class="el_method">Rustycluster.HIncrByFloatRequest()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="27" alt="27"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h4">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a35"><a href="Rustycluster.java.html#L18500" class="el_method">Rustycluster.HIncrByFloatRequest(GeneratedMessageV3.Builder)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="22" alt="22"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="Rustycluster.java.html#L18536" class="el_method">getKey()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="20" alt="20"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="Rustycluster.java.html#L18575" class="el_method">getField()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="20" alt="20"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="Rustycluster.java.html#L18554" class="el_method">getKeyBytes()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="18" alt="18"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="Rustycluster.java.html#L18593" class="el_method">getFieldBytes()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="18" alt="18"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h9">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a15"><a href="Rustycluster.java.html#L18641" class="el_method">isInitialized()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="17" alt="17"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h10">5</td><td class="ctr2" id="i10">5</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a37"><a href="Rustycluster.java.html#L18831" class="el_method">toBuilder()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="13" alt="13"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a36"><a href="Rustycluster.java.html#L19319" class="el_method">static {...}</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a19"><a href="Rustycluster.java.html#L18838" class="el_method">newBuilderForType(GeneratedMessageV3.BuilderParent)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="7" alt="7"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a26"><a href="Rustycluster.java.html#L18758" class="el_method">parseFrom(ByteBuffer, ExtensionRegistryLite)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a28"><a href="Rustycluster.java.html#L18769" class="el_method">parseFrom(ByteString, ExtensionRegistryLite)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a24"><a href="Rustycluster.java.html#L18779" class="el_method">parseFrom(byte[], ExtensionRegistryLite)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a32"><a href="Rustycluster.java.html#L18790" class="el_method">parseFrom(InputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a22"><a href="Rustycluster.java.html#L18804" class="el_method">parseDelimitedFrom(InputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a30"><a href="Rustycluster.java.html#L18817" class="el_method">parseFrom(CodedInputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h16">2</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a14"><a href="Rustycluster.java.html#L18522" class="el_method">internalGetFieldAccessorTable()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h17">2</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a25"><a href="Rustycluster.java.html#L18752" class="el_method">parseFrom(ByteBuffer)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a27"><a href="Rustycluster.java.html#L18763" class="el_method">parseFrom(ByteString)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a23"><a href="Rustycluster.java.html#L18773" class="el_method">parseFrom(byte[])</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a31"><a href="Rustycluster.java.html#L18783" class="el_method">parseFrom(InputStream)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h18">2</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a21"><a href="Rustycluster.java.html#L18796" class="el_method">parseDelimitedFrom(InputStream)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h19">2</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a29"><a href="Rustycluster.java.html#L18810" class="el_method">parseFrom(CodedInputStream)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h20">2</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a17"><a href="Rustycluster.java.html#L18827" class="el_method">newBuilder(Rustycluster.HIncrByFloatRequest)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a20"><a href="Rustycluster.java.html#L18511" class="el_method">newInstance(GeneratedMessageV3.UnusedPrivateParameter)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a12"><a href="Rustycluster.java.html#L18613" class="el_method">getValue()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a10"><a href="Rustycluster.java.html#L18624" class="el_method">getSkipReplication()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a11"><a href="Rustycluster.java.html#L18635" class="el_method">getSkipSiteReplication()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a16"><a href="Rustycluster.java.html#L18824" class="el_method">newBuilder()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a3"><a href="Rustycluster.java.html#L18516" class="el_method">getDescriptor()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a18"><a href="Rustycluster.java.html#L18822" class="el_method">newBuilderForType()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a1"><a href="Rustycluster.java.html#L19323" class="el_method">getDefaultInstance()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a33"><a href="Rustycluster.java.html#L19349" class="el_method">parser()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a8"><a href="Rustycluster.java.html#L19354" class="el_method">getParserForType()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a2"><a href="Rustycluster.java.html#L19359" class="el_method">getDefaultInstanceForType()</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
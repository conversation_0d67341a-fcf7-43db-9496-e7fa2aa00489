<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.client.config</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.client.config</span></div><h1>org.npci.rustyclient.client.config</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">16 of 523</td><td class="ctr2">96%</td><td class="bar">4 of 22</td><td class="ctr2">81%</td><td class="ctr1">4</td><td class="ctr2">58</td><td class="ctr1">4</td><td class="ctr2">131</td><td class="ctr1">0</td><td class="ctr2">47</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a3"><a href="RustyClusterClientConfig$Builder.html" class="el_class">RustyClusterClientConfig.Builder</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="114" height="10" title="350" alt="350"/></td><td class="ctr2" id="c3">95%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="15" alt="15"/></td><td class="ctr2" id="e0">83%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">34</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i0">90</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">25</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="RustyClusterClientConfig.html" class="el_class">RustyClusterClientConfig</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="104" alt="104"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">19</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">31</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">17</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="NodeRole.html" class="el_class">NodeRole</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="35" alt="35"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">3</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="NodeConfig.html" class="el_class">NodeConfig</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="18" alt="18"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">2</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
package org.npci.rustyclient.redis;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeRole;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

/**
 * Unit tests for RedisClient.
 * Note: These tests require a running Redis server on localhost:6379
 */
class RedisClientTest {

    private RedisClientConfig config;

    @BeforeEach
    void setUp() {
        config = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .maxConnectionsPerNode(5)
                .build();
    }

    @Test
    @DisplayName("Should create RedisClient without errors")
    void shouldCreateRedisClientWithoutErrors() {
        assertDoesNotThrow(() -> {
            try (RedisClient client = new RedisClient(config)) {
                assertThat(client).isNotNull();
            }
        });
    }

    @Test
    @DisplayName("Should build config with multiple nodes")
    void shouldBuildConfigWithMultipleNodes() {
        RedisClientConfig multiNodeConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .addSecondaryNode("localhost", 6380)
                .addNode("localhost", 6381, RedisNodeRole.TERTIARY)
                .build();

        assertThat(multiNodeConfig.getNodes()).hasSize(3);
        assertThat(multiNodeConfig.getNodes().get(0).role()).isEqualTo(RedisNodeRole.PRIMARY);
        assertThat(multiNodeConfig.getNodes().get(1).role()).isEqualTo(RedisNodeRole.SECONDARY);
        assertThat(multiNodeConfig.getNodes().get(2).role()).isEqualTo(RedisNodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should build config with connection strings")
    void shouldBuildConfigWithConnectionStrings() {
        RedisClientConfig stringConfig = RedisClientConfig.builder()
                .addNodes("localhost:6379", "localhost:6380", "localhost:6381")
                .build();

        assertThat(stringConfig.getNodes()).hasSize(3);
        assertThat(stringConfig.getNodes().get(0).role()).isEqualTo(RedisNodeRole.PRIMARY);
        assertThat(stringConfig.getNodes().get(1).role()).isEqualTo(RedisNodeRole.SECONDARY);
        assertThat(stringConfig.getNodes().get(2).role()).isEqualTo(RedisNodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should build config with authentication")
    void shouldBuildConfigWithAuthentication() {
        RedisClientConfig authConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .authentication("testuser", "testpass")
                .database(1)
                .build();

        assertThat(authConfig.hasAuthentication()).isTrue();
        assertThat(authConfig.getUsername()).isEqualTo("testuser");
        assertThat(authConfig.getPassword()).isEqualTo("testpass");
        assertThat(authConfig.getDatabase()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should build config with high throughput preset")
    void shouldBuildConfigWithHighThroughputPreset() {
        RedisClientConfig htConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .highThroughputPreset()
                .build();

        assertThat(htConfig.getMaxConnectionsPerNode()).isEqualTo(50);
        assertThat(htConfig.getConnectionTimeoutMs()).isEqualTo(1000);
        assertThat(htConfig.getMaxRetries()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should build config with low latency preset")
    void shouldBuildConfigWithLowLatencyPreset() {
        RedisClientConfig llConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .lowLatencyPreset()
                .build();

        assertThat(llConfig.getMaxConnectionsPerNode()).isEqualTo(10);
        assertThat(llConfig.getConnectionTimeoutMs()).isEqualTo(500);
        assertThat(llConfig.getMaxRetries()).isEqualTo(0);
    }

    @Test
    @DisplayName("Should build config with cluster mode")
    void shouldBuildConfigWithClusterMode() {
        RedisClientConfig clusterConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 7000)
                .enableClusterMode()
                .maxRedirections(10)
                .enableReadFromReplicas()
                .build();

        assertThat(clusterConfig.isUseClusterMode()).isTrue();
        assertThat(clusterConfig.getMaxRedirections()).isEqualTo(10);
        assertThat(clusterConfig.isEnableReadFromReplicas()).isTrue();
    }

    // Integration tests - these would require a running Redis server
    // Commented out to avoid test failures in CI/CD without Redis

    /*
    @Test
    @DisplayName("Should perform basic string operations")
    void shouldPerformBasicStringOperations() {
        try (RedisClient client = new RedisClient(config)) {
            // Test set and get
            boolean setResult = client.set("test:key1", "value1");
            assertThat(setResult).isTrue();

            String getValue = client.get("test:key1");
            assertThat(getValue).isEqualTo("value1");

            // Test exists
            boolean exists = client.exists("test:key1");
            assertThat(exists).isTrue();

            // Test delete
            boolean deleteResult = client.delete("test:key1");
            assertThat(deleteResult).isTrue();

            // Verify deletion
            String deletedValue = client.get("test:key1");
            assertThat(deletedValue).isNull();
        }
    }

    @Test
    @DisplayName("Should perform hash operations")
    void shouldPerformHashOperations() {
        try (RedisClient client = new RedisClient(config)) {
            // Test hSet and hGet
            boolean hSetResult = client.hSet("test:hash1", "field1", "value1");
            assertThat(hSetResult).isTrue();

            String hGetValue = client.hGet("test:hash1", "field1");
            assertThat(hGetValue).isEqualTo("value1");

            // Test hMSet
            Map<String, String> fields = new HashMap<>();
            fields.put("field2", "value2");
            fields.put("field3", "value3");
            
            boolean hMSetResult = client.hMSet("test:hash1", fields);
            assertThat(hMSetResult).isTrue();

            // Test hGetAll
            Map<String, String> allFields = client.hGetAll("test:hash1");
            assertThat(allFields).hasSize(3);
            assertThat(allFields.get("field1")).isEqualTo("value1");
            assertThat(allFields.get("field2")).isEqualTo("value2");
            assertThat(allFields.get("field3")).isEqualTo("value3");

            // Test hExists
            boolean fieldExists = client.hExists("test:hash1", "field1");
            assertThat(fieldExists).isTrue();

            // Cleanup
            client.delete("test:hash1");
        }
    }

    @Test
    @DisplayName("Should perform async operations")
    void shouldPerformAsyncOperations() throws Exception {
        try (RedisClient client = new RedisClient(config)) {
            // Test async set
            CompletableFuture<Boolean> setFuture = client.setAsync("test:async1", "asyncvalue1");
            Boolean setResult = setFuture.get();
            assertThat(setResult).isTrue();

            // Test async get
            CompletableFuture<String> getFuture = client.getAsync("test:async1");
            String getValue = getFuture.get();
            assertThat(getValue).isEqualTo("asyncvalue1");

            // Test async delete
            CompletableFuture<Boolean> deleteFuture = client.deleteAsync("test:async1");
            Boolean deleteResult = deleteFuture.get();
            assertThat(deleteResult).isTrue();
        }
    }

    @Test
    @DisplayName("Should handle ping operation")
    void shouldHandlePingOperation() {
        try (RedisClient client = new RedisClient(config)) {
            boolean pingResult = client.ping();
            assertThat(pingResult).isTrue();
        }
    }
    */
}

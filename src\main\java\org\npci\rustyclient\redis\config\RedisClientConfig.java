package org.npci.rustyclient.redis.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Configuration for the Redis client.
 */
public class RedisClientConfig {
    private List<RedisNodeConfig> nodes = new ArrayList<>();
    // High-throughput optimized defaults
    private int maxConnectionsPerNode = 20;
    private long connectionTimeoutMs = 3000;
    private long readTimeoutMs = 2000;
    private long writeTimeoutMs = 2000;
    private int maxRetries = 2;
    private long retryDelayMs = 100;
    private boolean useSecureConnection = false;
    private String tlsCertPath = null;
    private String username = null;
    private String password = null;
    private int database = 0;
    
    // Redis-specific configuration
    private boolean useClusterMode = false;
    private int maxRedirections = 5;
    private boolean enableReadFromReplicas = true;
    
    // Failback configuration
    private boolean enableFailback = true;
    private long failbackCheckIntervalMs = 30000;
    private int failbackHealthCheckRetries = 2;

    /**
     * Builder for RedisClientConfig.
     */
    public static class Builder {
        private final RedisClientConfig config = new RedisClientConfig();

        /**
         * Add a Redis node to the configuration with a specific role.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @param role The role of the node
         * @return The builder instance
         */
        public Builder addNode(String host, int port, RedisNodeRole role) {
            config.nodes.add(new RedisNodeConfig(host, port, role));
            return this;
        }

        /**
         * Add a primary Redis node.
         *
         * @param host The host address
         * @param port The port number
         * @return The builder instance
         */
        public Builder addPrimaryNode(String host, int port) {
            return addNode(host, port, RedisNodeRole.PRIMARY);
        }

        /**
         * Add a secondary Redis node.
         *
         * @param host The host address
         * @param port The port number
         * @return The builder instance
         */
        public Builder addSecondaryNode(String host, int port) {
            return addNode(host, port, RedisNodeRole.SECONDARY);
        }

        /**
         * Add nodes from connection strings.
         *
         * @param connectionStrings Connection strings in format "host:port"
         * @return The builder instance
         */
        public Builder addNodes(String... connectionStrings) {
            for (String connectionString : connectionStrings) {
                String[] parts = connectionString.split(":");
                if (parts.length != 2) {
                    throw new IllegalArgumentException("Invalid connection string format: " + connectionString);
                }
                
                String host = parts[0];
                int port = Integer.parseInt(parts[1]);
                
                // Auto-assign roles based on order
                RedisNodeRole role = switch (config.nodes.size()) {
                    case 0 -> RedisNodeRole.PRIMARY;
                    case 1 -> RedisNodeRole.SECONDARY;
                    default -> RedisNodeRole.TERTIARY;
                };
                
                addNode(host, port, role);
            }
            return this;
        }

        /**
         * Set the maximum number of connections per node.
         *
         * @param maxConnections The maximum number of connections
         * @return The builder instance
         */
        public Builder maxConnectionsPerNode(int maxConnections) {
            config.maxConnectionsPerNode = maxConnections;
            return this;
        }

        /**
         * Set the connection timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder connectionTimeout(long timeout, TimeUnit unit) {
            config.connectionTimeoutMs = unit.toMillis(timeout);
            return this;
        }

        /**
         * Set the read timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder readTimeout(long timeout, TimeUnit unit) {
            config.readTimeoutMs = unit.toMillis(timeout);
            return this;
        }

        /**
         * Set the write timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder writeTimeout(long timeout, TimeUnit unit) {
            config.writeTimeoutMs = unit.toMillis(timeout);
            return this;
        }

        /**
         * Set the maximum number of retries.
         *
         * @param maxRetries The maximum number of retries
         * @return The builder instance
         */
        public Builder maxRetries(int maxRetries) {
            config.maxRetries = maxRetries;
            return this;
        }

        /**
         * Set the retry delay.
         *
         * @param delay The delay value
         * @param unit  The time unit
         * @return The builder instance
         */
        public Builder retryDelay(long delay, TimeUnit unit) {
            config.retryDelayMs = unit.toMillis(delay);
            return this;
        }

        /**
         * Enable secure connection with TLS.
         *
         * @param certPath Path to the TLS certificate (optional)
         * @return The builder instance
         */
        public Builder useSecureConnection(String certPath) {
            config.useSecureConnection = true;
            config.tlsCertPath = certPath;
            return this;
        }

        /**
         * Enable secure connection with TLS.
         *
         * @return The builder instance
         */
        public Builder useSecureConnection() {
            return useSecureConnection(null);
        }

        /**
         * Set authentication credentials.
         *
         * @param username The username for authentication
         * @param password The password for authentication
         * @return The builder instance
         */
        public Builder authentication(String username, String password) {
            config.username = username;
            config.password = password;
            return this;
        }

        /**
         * Set Redis database number.
         *
         * @param database The database number (0-15)
         * @return The builder instance
         */
        public Builder database(int database) {
            if (database < 0 || database > 15) {
                throw new IllegalArgumentException("Database number must be between 0 and 15");
            }
            config.database = database;
            return this;
        }

        /**
         * Enable Redis cluster mode.
         *
         * @return The builder instance
         */
        public Builder enableClusterMode() {
            config.useClusterMode = true;
            return this;
        }

        /**
         * Set maximum redirections for cluster mode.
         *
         * @param maxRedirections The maximum number of redirections
         * @return The builder instance
         */
        public Builder maxRedirections(int maxRedirections) {
            config.maxRedirections = maxRedirections;
            return this;
        }

        /**
         * Enable reading from replica nodes.
         *
         * @return The builder instance
         */
        public Builder enableReadFromReplicas() {
            config.enableReadFromReplicas = true;
            return this;
        }

        /**
         * Disable reading from replica nodes.
         *
         * @return The builder instance
         */
        public Builder disableReadFromReplicas() {
            config.enableReadFromReplicas = false;
            return this;
        }

        /**
         * Apply high-throughput optimized settings.
         *
         * @return The builder instance
         */
        public Builder highThroughputPreset() {
            config.maxConnectionsPerNode = 50;
            config.connectionTimeoutMs = 1000;
            config.readTimeoutMs = 1000;
            config.writeTimeoutMs = 1000;
            config.maxRetries = 1;
            config.retryDelayMs = 50;
            return this;
        }

        /**
         * Apply low-latency optimized settings.
         *
         * @return The builder instance
         */
        public Builder lowLatencyPreset() {
            config.maxConnectionsPerNode = 10;
            config.connectionTimeoutMs = 500;
            config.readTimeoutMs = 500;
            config.writeTimeoutMs = 500;
            config.maxRetries = 0;
            config.retryDelayMs = 0;
            return this;
        }

        /**
         * Build the configuration.
         *
         * @return The built configuration
         */
        public RedisClientConfig build() {
            if (config.nodes.isEmpty()) {
                throw new IllegalStateException("At least one Redis node must be configured");
            }
            return config;
        }
    }

    /**
     * Create a new builder instance.
     *
     * @return A new builder
     */
    public static Builder builder() {
        return new Builder();
    }

    // Getters
    public List<RedisNodeConfig> getNodes() {
        return Collections.unmodifiableList(nodes);
    }

    public int getMaxConnectionsPerNode() {
        return maxConnectionsPerNode;
    }

    public long getConnectionTimeoutMs() {
        return connectionTimeoutMs;
    }

    public long getReadTimeoutMs() {
        return readTimeoutMs;
    }

    public long getWriteTimeoutMs() {
        return writeTimeoutMs;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public long getRetryDelayMs() {
        return retryDelayMs;
    }

    public boolean isUseSecureConnection() {
        return useSecureConnection;
    }

    public String getTlsCertPath() {
        return tlsCertPath;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public int getDatabase() {
        return database;
    }

    public boolean isUseClusterMode() {
        return useClusterMode;
    }

    public int getMaxRedirections() {
        return maxRedirections;
    }

    public boolean isEnableReadFromReplicas() {
        return enableReadFromReplicas;
    }

    public boolean isEnableFailback() {
        return enableFailback;
    }

    public long getFailbackCheckIntervalMs() {
        return failbackCheckIntervalMs;
    }

    public int getFailbackHealthCheckRetries() {
        return failbackHealthCheckRetries;
    }

    /**
     * Check if authentication is configured.
     *
     * @return True if authentication is configured
     */
    public boolean hasAuthentication() {
        return username != null && password != null;
    }
}

package org.npci.rustyclient.client.connection;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;

import java.io.File;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

/**
 * Factory for creating and caching gRPC channels to RustyCluster nodes.
 * Implements channel reuse optimization for better performance and resource utilization.
 */
public class GrpcChannelFactory {
    private final RustyClusterClientConfig config;
    private final ConcurrentHashMap<String, ManagedChannel> channelCache = new ConcurrentHashMap<>();

    /**
     * Create a new GrpcChannelFactory.
     *
     * @param config The client configuration
     */
    public GrpcChannelFactory(RustyClusterClientConfig config) {
        this.config = config;
    }

    /**
     * Get or create a gRPC channel for the given node.
     * Implements channel reuse optimization for better performance.
     *
     * @param nodeConfig The node configuration
     * @return A cached or new ManagedChannel instance
     */
    public ManagedChannel createChannel(NodeConfig nodeConfig) {
        return channelCache.computeIfAbsent(nodeConfig.getAddress(),
            address -> {
                if (config.isUseSecureConnection()) {
                    return createSecureChannel(nodeConfig);
                } else {
                    return createInsecureChannel(nodeConfig);
                }
            });
    }

    /**
     * Close all cached channels and clear the cache.
     * Should be called when the factory is no longer needed.
     */
    public void close() {
        channelCache.values().forEach(channel -> {
            try {
                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        channelCache.clear();
    }

    private ManagedChannel createInsecureChannel(NodeConfig nodeConfig) {
        return ManagedChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())
                .usePlaintext()
                // High-throughput keep-alive settings
                .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive
                .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection
                .keepAliveWithoutCalls(true)
                // Performance optimizations
                .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches
                .maxInboundMetadataSize(8 * 1024) // 8KB metadata
                // Connection management
                .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections
                // Retry configuration
                .enableRetry()
                .maxRetryAttempts(3)
                .build();
    }

    private ManagedChannel createSecureChannel(NodeConfig nodeConfig) {
        try {
            SslContext sslContext = GrpcSslContexts.forClient()
                    .trustManager(new File(config.getTlsCertPath()))
                    .build();

            return NettyChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())
                    .sslContext(sslContext)
                    // High-throughput keep-alive settings
                    .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive
                    .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection
                    .keepAliveWithoutCalls(true)
                    // Performance optimizations
                    .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches
                    .maxInboundMetadataSize(8 * 1024) // 8KB metadata
                    // Connection management
                    .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections
                    // Retry configuration
                    .enableRetry()
                    .maxRetryAttempts(3)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create secure channel", e);
        }
    }
}

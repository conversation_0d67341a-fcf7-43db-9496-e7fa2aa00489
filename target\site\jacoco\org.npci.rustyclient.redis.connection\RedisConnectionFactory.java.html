<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RedisConnectionFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.redis.connection</a> &gt; <span class="el_source">RedisConnectionFactory.java</span></div><h1>RedisConnectionFactory.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.redis.connection;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

/**
 * Factory for creating Redis connections using Jedis.
 */
public class RedisConnectionFactory extends BasePooledObjectFactory&lt;Jedis&gt; {
<span class="fc" id="L17">    private static final Logger logger = LoggerFactory.getLogger(RedisConnectionFactory.class);</span>

    private final RedisNodeConfig nodeConfig;
    private final RedisClientConfig clientConfig;

    /**
     * Create a new RedisConnectionFactory.
     *
     * @param nodeConfig   The node configuration
     * @param clientConfig The client configuration
     */
<span class="fc" id="L28">    public RedisConnectionFactory(RedisNodeConfig nodeConfig, RedisClientConfig clientConfig) {</span>
<span class="fc" id="L29">        this.nodeConfig = nodeConfig;</span>
<span class="fc" id="L30">        this.clientConfig = clientConfig;</span>
<span class="fc" id="L31">    }</span>

    @Override
    public Jedis create() throws Exception {
<span class="nc" id="L35">        logger.debug(&quot;Creating new Redis connection to {}&quot;, nodeConfig.getAddress());</span>

        // Create Jedis with timeout configuration
<span class="nc" id="L38">        Jedis jedis = new Jedis(nodeConfig.host(), nodeConfig.port(),</span>
<span class="nc" id="L39">                               (int) clientConfig.getConnectionTimeoutMs(),</span>
<span class="nc" id="L40">                               (int) clientConfig.getReadTimeoutMs());</span>

        // Configure SSL if enabled
<span class="nc bnc" id="L43" title="All 2 branches missed.">        if (clientConfig.isUseSecureConnection()) {</span>
            // For SSL, we need to create with SSL configuration
            // This would require using JedisPoolConfig or custom SSL setup
<span class="nc" id="L46">            logger.debug(&quot;SSL configuration requested for: {}&quot;, nodeConfig.getAddress());</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">            if (clientConfig.getTlsCertPath() != null) {</span>
<span class="nc" id="L48">                logger.debug(&quot;Using custom SSL configuration with cert: {}&quot;, clientConfig.getTlsCertPath());</span>
            }
        }

        try {
            // Authenticate if credentials are provided
<span class="nc bnc" id="L54" title="All 2 branches missed.">            if (clientConfig.hasAuthentication()) {</span>
<span class="nc bnc" id="L55" title="All 2 branches missed.">                if (clientConfig.getUsername() != null) {</span>
                    // Redis 6+ ACL authentication
<span class="nc" id="L57">                    jedis.auth(clientConfig.getUsername(), clientConfig.getPassword());</span>
                } else {
                    // Legacy password authentication
<span class="nc" id="L60">                    jedis.auth(clientConfig.getPassword());</span>
                }
<span class="nc" id="L62">                logger.debug(&quot;Authenticated with Redis server: {}&quot;, nodeConfig.getAddress());</span>
            }

            // Select database
<span class="nc bnc" id="L66" title="All 2 branches missed.">            if (clientConfig.getDatabase() != 0) {</span>
<span class="nc" id="L67">                jedis.select(clientConfig.getDatabase());</span>
<span class="nc" id="L68">                logger.debug(&quot;Selected database {} for {}&quot;, clientConfig.getDatabase(), nodeConfig.getAddress());</span>
            }

            // Test with ping
<span class="nc" id="L72">            String response = jedis.ping();</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">            if (!&quot;PONG&quot;.equals(response)) {</span>
<span class="nc" id="L74">                throw new JedisException(&quot;Unexpected ping response: &quot; + response);</span>
            }

<span class="nc" id="L77">            logger.debug(&quot;Successfully created Redis connection to {}&quot;, nodeConfig.getAddress());</span>
<span class="nc" id="L78">            return jedis;</span>

<span class="nc" id="L80">        } catch (Exception e) {</span>
<span class="nc" id="L81">            logger.error(&quot;Failed to create Redis connection to {}&quot;, nodeConfig.getAddress(), e);</span>
            // Clean up the connection if it was partially created
            try {
<span class="nc" id="L84">                jedis.close();</span>
<span class="nc" id="L85">            } catch (Exception closeException) {</span>
<span class="nc" id="L86">                logger.debug(&quot;Error closing failed connection&quot;, closeException);</span>
<span class="nc" id="L87">            }</span>
<span class="nc" id="L88">            throw e;</span>
        }
    }

    @Override
    public PooledObject&lt;Jedis&gt; wrap(Jedis jedis) {
<span class="nc" id="L94">        return new DefaultPooledObject&lt;&gt;(jedis);</span>
    }

    @Override
    public boolean validateObject(PooledObject&lt;Jedis&gt; pooledObject) {
<span class="nc" id="L99">        Jedis jedis = pooledObject.getObject();</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">        if (jedis == null) {</span>
<span class="nc" id="L101">            return false;</span>
        }

        try {
            // Check if connection is still alive
<span class="nc bnc" id="L106" title="All 2 branches missed.">            if (!jedis.isConnected()) {</span>
<span class="nc" id="L107">                return false;</span>
            }

            // Ping test
<span class="nc" id="L111">            String response = jedis.ping();</span>
<span class="nc" id="L112">            return &quot;PONG&quot;.equals(response);</span>
<span class="nc" id="L113">        } catch (Exception e) {</span>
<span class="nc" id="L114">            logger.debug(&quot;Connection validation failed for {}&quot;, nodeConfig.getAddress(), e);</span>
<span class="nc" id="L115">            return false;</span>
        }
    }

    @Override
    public void destroyObject(PooledObject&lt;Jedis&gt; pooledObject) throws Exception {
<span class="nc" id="L121">        Jedis jedis = pooledObject.getObject();</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">        if (jedis != null) {</span>
            try {
                // In Jedis 5.x, just close the connection directly
<span class="nc" id="L125">                jedis.close();</span>
<span class="nc" id="L126">            } catch (Exception e) {</span>
<span class="nc" id="L127">                logger.debug(&quot;Error closing connection&quot;, e);</span>
<span class="nc" id="L128">            }</span>
        }
<span class="nc" id="L130">        logger.debug(&quot;Destroyed Redis connection to {}&quot;, nodeConfig.getAddress());</span>
<span class="nc" id="L131">    }</span>

    @Override
    public void activateObject(PooledObject&lt;Jedis&gt; pooledObject) throws Exception {
<span class="nc" id="L135">        Jedis jedis = pooledObject.getObject();</span>
<span class="nc bnc" id="L136" title="All 4 branches missed.">        if (jedis != null &amp;&amp; !jedis.isConnected()) {</span>
            // In Jedis 5.x, connection is automatically managed
            // Re-authenticate if needed
<span class="nc bnc" id="L139" title="All 2 branches missed.">            if (clientConfig.hasAuthentication()) {</span>
<span class="nc bnc" id="L140" title="All 2 branches missed.">                if (clientConfig.getUsername() != null) {</span>
<span class="nc" id="L141">                    jedis.auth(clientConfig.getUsername(), clientConfig.getPassword());</span>
                } else {
<span class="nc" id="L143">                    jedis.auth(clientConfig.getPassword());</span>
                }
            }

            // Re-select database if needed
<span class="nc bnc" id="L148" title="All 2 branches missed.">            if (clientConfig.getDatabase() != 0) {</span>
<span class="nc" id="L149">                jedis.select(clientConfig.getDatabase());</span>
            }
        }
<span class="nc" id="L152">    }</span>

    @Override
    public void passivateObject(PooledObject&lt;Jedis&gt; pooledObject) throws Exception {
        // Reset connection state if needed
<span class="nc" id="L157">        Jedis jedis = pooledObject.getObject();</span>
<span class="nc bnc" id="L158" title="All 4 branches missed.">        if (jedis != null &amp;&amp; jedis.isConnected()) {</span>
            // Reset to default database if we're not using database 0
<span class="nc bnc" id="L160" title="All 2 branches missed.">            if (clientConfig.getDatabase() != 0) {</span>
                try {
<span class="nc" id="L162">                    jedis.select(0);</span>
<span class="nc" id="L163">                } catch (Exception e) {</span>
<span class="nc" id="L164">                    logger.debug(&quot;Failed to reset database to 0&quot;, e);</span>
<span class="nc" id="L165">                }</span>
            }
        }
<span class="nc" id="L168">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
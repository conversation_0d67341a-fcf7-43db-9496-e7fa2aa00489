package rustycluster;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.59.0)",
    comments = "Source: rustycluster.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class KeyValueServiceGrpc {

  private KeyValueServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = "rustycluster.KeyValueService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.AuthenticateRequest,
      rustycluster.Rustycluster.AuthenticateResponse> getAuthenticateMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Authenticate",
      requestType = rustycluster.Rustycluster.AuthenticateRequest.class,
      responseType = rustycluster.Rustycluster.AuthenticateResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.AuthenticateRequest,
      rustycluster.Rustycluster.AuthenticateResponse> getAuthenticateMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.AuthenticateRequest, rustycluster.Rustycluster.AuthenticateResponse> getAuthenticateMethod;
    if ((getAuthenticateMethod = KeyValueServiceGrpc.getAuthenticateMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getAuthenticateMethod = KeyValueServiceGrpc.getAuthenticateMethod) == null) {
          KeyValueServiceGrpc.getAuthenticateMethod = getAuthenticateMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.AuthenticateRequest, rustycluster.Rustycluster.AuthenticateResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Authenticate"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.AuthenticateRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.AuthenticateResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Authenticate"))
              .build();
        }
      }
    }
    return getAuthenticateMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.PingRequest,
      rustycluster.Rustycluster.PingResponse> getPingMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Ping",
      requestType = rustycluster.Rustycluster.PingRequest.class,
      responseType = rustycluster.Rustycluster.PingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.PingRequest,
      rustycluster.Rustycluster.PingResponse> getPingMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.PingRequest, rustycluster.Rustycluster.PingResponse> getPingMethod;
    if ((getPingMethod = KeyValueServiceGrpc.getPingMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getPingMethod = KeyValueServiceGrpc.getPingMethod) == null) {
          KeyValueServiceGrpc.getPingMethod = getPingMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.PingRequest, rustycluster.Rustycluster.PingResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Ping"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.PingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.PingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Ping"))
              .build();
        }
      }
    }
    return getPingMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.PingRequest,
      rustycluster.Rustycluster.PingResponse> getHealthCheckMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HealthCheck",
      requestType = rustycluster.Rustycluster.PingRequest.class,
      responseType = rustycluster.Rustycluster.PingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.PingRequest,
      rustycluster.Rustycluster.PingResponse> getHealthCheckMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.PingRequest, rustycluster.Rustycluster.PingResponse> getHealthCheckMethod;
    if ((getHealthCheckMethod = KeyValueServiceGrpc.getHealthCheckMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHealthCheckMethod = KeyValueServiceGrpc.getHealthCheckMethod) == null) {
          KeyValueServiceGrpc.getHealthCheckMethod = getHealthCheckMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.PingRequest, rustycluster.Rustycluster.PingResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HealthCheck"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.PingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.PingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HealthCheck"))
              .build();
        }
      }
    }
    return getHealthCheckMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetRequest,
      rustycluster.Rustycluster.SetResponse> getSetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Set",
      requestType = rustycluster.Rustycluster.SetRequest.class,
      responseType = rustycluster.Rustycluster.SetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetRequest,
      rustycluster.Rustycluster.SetResponse> getSetMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetRequest, rustycluster.Rustycluster.SetResponse> getSetMethod;
    if ((getSetMethod = KeyValueServiceGrpc.getSetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetMethod = KeyValueServiceGrpc.getSetMethod) == null) {
          KeyValueServiceGrpc.getSetMethod = getSetMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.SetRequest, rustycluster.Rustycluster.SetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Set"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Set"))
              .build();
        }
      }
    }
    return getSetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.GetRequest,
      rustycluster.Rustycluster.GetResponse> getGetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Get",
      requestType = rustycluster.Rustycluster.GetRequest.class,
      responseType = rustycluster.Rustycluster.GetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.GetRequest,
      rustycluster.Rustycluster.GetResponse> getGetMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.GetRequest, rustycluster.Rustycluster.GetResponse> getGetMethod;
    if ((getGetMethod = KeyValueServiceGrpc.getGetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getGetMethod = KeyValueServiceGrpc.getGetMethod) == null) {
          KeyValueServiceGrpc.getGetMethod = getGetMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.GetRequest, rustycluster.Rustycluster.GetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Get"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.GetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.GetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Get"))
              .build();
        }
      }
    }
    return getGetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.DeleteRequest,
      rustycluster.Rustycluster.DeleteResponse> getDeleteMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Delete",
      requestType = rustycluster.Rustycluster.DeleteRequest.class,
      responseType = rustycluster.Rustycluster.DeleteResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.DeleteRequest,
      rustycluster.Rustycluster.DeleteResponse> getDeleteMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.DeleteRequest, rustycluster.Rustycluster.DeleteResponse> getDeleteMethod;
    if ((getDeleteMethod = KeyValueServiceGrpc.getDeleteMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getDeleteMethod = KeyValueServiceGrpc.getDeleteMethod) == null) {
          KeyValueServiceGrpc.getDeleteMethod = getDeleteMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.DeleteRequest, rustycluster.Rustycluster.DeleteResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Delete"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.DeleteRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.DeleteResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Delete"))
              .build();
        }
      }
    }
    return getDeleteMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetExRequest,
      rustycluster.Rustycluster.SetExResponse> getSetExMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetEx",
      requestType = rustycluster.Rustycluster.SetExRequest.class,
      responseType = rustycluster.Rustycluster.SetExResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetExRequest,
      rustycluster.Rustycluster.SetExResponse> getSetExMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetExRequest, rustycluster.Rustycluster.SetExResponse> getSetExMethod;
    if ((getSetExMethod = KeyValueServiceGrpc.getSetExMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetExMethod = KeyValueServiceGrpc.getSetExMethod) == null) {
          KeyValueServiceGrpc.getSetExMethod = getSetExMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.SetExRequest, rustycluster.Rustycluster.SetExResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetEx"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetExRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetExResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("SetEx"))
              .build();
        }
      }
    }
    return getSetExMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetExpiryRequest,
      rustycluster.Rustycluster.SetExpiryResponse> getSetExpiryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetExpiry",
      requestType = rustycluster.Rustycluster.SetExpiryRequest.class,
      responseType = rustycluster.Rustycluster.SetExpiryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetExpiryRequest,
      rustycluster.Rustycluster.SetExpiryResponse> getSetExpiryMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetExpiryRequest, rustycluster.Rustycluster.SetExpiryResponse> getSetExpiryMethod;
    if ((getSetExpiryMethod = KeyValueServiceGrpc.getSetExpiryMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetExpiryMethod = KeyValueServiceGrpc.getSetExpiryMethod) == null) {
          KeyValueServiceGrpc.getSetExpiryMethod = getSetExpiryMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.SetExpiryRequest, rustycluster.Rustycluster.SetExpiryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetExpiry"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetExpiryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetExpiryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("SetExpiry"))
              .build();
        }
      }
    }
    return getSetExpiryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.IncrByRequest,
      rustycluster.Rustycluster.IncrByResponse> getIncrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "IncrBy",
      requestType = rustycluster.Rustycluster.IncrByRequest.class,
      responseType = rustycluster.Rustycluster.IncrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.IncrByRequest,
      rustycluster.Rustycluster.IncrByResponse> getIncrByMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.IncrByRequest, rustycluster.Rustycluster.IncrByResponse> getIncrByMethod;
    if ((getIncrByMethod = KeyValueServiceGrpc.getIncrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getIncrByMethod = KeyValueServiceGrpc.getIncrByMethod) == null) {
          KeyValueServiceGrpc.getIncrByMethod = getIncrByMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.IncrByRequest, rustycluster.Rustycluster.IncrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "IncrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.IncrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.IncrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("IncrBy"))
              .build();
        }
      }
    }
    return getIncrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.DecrByRequest,
      rustycluster.Rustycluster.DecrByResponse> getDecrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "DecrBy",
      requestType = rustycluster.Rustycluster.DecrByRequest.class,
      responseType = rustycluster.Rustycluster.DecrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.DecrByRequest,
      rustycluster.Rustycluster.DecrByResponse> getDecrByMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.DecrByRequest, rustycluster.Rustycluster.DecrByResponse> getDecrByMethod;
    if ((getDecrByMethod = KeyValueServiceGrpc.getDecrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getDecrByMethod = KeyValueServiceGrpc.getDecrByMethod) == null) {
          KeyValueServiceGrpc.getDecrByMethod = getDecrByMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.DecrByRequest, rustycluster.Rustycluster.DecrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "DecrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.DecrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.DecrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("DecrBy"))
              .build();
        }
      }
    }
    return getDecrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.IncrByFloatRequest,
      rustycluster.Rustycluster.IncrByFloatResponse> getIncrByFloatMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "IncrByFloat",
      requestType = rustycluster.Rustycluster.IncrByFloatRequest.class,
      responseType = rustycluster.Rustycluster.IncrByFloatResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.IncrByFloatRequest,
      rustycluster.Rustycluster.IncrByFloatResponse> getIncrByFloatMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.IncrByFloatRequest, rustycluster.Rustycluster.IncrByFloatResponse> getIncrByFloatMethod;
    if ((getIncrByFloatMethod = KeyValueServiceGrpc.getIncrByFloatMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getIncrByFloatMethod = KeyValueServiceGrpc.getIncrByFloatMethod) == null) {
          KeyValueServiceGrpc.getIncrByFloatMethod = getIncrByFloatMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.IncrByFloatRequest, rustycluster.Rustycluster.IncrByFloatResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "IncrByFloat"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.IncrByFloatRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.IncrByFloatResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("IncrByFloat"))
              .build();
        }
      }
    }
    return getIncrByFloatMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HSetRequest,
      rustycluster.Rustycluster.HSetResponse> getHSetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HSet",
      requestType = rustycluster.Rustycluster.HSetRequest.class,
      responseType = rustycluster.Rustycluster.HSetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HSetRequest,
      rustycluster.Rustycluster.HSetResponse> getHSetMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HSetRequest, rustycluster.Rustycluster.HSetResponse> getHSetMethod;
    if ((getHSetMethod = KeyValueServiceGrpc.getHSetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHSetMethod = KeyValueServiceGrpc.getHSetMethod) == null) {
          KeyValueServiceGrpc.getHSetMethod = getHSetMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HSetRequest, rustycluster.Rustycluster.HSetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HSet"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HSetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HSetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HSet"))
              .build();
        }
      }
    }
    return getHSetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HGetRequest,
      rustycluster.Rustycluster.HGetResponse> getHGetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HGet",
      requestType = rustycluster.Rustycluster.HGetRequest.class,
      responseType = rustycluster.Rustycluster.HGetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HGetRequest,
      rustycluster.Rustycluster.HGetResponse> getHGetMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HGetRequest, rustycluster.Rustycluster.HGetResponse> getHGetMethod;
    if ((getHGetMethod = KeyValueServiceGrpc.getHGetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHGetMethod = KeyValueServiceGrpc.getHGetMethod) == null) {
          KeyValueServiceGrpc.getHGetMethod = getHGetMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HGetRequest, rustycluster.Rustycluster.HGetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HGet"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HGetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HGetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HGet"))
              .build();
        }
      }
    }
    return getHGetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HGetAllRequest,
      rustycluster.Rustycluster.HGetAllResponse> getHGetAllMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HGetAll",
      requestType = rustycluster.Rustycluster.HGetAllRequest.class,
      responseType = rustycluster.Rustycluster.HGetAllResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HGetAllRequest,
      rustycluster.Rustycluster.HGetAllResponse> getHGetAllMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HGetAllRequest, rustycluster.Rustycluster.HGetAllResponse> getHGetAllMethod;
    if ((getHGetAllMethod = KeyValueServiceGrpc.getHGetAllMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHGetAllMethod = KeyValueServiceGrpc.getHGetAllMethod) == null) {
          KeyValueServiceGrpc.getHGetAllMethod = getHGetAllMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HGetAllRequest, rustycluster.Rustycluster.HGetAllResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HGetAll"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HGetAllRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HGetAllResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HGetAll"))
              .build();
        }
      }
    }
    return getHGetAllMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HIncrByRequest,
      rustycluster.Rustycluster.HIncrByResponse> getHIncrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HIncrBy",
      requestType = rustycluster.Rustycluster.HIncrByRequest.class,
      responseType = rustycluster.Rustycluster.HIncrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HIncrByRequest,
      rustycluster.Rustycluster.HIncrByResponse> getHIncrByMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HIncrByRequest, rustycluster.Rustycluster.HIncrByResponse> getHIncrByMethod;
    if ((getHIncrByMethod = KeyValueServiceGrpc.getHIncrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHIncrByMethod = KeyValueServiceGrpc.getHIncrByMethod) == null) {
          KeyValueServiceGrpc.getHIncrByMethod = getHIncrByMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HIncrByRequest, rustycluster.Rustycluster.HIncrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HIncrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HIncrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HIncrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HIncrBy"))
              .build();
        }
      }
    }
    return getHIncrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HDecrByRequest,
      rustycluster.Rustycluster.HDecrByResponse> getHDecrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HDecrBy",
      requestType = rustycluster.Rustycluster.HDecrByRequest.class,
      responseType = rustycluster.Rustycluster.HDecrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HDecrByRequest,
      rustycluster.Rustycluster.HDecrByResponse> getHDecrByMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HDecrByRequest, rustycluster.Rustycluster.HDecrByResponse> getHDecrByMethod;
    if ((getHDecrByMethod = KeyValueServiceGrpc.getHDecrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHDecrByMethod = KeyValueServiceGrpc.getHDecrByMethod) == null) {
          KeyValueServiceGrpc.getHDecrByMethod = getHDecrByMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HDecrByRequest, rustycluster.Rustycluster.HDecrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HDecrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HDecrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HDecrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HDecrBy"))
              .build();
        }
      }
    }
    return getHDecrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HIncrByFloatRequest,
      rustycluster.Rustycluster.HIncrByFloatResponse> getHIncrByFloatMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HIncrByFloat",
      requestType = rustycluster.Rustycluster.HIncrByFloatRequest.class,
      responseType = rustycluster.Rustycluster.HIncrByFloatResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HIncrByFloatRequest,
      rustycluster.Rustycluster.HIncrByFloatResponse> getHIncrByFloatMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HIncrByFloatRequest, rustycluster.Rustycluster.HIncrByFloatResponse> getHIncrByFloatMethod;
    if ((getHIncrByFloatMethod = KeyValueServiceGrpc.getHIncrByFloatMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHIncrByFloatMethod = KeyValueServiceGrpc.getHIncrByFloatMethod) == null) {
          KeyValueServiceGrpc.getHIncrByFloatMethod = getHIncrByFloatMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HIncrByFloatRequest, rustycluster.Rustycluster.HIncrByFloatResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HIncrByFloat"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HIncrByFloatRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HIncrByFloatResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HIncrByFloat"))
              .build();
        }
      }
    }
    return getHIncrByFloatMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HMSetRequest,
      rustycluster.Rustycluster.HMSetResponse> getHMSetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HMSet",
      requestType = rustycluster.Rustycluster.HMSetRequest.class,
      responseType = rustycluster.Rustycluster.HMSetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HMSetRequest,
      rustycluster.Rustycluster.HMSetResponse> getHMSetMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HMSetRequest, rustycluster.Rustycluster.HMSetResponse> getHMSetMethod;
    if ((getHMSetMethod = KeyValueServiceGrpc.getHMSetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHMSetMethod = KeyValueServiceGrpc.getHMSetMethod) == null) {
          KeyValueServiceGrpc.getHMSetMethod = getHMSetMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HMSetRequest, rustycluster.Rustycluster.HMSetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HMSet"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HMSetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HMSetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HMSet"))
              .build();
        }
      }
    }
    return getHMSetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.HExistsRequest,
      rustycluster.Rustycluster.HExistsResponse> getHExistsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HExists",
      requestType = rustycluster.Rustycluster.HExistsRequest.class,
      responseType = rustycluster.Rustycluster.HExistsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.HExistsRequest,
      rustycluster.Rustycluster.HExistsResponse> getHExistsMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.HExistsRequest, rustycluster.Rustycluster.HExistsResponse> getHExistsMethod;
    if ((getHExistsMethod = KeyValueServiceGrpc.getHExistsMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHExistsMethod = KeyValueServiceGrpc.getHExistsMethod) == null) {
          KeyValueServiceGrpc.getHExistsMethod = getHExistsMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.HExistsRequest, rustycluster.Rustycluster.HExistsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HExists"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HExistsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.HExistsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HExists"))
              .build();
        }
      }
    }
    return getHExistsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetNXRequest,
      rustycluster.Rustycluster.SetNXResponse> getSetNXMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetNX",
      requestType = rustycluster.Rustycluster.SetNXRequest.class,
      responseType = rustycluster.Rustycluster.SetNXResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetNXRequest,
      rustycluster.Rustycluster.SetNXResponse> getSetNXMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.SetNXRequest, rustycluster.Rustycluster.SetNXResponse> getSetNXMethod;
    if ((getSetNXMethod = KeyValueServiceGrpc.getSetNXMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetNXMethod = KeyValueServiceGrpc.getSetNXMethod) == null) {
          KeyValueServiceGrpc.getSetNXMethod = getSetNXMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.SetNXRequest, rustycluster.Rustycluster.SetNXResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetNX"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetNXRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.SetNXResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("SetNX"))
              .build();
        }
      }
    }
    return getSetNXMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.ExistsRequest,
      rustycluster.Rustycluster.ExistsResponse> getExistsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Exists",
      requestType = rustycluster.Rustycluster.ExistsRequest.class,
      responseType = rustycluster.Rustycluster.ExistsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.ExistsRequest,
      rustycluster.Rustycluster.ExistsResponse> getExistsMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.ExistsRequest, rustycluster.Rustycluster.ExistsResponse> getExistsMethod;
    if ((getExistsMethod = KeyValueServiceGrpc.getExistsMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getExistsMethod = KeyValueServiceGrpc.getExistsMethod) == null) {
          KeyValueServiceGrpc.getExistsMethod = getExistsMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.ExistsRequest, rustycluster.Rustycluster.ExistsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Exists"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.ExistsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.ExistsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Exists"))
              .build();
        }
      }
    }
    return getExistsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.LoadScriptRequest,
      rustycluster.Rustycluster.LoadScriptResponse> getLoadScriptMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "LoadScript",
      requestType = rustycluster.Rustycluster.LoadScriptRequest.class,
      responseType = rustycluster.Rustycluster.LoadScriptResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.LoadScriptRequest,
      rustycluster.Rustycluster.LoadScriptResponse> getLoadScriptMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.LoadScriptRequest, rustycluster.Rustycluster.LoadScriptResponse> getLoadScriptMethod;
    if ((getLoadScriptMethod = KeyValueServiceGrpc.getLoadScriptMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getLoadScriptMethod = KeyValueServiceGrpc.getLoadScriptMethod) == null) {
          KeyValueServiceGrpc.getLoadScriptMethod = getLoadScriptMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.LoadScriptRequest, rustycluster.Rustycluster.LoadScriptResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "LoadScript"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.LoadScriptRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.LoadScriptResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("LoadScript"))
              .build();
        }
      }
    }
    return getLoadScriptMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.EvalShaRequest,
      rustycluster.Rustycluster.EvalShaResponse> getEvalShaMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "EvalSha",
      requestType = rustycluster.Rustycluster.EvalShaRequest.class,
      responseType = rustycluster.Rustycluster.EvalShaResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.EvalShaRequest,
      rustycluster.Rustycluster.EvalShaResponse> getEvalShaMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.EvalShaRequest, rustycluster.Rustycluster.EvalShaResponse> getEvalShaMethod;
    if ((getEvalShaMethod = KeyValueServiceGrpc.getEvalShaMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getEvalShaMethod = KeyValueServiceGrpc.getEvalShaMethod) == null) {
          KeyValueServiceGrpc.getEvalShaMethod = getEvalShaMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.EvalShaRequest, rustycluster.Rustycluster.EvalShaResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "EvalSha"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.EvalShaRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.EvalShaResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("EvalSha"))
              .build();
        }
      }
    }
    return getEvalShaMethod;
  }

  private static volatile io.grpc.MethodDescriptor<rustycluster.Rustycluster.BatchWriteRequest,
      rustycluster.Rustycluster.BatchWriteResponse> getBatchWriteMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "BatchWrite",
      requestType = rustycluster.Rustycluster.BatchWriteRequest.class,
      responseType = rustycluster.Rustycluster.BatchWriteResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<rustycluster.Rustycluster.BatchWriteRequest,
      rustycluster.Rustycluster.BatchWriteResponse> getBatchWriteMethod() {
    io.grpc.MethodDescriptor<rustycluster.Rustycluster.BatchWriteRequest, rustycluster.Rustycluster.BatchWriteResponse> getBatchWriteMethod;
    if ((getBatchWriteMethod = KeyValueServiceGrpc.getBatchWriteMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getBatchWriteMethod = KeyValueServiceGrpc.getBatchWriteMethod) == null) {
          KeyValueServiceGrpc.getBatchWriteMethod = getBatchWriteMethod =
              io.grpc.MethodDescriptor.<rustycluster.Rustycluster.BatchWriteRequest, rustycluster.Rustycluster.BatchWriteResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "BatchWrite"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.BatchWriteRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  rustycluster.Rustycluster.BatchWriteResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("BatchWrite"))
              .build();
        }
      }
    }
    return getBatchWriteMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static KeyValueServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceStub>() {
        @java.lang.Override
        public KeyValueServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new KeyValueServiceStub(channel, callOptions);
        }
      };
    return KeyValueServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static KeyValueServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceBlockingStub>() {
        @java.lang.Override
        public KeyValueServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new KeyValueServiceBlockingStub(channel, callOptions);
        }
      };
    return KeyValueServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static KeyValueServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceFutureStub>() {
        @java.lang.Override
        public KeyValueServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new KeyValueServiceFutureStub(channel, callOptions);
        }
      };
    return KeyValueServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    default void authenticate(rustycluster.Rustycluster.AuthenticateRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.AuthenticateResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAuthenticateMethod(), responseObserver);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    default void ping(rustycluster.Rustycluster.PingRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.PingResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getPingMethod(), responseObserver);
    }

    /**
     */
    default void healthCheck(rustycluster.Rustycluster.PingRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.PingResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHealthCheckMethod(), responseObserver);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    default void set(rustycluster.Rustycluster.SetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetMethod(), responseObserver);
    }

    /**
     */
    default void get(rustycluster.Rustycluster.GetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.GetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetMethod(), responseObserver);
    }

    /**
     */
    default void delete(rustycluster.Rustycluster.DeleteRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.DeleteResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteMethod(), responseObserver);
    }

    /**
     */
    default void setEx(rustycluster.Rustycluster.SetExRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetExResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetExMethod(), responseObserver);
    }

    /**
     */
    default void setExpiry(rustycluster.Rustycluster.SetExpiryRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetExpiryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetExpiryMethod(), responseObserver);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    default void incrBy(rustycluster.Rustycluster.IncrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.IncrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getIncrByMethod(), responseObserver);
    }

    /**
     */
    default void decrBy(rustycluster.Rustycluster.DecrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.DecrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDecrByMethod(), responseObserver);
    }

    /**
     */
    default void incrByFloat(rustycluster.Rustycluster.IncrByFloatRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.IncrByFloatResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getIncrByFloatMethod(), responseObserver);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    default void hSet(rustycluster.Rustycluster.HSetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HSetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHSetMethod(), responseObserver);
    }

    /**
     */
    default void hGet(rustycluster.Rustycluster.HGetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HGetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHGetMethod(), responseObserver);
    }

    /**
     */
    default void hGetAll(rustycluster.Rustycluster.HGetAllRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HGetAllResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHGetAllMethod(), responseObserver);
    }

    /**
     */
    default void hIncrBy(rustycluster.Rustycluster.HIncrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HIncrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHIncrByMethod(), responseObserver);
    }

    /**
     */
    default void hDecrBy(rustycluster.Rustycluster.HDecrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HDecrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHDecrByMethod(), responseObserver);
    }

    /**
     */
    default void hIncrByFloat(rustycluster.Rustycluster.HIncrByFloatRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HIncrByFloatResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHIncrByFloatMethod(), responseObserver);
    }

    /**
     */
    default void hMSet(rustycluster.Rustycluster.HMSetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HMSetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHMSetMethod(), responseObserver);
    }

    /**
     */
    default void hExists(rustycluster.Rustycluster.HExistsRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HExistsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHExistsMethod(), responseObserver);
    }

    /**
     * <pre>
     * Key operations
     * </pre>
     */
    default void setNX(rustycluster.Rustycluster.SetNXRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetNXResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetNXMethod(), responseObserver);
    }

    /**
     */
    default void exists(rustycluster.Rustycluster.ExistsRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.ExistsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getExistsMethod(), responseObserver);
    }

    /**
     * <pre>
     * Script operations
     * </pre>
     */
    default void loadScript(rustycluster.Rustycluster.LoadScriptRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.LoadScriptResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLoadScriptMethod(), responseObserver);
    }

    /**
     */
    default void evalSha(rustycluster.Rustycluster.EvalShaRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.EvalShaResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getEvalShaMethod(), responseObserver);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    default void batchWrite(rustycluster.Rustycluster.BatchWriteRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.BatchWriteResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getBatchWriteMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service KeyValueService.
   */
  public static abstract class KeyValueServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return KeyValueServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceStub
      extends io.grpc.stub.AbstractAsyncStub<KeyValueServiceStub> {
    private KeyValueServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected KeyValueServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new KeyValueServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    public void authenticate(rustycluster.Rustycluster.AuthenticateRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.AuthenticateResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getAuthenticateMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    public void ping(rustycluster.Rustycluster.PingRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.PingResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getPingMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void healthCheck(rustycluster.Rustycluster.PingRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.PingResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHealthCheckMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    public void set(rustycluster.Rustycluster.SetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void get(rustycluster.Rustycluster.GetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.GetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void delete(rustycluster.Rustycluster.DeleteRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.DeleteResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void setEx(rustycluster.Rustycluster.SetExRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetExResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetExMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void setExpiry(rustycluster.Rustycluster.SetExpiryRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetExpiryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetExpiryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    public void incrBy(rustycluster.Rustycluster.IncrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.IncrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getIncrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void decrBy(rustycluster.Rustycluster.DecrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.DecrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDecrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void incrByFloat(rustycluster.Rustycluster.IncrByFloatRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.IncrByFloatResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getIncrByFloatMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    public void hSet(rustycluster.Rustycluster.HSetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HSetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHSetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hGet(rustycluster.Rustycluster.HGetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HGetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHGetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hGetAll(rustycluster.Rustycluster.HGetAllRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HGetAllResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHGetAllMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hIncrBy(rustycluster.Rustycluster.HIncrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HIncrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHIncrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hDecrBy(rustycluster.Rustycluster.HDecrByRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HDecrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHDecrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hIncrByFloat(rustycluster.Rustycluster.HIncrByFloatRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HIncrByFloatResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHIncrByFloatMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hMSet(rustycluster.Rustycluster.HMSetRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HMSetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHMSetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hExists(rustycluster.Rustycluster.HExistsRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HExistsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHExistsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Key operations
     * </pre>
     */
    public void setNX(rustycluster.Rustycluster.SetNXRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetNXResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetNXMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void exists(rustycluster.Rustycluster.ExistsRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.ExistsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getExistsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Script operations
     * </pre>
     */
    public void loadScript(rustycluster.Rustycluster.LoadScriptRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.LoadScriptResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLoadScriptMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void evalSha(rustycluster.Rustycluster.EvalShaRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.EvalShaResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getEvalShaMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    public void batchWrite(rustycluster.Rustycluster.BatchWriteRequest request,
        io.grpc.stub.StreamObserver<rustycluster.Rustycluster.BatchWriteResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getBatchWriteMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<KeyValueServiceBlockingStub> {
    private KeyValueServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected KeyValueServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new KeyValueServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    public rustycluster.Rustycluster.AuthenticateResponse authenticate(rustycluster.Rustycluster.AuthenticateRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getAuthenticateMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    public rustycluster.Rustycluster.PingResponse ping(rustycluster.Rustycluster.PingRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getPingMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.PingResponse healthCheck(rustycluster.Rustycluster.PingRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHealthCheckMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    public rustycluster.Rustycluster.SetResponse set(rustycluster.Rustycluster.SetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.GetResponse get(rustycluster.Rustycluster.GetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.DeleteResponse delete(rustycluster.Rustycluster.DeleteRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.SetExResponse setEx(rustycluster.Rustycluster.SetExRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetExMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.SetExpiryResponse setExpiry(rustycluster.Rustycluster.SetExpiryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetExpiryMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    public rustycluster.Rustycluster.IncrByResponse incrBy(rustycluster.Rustycluster.IncrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getIncrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.DecrByResponse decrBy(rustycluster.Rustycluster.DecrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDecrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.IncrByFloatResponse incrByFloat(rustycluster.Rustycluster.IncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getIncrByFloatMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    public rustycluster.Rustycluster.HSetResponse hSet(rustycluster.Rustycluster.HSetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHSetMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.HGetResponse hGet(rustycluster.Rustycluster.HGetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHGetMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.HGetAllResponse hGetAll(rustycluster.Rustycluster.HGetAllRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHGetAllMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.HIncrByResponse hIncrBy(rustycluster.Rustycluster.HIncrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHIncrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.HDecrByResponse hDecrBy(rustycluster.Rustycluster.HDecrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHDecrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.HIncrByFloatResponse hIncrByFloat(rustycluster.Rustycluster.HIncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHIncrByFloatMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.HMSetResponse hMSet(rustycluster.Rustycluster.HMSetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHMSetMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.HExistsResponse hExists(rustycluster.Rustycluster.HExistsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHExistsMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Key operations
     * </pre>
     */
    public rustycluster.Rustycluster.SetNXResponse setNX(rustycluster.Rustycluster.SetNXRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetNXMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.ExistsResponse exists(rustycluster.Rustycluster.ExistsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getExistsMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Script operations
     * </pre>
     */
    public rustycluster.Rustycluster.LoadScriptResponse loadScript(rustycluster.Rustycluster.LoadScriptRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLoadScriptMethod(), getCallOptions(), request);
    }

    /**
     */
    public rustycluster.Rustycluster.EvalShaResponse evalSha(rustycluster.Rustycluster.EvalShaRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getEvalShaMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    public rustycluster.Rustycluster.BatchWriteResponse batchWrite(rustycluster.Rustycluster.BatchWriteRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getBatchWriteMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<KeyValueServiceFutureStub> {
    private KeyValueServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected KeyValueServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new KeyValueServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.AuthenticateResponse> authenticate(
        rustycluster.Rustycluster.AuthenticateRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getAuthenticateMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.PingResponse> ping(
        rustycluster.Rustycluster.PingRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getPingMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.PingResponse> healthCheck(
        rustycluster.Rustycluster.PingRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHealthCheckMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.SetResponse> set(
        rustycluster.Rustycluster.SetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.GetResponse> get(
        rustycluster.Rustycluster.GetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.DeleteResponse> delete(
        rustycluster.Rustycluster.DeleteRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.SetExResponse> setEx(
        rustycluster.Rustycluster.SetExRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetExMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.SetExpiryResponse> setExpiry(
        rustycluster.Rustycluster.SetExpiryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetExpiryMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.IncrByResponse> incrBy(
        rustycluster.Rustycluster.IncrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getIncrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.DecrByResponse> decrBy(
        rustycluster.Rustycluster.DecrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDecrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.IncrByFloatResponse> incrByFloat(
        rustycluster.Rustycluster.IncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getIncrByFloatMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HSetResponse> hSet(
        rustycluster.Rustycluster.HSetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHSetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HGetResponse> hGet(
        rustycluster.Rustycluster.HGetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHGetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HGetAllResponse> hGetAll(
        rustycluster.Rustycluster.HGetAllRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHGetAllMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HIncrByResponse> hIncrBy(
        rustycluster.Rustycluster.HIncrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHIncrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HDecrByResponse> hDecrBy(
        rustycluster.Rustycluster.HDecrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHDecrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HIncrByFloatResponse> hIncrByFloat(
        rustycluster.Rustycluster.HIncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHIncrByFloatMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HMSetResponse> hMSet(
        rustycluster.Rustycluster.HMSetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHMSetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.HExistsResponse> hExists(
        rustycluster.Rustycluster.HExistsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHExistsMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Key operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.SetNXResponse> setNX(
        rustycluster.Rustycluster.SetNXRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetNXMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.ExistsResponse> exists(
        rustycluster.Rustycluster.ExistsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getExistsMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Script operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.LoadScriptResponse> loadScript(
        rustycluster.Rustycluster.LoadScriptRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLoadScriptMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.EvalShaResponse> evalSha(
        rustycluster.Rustycluster.EvalShaRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getEvalShaMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<rustycluster.Rustycluster.BatchWriteResponse> batchWrite(
        rustycluster.Rustycluster.BatchWriteRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getBatchWriteMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_AUTHENTICATE = 0;
  private static final int METHODID_PING = 1;
  private static final int METHODID_HEALTH_CHECK = 2;
  private static final int METHODID_SET = 3;
  private static final int METHODID_GET = 4;
  private static final int METHODID_DELETE = 5;
  private static final int METHODID_SET_EX = 6;
  private static final int METHODID_SET_EXPIRY = 7;
  private static final int METHODID_INCR_BY = 8;
  private static final int METHODID_DECR_BY = 9;
  private static final int METHODID_INCR_BY_FLOAT = 10;
  private static final int METHODID_HSET = 11;
  private static final int METHODID_HGET = 12;
  private static final int METHODID_HGET_ALL = 13;
  private static final int METHODID_HINCR_BY = 14;
  private static final int METHODID_HDECR_BY = 15;
  private static final int METHODID_HINCR_BY_FLOAT = 16;
  private static final int METHODID_HMSET = 17;
  private static final int METHODID_HEXISTS = 18;
  private static final int METHODID_SET_NX = 19;
  private static final int METHODID_EXISTS = 20;
  private static final int METHODID_LOAD_SCRIPT = 21;
  private static final int METHODID_EVAL_SHA = 22;
  private static final int METHODID_BATCH_WRITE = 23;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_AUTHENTICATE:
          serviceImpl.authenticate((rustycluster.Rustycluster.AuthenticateRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.AuthenticateResponse>) responseObserver);
          break;
        case METHODID_PING:
          serviceImpl.ping((rustycluster.Rustycluster.PingRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.PingResponse>) responseObserver);
          break;
        case METHODID_HEALTH_CHECK:
          serviceImpl.healthCheck((rustycluster.Rustycluster.PingRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.PingResponse>) responseObserver);
          break;
        case METHODID_SET:
          serviceImpl.set((rustycluster.Rustycluster.SetRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetResponse>) responseObserver);
          break;
        case METHODID_GET:
          serviceImpl.get((rustycluster.Rustycluster.GetRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.GetResponse>) responseObserver);
          break;
        case METHODID_DELETE:
          serviceImpl.delete((rustycluster.Rustycluster.DeleteRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.DeleteResponse>) responseObserver);
          break;
        case METHODID_SET_EX:
          serviceImpl.setEx((rustycluster.Rustycluster.SetExRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetExResponse>) responseObserver);
          break;
        case METHODID_SET_EXPIRY:
          serviceImpl.setExpiry((rustycluster.Rustycluster.SetExpiryRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetExpiryResponse>) responseObserver);
          break;
        case METHODID_INCR_BY:
          serviceImpl.incrBy((rustycluster.Rustycluster.IncrByRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.IncrByResponse>) responseObserver);
          break;
        case METHODID_DECR_BY:
          serviceImpl.decrBy((rustycluster.Rustycluster.DecrByRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.DecrByResponse>) responseObserver);
          break;
        case METHODID_INCR_BY_FLOAT:
          serviceImpl.incrByFloat((rustycluster.Rustycluster.IncrByFloatRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.IncrByFloatResponse>) responseObserver);
          break;
        case METHODID_HSET:
          serviceImpl.hSet((rustycluster.Rustycluster.HSetRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HSetResponse>) responseObserver);
          break;
        case METHODID_HGET:
          serviceImpl.hGet((rustycluster.Rustycluster.HGetRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HGetResponse>) responseObserver);
          break;
        case METHODID_HGET_ALL:
          serviceImpl.hGetAll((rustycluster.Rustycluster.HGetAllRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HGetAllResponse>) responseObserver);
          break;
        case METHODID_HINCR_BY:
          serviceImpl.hIncrBy((rustycluster.Rustycluster.HIncrByRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HIncrByResponse>) responseObserver);
          break;
        case METHODID_HDECR_BY:
          serviceImpl.hDecrBy((rustycluster.Rustycluster.HDecrByRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HDecrByResponse>) responseObserver);
          break;
        case METHODID_HINCR_BY_FLOAT:
          serviceImpl.hIncrByFloat((rustycluster.Rustycluster.HIncrByFloatRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HIncrByFloatResponse>) responseObserver);
          break;
        case METHODID_HMSET:
          serviceImpl.hMSet((rustycluster.Rustycluster.HMSetRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HMSetResponse>) responseObserver);
          break;
        case METHODID_HEXISTS:
          serviceImpl.hExists((rustycluster.Rustycluster.HExistsRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.HExistsResponse>) responseObserver);
          break;
        case METHODID_SET_NX:
          serviceImpl.setNX((rustycluster.Rustycluster.SetNXRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.SetNXResponse>) responseObserver);
          break;
        case METHODID_EXISTS:
          serviceImpl.exists((rustycluster.Rustycluster.ExistsRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.ExistsResponse>) responseObserver);
          break;
        case METHODID_LOAD_SCRIPT:
          serviceImpl.loadScript((rustycluster.Rustycluster.LoadScriptRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.LoadScriptResponse>) responseObserver);
          break;
        case METHODID_EVAL_SHA:
          serviceImpl.evalSha((rustycluster.Rustycluster.EvalShaRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.EvalShaResponse>) responseObserver);
          break;
        case METHODID_BATCH_WRITE:
          serviceImpl.batchWrite((rustycluster.Rustycluster.BatchWriteRequest) request,
              (io.grpc.stub.StreamObserver<rustycluster.Rustycluster.BatchWriteResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getAuthenticateMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.AuthenticateRequest,
              rustycluster.Rustycluster.AuthenticateResponse>(
                service, METHODID_AUTHENTICATE)))
        .addMethod(
          getPingMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.PingRequest,
              rustycluster.Rustycluster.PingResponse>(
                service, METHODID_PING)))
        .addMethod(
          getHealthCheckMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.PingRequest,
              rustycluster.Rustycluster.PingResponse>(
                service, METHODID_HEALTH_CHECK)))
        .addMethod(
          getSetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.SetRequest,
              rustycluster.Rustycluster.SetResponse>(
                service, METHODID_SET)))
        .addMethod(
          getGetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.GetRequest,
              rustycluster.Rustycluster.GetResponse>(
                service, METHODID_GET)))
        .addMethod(
          getDeleteMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.DeleteRequest,
              rustycluster.Rustycluster.DeleteResponse>(
                service, METHODID_DELETE)))
        .addMethod(
          getSetExMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.SetExRequest,
              rustycluster.Rustycluster.SetExResponse>(
                service, METHODID_SET_EX)))
        .addMethod(
          getSetExpiryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.SetExpiryRequest,
              rustycluster.Rustycluster.SetExpiryResponse>(
                service, METHODID_SET_EXPIRY)))
        .addMethod(
          getIncrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.IncrByRequest,
              rustycluster.Rustycluster.IncrByResponse>(
                service, METHODID_INCR_BY)))
        .addMethod(
          getDecrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.DecrByRequest,
              rustycluster.Rustycluster.DecrByResponse>(
                service, METHODID_DECR_BY)))
        .addMethod(
          getIncrByFloatMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.IncrByFloatRequest,
              rustycluster.Rustycluster.IncrByFloatResponse>(
                service, METHODID_INCR_BY_FLOAT)))
        .addMethod(
          getHSetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HSetRequest,
              rustycluster.Rustycluster.HSetResponse>(
                service, METHODID_HSET)))
        .addMethod(
          getHGetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HGetRequest,
              rustycluster.Rustycluster.HGetResponse>(
                service, METHODID_HGET)))
        .addMethod(
          getHGetAllMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HGetAllRequest,
              rustycluster.Rustycluster.HGetAllResponse>(
                service, METHODID_HGET_ALL)))
        .addMethod(
          getHIncrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HIncrByRequest,
              rustycluster.Rustycluster.HIncrByResponse>(
                service, METHODID_HINCR_BY)))
        .addMethod(
          getHDecrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HDecrByRequest,
              rustycluster.Rustycluster.HDecrByResponse>(
                service, METHODID_HDECR_BY)))
        .addMethod(
          getHIncrByFloatMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HIncrByFloatRequest,
              rustycluster.Rustycluster.HIncrByFloatResponse>(
                service, METHODID_HINCR_BY_FLOAT)))
        .addMethod(
          getHMSetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HMSetRequest,
              rustycluster.Rustycluster.HMSetResponse>(
                service, METHODID_HMSET)))
        .addMethod(
          getHExistsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.HExistsRequest,
              rustycluster.Rustycluster.HExistsResponse>(
                service, METHODID_HEXISTS)))
        .addMethod(
          getSetNXMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.SetNXRequest,
              rustycluster.Rustycluster.SetNXResponse>(
                service, METHODID_SET_NX)))
        .addMethod(
          getExistsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.ExistsRequest,
              rustycluster.Rustycluster.ExistsResponse>(
                service, METHODID_EXISTS)))
        .addMethod(
          getLoadScriptMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.LoadScriptRequest,
              rustycluster.Rustycluster.LoadScriptResponse>(
                service, METHODID_LOAD_SCRIPT)))
        .addMethod(
          getEvalShaMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.EvalShaRequest,
              rustycluster.Rustycluster.EvalShaResponse>(
                service, METHODID_EVAL_SHA)))
        .addMethod(
          getBatchWriteMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              rustycluster.Rustycluster.BatchWriteRequest,
              rustycluster.Rustycluster.BatchWriteResponse>(
                service, METHODID_BATCH_WRITE)))
        .build();
  }

  private static abstract class KeyValueServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    KeyValueServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return rustycluster.Rustycluster.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("KeyValueService");
    }
  }

  private static final class KeyValueServiceFileDescriptorSupplier
      extends KeyValueServiceBaseDescriptorSupplier {
    KeyValueServiceFileDescriptorSupplier() {}
  }

  private static final class KeyValueServiceMethodDescriptorSupplier
      extends KeyValueServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    KeyValueServiceMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (KeyValueServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new KeyValueServiceFileDescriptorSupplier())
              .addMethod(getAuthenticateMethod())
              .addMethod(getPingMethod())
              .addMethod(getHealthCheckMethod())
              .addMethod(getSetMethod())
              .addMethod(getGetMethod())
              .addMethod(getDeleteMethod())
              .addMethod(getSetExMethod())
              .addMethod(getSetExpiryMethod())
              .addMethod(getIncrByMethod())
              .addMethod(getDecrByMethod())
              .addMethod(getIncrByFloatMethod())
              .addMethod(getHSetMethod())
              .addMethod(getHGetMethod())
              .addMethod(getHGetAllMethod())
              .addMethod(getHIncrByMethod())
              .addMethod(getHDecrByMethod())
              .addMethod(getHIncrByFloatMethod())
              .addMethod(getHMSetMethod())
              .addMethod(getHExistsMethod())
              .addMethod(getSetNXMethod())
              .addMethod(getExistsMethod())
              .addMethod(getLoadScriptMethod())
              .addMethod(getEvalShaMethod())
              .addMethod(getBatchWriteMethod())
              .build();
        }
      }
    }
    return result;
  }
}

package org.npci.rustyclient.client.connection;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.OperationType;
import rustycluster.KeyValueServiceGrpc;
import rustycluster.Rustycluster;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Test to verify that the timeout fix works correctly.
 * This test ensures that deadlines are set per-operation rather than per-stub.
 */
@ExtendWith(MockitoExtension.class)
class TimeoutFixTest {

    @Mock
    private ConnectionPool connectionPool;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStub;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStubWithDeadline;

    private ConnectionManager connectionManager;
    private RustyClusterClientConfig config;

    @BeforeEach
    void setUp() {
        config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .connectionTimeout(5, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .writeTimeout(2, TimeUnit.SECONDS)
                .maxRetries(1)
                .retryDelay(100, TimeUnit.MILLISECONDS)
                .build();

        connectionManager = new ConnectionManager(config, connectionPool);
    }

    @Test
    @DisplayName("Should apply read timeout for read operations")
    void shouldApplyReadTimeoutForReadOperations() throws Exception {
        // Given
        when(connectionPool.borrowStub(any())).thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(eq(3000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);
        
        Rustycluster.GetResponse expectedResponse = Rustycluster.GetResponse.newBuilder()
                .setValue("test-value")
                .setFound(true)
                .build();
        when(mockStubWithDeadline.get(any(Rustycluster.GetRequest.class)))
                .thenReturn(expectedResponse);

        // When
        Rustycluster.GetResponse response = connectionManager.executeWithFailover(
                stub -> stub.get(Rustycluster.GetRequest.newBuilder().setKey("test").build()),
                OperationType.READ
        );

        // Then
        assertThat(response).isEqualTo(expectedResponse);
        verify(mockStub).withDeadlineAfter(3000L, TimeUnit.MILLISECONDS);
        verify(mockStubWithDeadline).get(any(Rustycluster.GetRequest.class));
    }

    @Test
    @DisplayName("Should apply write timeout for write operations")
    void shouldApplyWriteTimeoutForWriteOperations() throws Exception {
        // Given
        when(connectionPool.borrowStub(any())).thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(eq(2000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);
        
        Rustycluster.SetResponse expectedResponse = Rustycluster.SetResponse.newBuilder()
                .setSuccess(true)
                .build();
        when(mockStubWithDeadline.set(any(Rustycluster.SetRequest.class)))
                .thenReturn(expectedResponse);

        // When
        Rustycluster.SetResponse response = connectionManager.executeWithFailover(
                stub -> stub.set(Rustycluster.SetRequest.newBuilder()
                        .setKey("test")
                        .setValue("value")
                        .build()),
                OperationType.WRITE
        );

        // Then
        assertThat(response).isEqualTo(expectedResponse);
        verify(mockStub).withDeadlineAfter(2000L, TimeUnit.MILLISECONDS);
        verify(mockStubWithDeadline).set(any(Rustycluster.SetRequest.class));
    }

    @Test
    @DisplayName("Should apply connection timeout for auth operations")
    void shouldApplyConnectionTimeoutForAuthOperations() throws Exception {
        // Given
        when(connectionPool.borrowStub(any())).thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(eq(5000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);
        
        Rustycluster.AuthenticateResponse expectedResponse = Rustycluster.AuthenticateResponse.newBuilder()
                .setSuccess(true)
                .setSessionToken("test-token")
                .build();
        when(mockStubWithDeadline.authenticate(any(Rustycluster.AuthenticateRequest.class)))
                .thenReturn(expectedResponse);

        // When
        Rustycluster.AuthenticateResponse response = connectionManager.executeWithFailover(
                stub -> stub.authenticate(Rustycluster.AuthenticateRequest.newBuilder()
                        .setUsername("test")
                        .setPassword("pass")
                        .build()),
                OperationType.AUTH
        );

        // Then
        assertThat(response).isEqualTo(expectedResponse);
        verify(mockStub).withDeadlineAfter(5000L, TimeUnit.MILLISECONDS);
        verify(mockStubWithDeadline).authenticate(any(Rustycluster.AuthenticateRequest.class));
    }
}

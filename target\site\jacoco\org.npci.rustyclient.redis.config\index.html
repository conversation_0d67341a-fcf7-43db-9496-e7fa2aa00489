<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.redis.config</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.redis.config</span></div><h1>org.npci.rustyclient.redis.config</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">102 of 445</td><td class="ctr2">77%</td><td class="bar">6 of 17</td><td class="ctr2">64%</td><td class="ctr1">23</td><td class="ctr2">57</td><td class="ctr1">28</td><td class="ctr2">115</td><td class="ctr1">17</td><td class="ctr2">48</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a1"><a href="RedisClientConfig$Builder.html" class="el_class">RedisClientConfig.Builder</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="74" alt="74"/><img src="../jacoco-resources/greenbar.gif" width="86" height="10" title="190" alt="190"/></td><td class="ctr2" id="c3">71%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="83" height="10" title="9" alt="9"/></td><td class="ctr2" id="e0">69%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">29</td><td class="ctr1" id="h0">19</td><td class="ctr2" id="i0">66</td><td class="ctr1" id="j0">8</td><td class="ctr2" id="k0">22</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="RedisClientConfig.html" class="el_class">RedisClientConfig</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="103" alt="103"/></td><td class="ctr2" id="c2">80%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">23</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i1">39</td><td class="ctr1" id="j1">8</td><td class="ctr2" id="k1">21</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="RedisNodeRole.html" class="el_class">RedisNodeRole</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="32" alt="32"/></td><td class="ctr2" id="c1">91%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">3</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a2"><a href="RedisNodeConfig.html" class="el_class">RedisNodeConfig</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="18" alt="18"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">2</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
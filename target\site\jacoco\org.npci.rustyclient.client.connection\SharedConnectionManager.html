<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SharedConnectionManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_class">SharedConnectionManager</span></div><h1>SharedConnectionManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">337 of 337</td><td class="ctr2">0%</td><td class="bar">18 of 18</td><td class="ctr2">0%</td><td class="ctr1">23</td><td class="ctr2">23</td><td class="ctr1">77</td><td class="ctr2">77</td><td class="ctr1">14</td><td class="ctr2">14</td></tr></tfoot><tbody><tr><td id="a2"><a href="SharedConnectionManager.java.html#L80" class="el_method">executeWithFailover(SharedConnectionManager.BlockingOperation, SharedConnectionManager.OperationType)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="128" alt="128"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h0">26</td><td class="ctr2" id="i0">26</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a11"><a href="SharedConnectionManager.java.html#L37" class="el_method">SharedConnectionManager(RustyClusterClientConfig, AuthenticationManager)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="54" alt="54"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h1">11</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="SharedConnectionManager.java.html#L175" class="el_method">findNextAvailableNode(NodeConfig)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="30" alt="30"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h3">6</td><td class="ctr2" id="i3">6</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="SharedConnectionManager.java.html#L146" class="el_method">executeWithFailoverAsync(SharedConnectionManager.AsyncOperation, SharedConnectionManager.OperationType)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="29" alt="29"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h5">5</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="SharedConnectionManager.java.html#L153" class="el_method">lambda$executeWithFailoverAsync$2(long, SharedConnectionManager.AsyncOperation, SharedConnectionPool.SharedConnectionWrapper)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="24" alt="24"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h2">8</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="SharedConnectionManager.java.html#L199" class="el_method">close()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="16" alt="16"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a13"><a href="SharedConnectionManager.java.html#L186" class="el_method">toCompletableFuture(ListenableFuture)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="12" alt="12"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a10"><a href="SharedConnectionManager.java.html#L189" class="el_method">lambda$toCompletableFuture$4(CompletableFuture, ListenableFuture)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="12" alt="12"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a8"><a href="SharedConnectionManager.java.html#L166" class="el_method">lambda$executeWithFailoverAsync$3(NodeConfig, Throwable)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="11" alt="11"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="SharedConnectionManager.java.html#L65" class="el_method">executeWithFailover(SharedConnectionManager.BlockingOperation)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="SharedConnectionManager.java.html#L134" class="el_method">executeWithFailoverAsync(SharedConnectionManager.AsyncOperation)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="SharedConnectionManager.java.html#L43" class="el_method">lambda$new$0(NodeConfig)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a12"><a href="SharedConnectionManager.java.html#L22" class="el_method">static {...}</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a6"><a href="SharedConnectionManager.java.html#L158" class="el_method">lambda$executeWithFailoverAsync$1(SharedConnectionPool.SharedConnectionWrapper, Object, Throwable)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
package org.npci.rustyclient.client.config;

/**
 * Configuration for a RustyCluster node.
 *
 * @param host The host address of the node
 * @param port The port number of the node
 * @param role The role of the node
 */
public record NodeConfig(String host, int port, NodeRole role) {
    /**
     * Get the address of the node in the format "host:port".
     *
     * @return The node address
     */
    public String getAddress() {
        return host + ":" + port;
    }
}

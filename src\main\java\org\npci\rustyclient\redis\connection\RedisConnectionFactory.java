package org.npci.rustyclient.redis.connection;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

/**
 * Factory for creating Redis connections using Jedis.
 */
public class RedisConnectionFactory extends BasePooledObjectFactory<Jedis> {
    private static final Logger logger = LoggerFactory.getLogger(RedisConnectionFactory.class);

    private final RedisNodeConfig nodeConfig;
    private final RedisClientConfig clientConfig;

    /**
     * Create a new RedisConnectionFactory.
     *
     * @param nodeConfig   The node configuration
     * @param clientConfig The client configuration
     */
    public RedisConnectionFactory(RedisNodeConfig nodeConfig, RedisClientConfig clientConfig) {
        this.nodeConfig = nodeConfig;
        this.clientConfig = clientConfig;
    }

    @Override
    public Jedis create() throws Exception {
        logger.debug("Creating new Redis connection to {}", nodeConfig.getAddress());

        // Create Jedis with timeout configuration
        Jedis jedis = new Jedis(nodeConfig.host(), nodeConfig.port(),
                               (int) clientConfig.getConnectionTimeoutMs(),
                               (int) clientConfig.getReadTimeoutMs());

        // Configure SSL if enabled
        if (clientConfig.isUseSecureConnection()) {
            // For SSL, we need to create with SSL configuration
            // This would require using JedisPoolConfig or custom SSL setup
            logger.debug("SSL configuration requested for: {}", nodeConfig.getAddress());
            if (clientConfig.getTlsCertPath() != null) {
                logger.debug("Using custom SSL configuration with cert: {}", clientConfig.getTlsCertPath());
            }
        }

        try {
            // Authenticate if credentials are provided
            if (clientConfig.hasAuthentication()) {
                if (clientConfig.getUsername() != null) {
                    // Redis 6+ ACL authentication
                    jedis.auth(clientConfig.getUsername(), clientConfig.getPassword());
                } else {
                    // Legacy password authentication
                    jedis.auth(clientConfig.getPassword());
                }
                logger.debug("Authenticated with Redis server: {}", nodeConfig.getAddress());
            }

            // Select database
            if (clientConfig.getDatabase() != 0) {
                jedis.select(clientConfig.getDatabase());
                logger.debug("Selected database {} for {}", clientConfig.getDatabase(), nodeConfig.getAddress());
            }

            // Test with ping
            String response = jedis.ping();
            if (!"PONG".equals(response)) {
                throw new JedisException("Unexpected ping response: " + response);
            }

            logger.debug("Successfully created Redis connection to {}", nodeConfig.getAddress());
            return jedis;

        } catch (Exception e) {
            logger.error("Failed to create Redis connection to {}", nodeConfig.getAddress(), e);
            // Clean up the connection if it was partially created
            try {
                jedis.close();
            } catch (Exception closeException) {
                logger.debug("Error closing failed connection", closeException);
            }
            throw e;
        }
    }

    @Override
    public PooledObject<Jedis> wrap(Jedis jedis) {
        return new DefaultPooledObject<>(jedis);
    }

    @Override
    public boolean validateObject(PooledObject<Jedis> pooledObject) {
        Jedis jedis = pooledObject.getObject();
        if (jedis == null) {
            return false;
        }

        try {
            // Check if connection is still alive
            if (!jedis.isConnected()) {
                return false;
            }

            // Ping test
            String response = jedis.ping();
            return "PONG".equals(response);
        } catch (Exception e) {
            logger.debug("Connection validation failed for {}", nodeConfig.getAddress(), e);
            return false;
        }
    }

    @Override
    public void destroyObject(PooledObject<Jedis> pooledObject) throws Exception {
        Jedis jedis = pooledObject.getObject();
        if (jedis != null) {
            try {
                // In Jedis 5.x, just close the connection directly
                jedis.close();
            } catch (Exception e) {
                logger.debug("Error closing connection", e);
            }
        }
        logger.debug("Destroyed Redis connection to {}", nodeConfig.getAddress());
    }

    @Override
    public void activateObject(PooledObject<Jedis> pooledObject) throws Exception {
        Jedis jedis = pooledObject.getObject();
        if (jedis != null && !jedis.isConnected()) {
            // In Jedis 5.x, connection is automatically managed
            // Re-authenticate if needed
            if (clientConfig.hasAuthentication()) {
                if (clientConfig.getUsername() != null) {
                    jedis.auth(clientConfig.getUsername(), clientConfig.getPassword());
                } else {
                    jedis.auth(clientConfig.getPassword());
                }
            }

            // Re-select database if needed
            if (clientConfig.getDatabase() != 0) {
                jedis.select(clientConfig.getDatabase());
            }
        }
    }

    @Override
    public void passivateObject(PooledObject<Jedis> pooledObject) throws Exception {
        // Reset connection state if needed
        Jedis jedis = pooledObject.getObject();
        if (jedis != null && jedis.isConnected()) {
            // Reset to default database if we're not using database 0
            if (clientConfig.getDatabase() != 0) {
                try {
                    jedis.select(0);
                } catch (Exception e) {
                    logger.debug("Failed to reset database to 0", e);
                }
            }
        }
    }
}

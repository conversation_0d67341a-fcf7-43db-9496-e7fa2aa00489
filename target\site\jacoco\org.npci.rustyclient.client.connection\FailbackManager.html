<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FailbackManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_class">FailbackManager</span></div><h1>FailbackManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">49 of 375</td><td class="ctr2">86%</td><td class="bar">8 of 40</td><td class="ctr2">80%</td><td class="ctr1">8</td><td class="ctr2">30</td><td class="ctr1">19</td><td class="ctr2">103</td><td class="ctr1">0</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a6"><a href="FailbackManager.java.html#L205" class="el_method">performHealthCheck(NodeConfig)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="57" alt="57"/></td><td class="ctr2" id="c8">75%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="5" alt="5"/></td><td class="ctr2" id="e1">83%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h0">5</td><td class="ctr2" id="i0">23</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="FailbackManager.java.html#L81" class="el_method">stop()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="17" alt="17"/></td><td class="ctr2" id="c9">60%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="1" alt="1"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="FailbackManager.java.html#L98" class="el_method">checkForFailback()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="99" height="10" title="67" alt="67"/></td><td class="ctr2" id="c7">89%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="7" alt="7"/></td><td class="ctr2" id="e4">70%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="FailbackManager.java.html#L166" class="el_method">isNodeHealthy(NodeConfig)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="112" height="10" title="76" alt="76"/></td><td class="ctr2" id="c5">93%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="FailbackManager.java.html#L56" class="el_method">start()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="35" alt="35"/></td><td class="ctr2" id="c6">89%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="3" alt="3"/></td><td class="ctr2" id="e2">75%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="FailbackManager.java.html#L139" class="el_method">findBestAvailableNode()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="34" alt="34"/></td><td class="ctr2" id="c4">94%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">75%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a2"><a href="FailbackManager.java.html#L27" class="el_method">FailbackManager(RustyClusterClientConfig, ConnectionPool, List, AtomicReference)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="22" alt="22"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="FailbackManager.java.html#L46" class="el_method">lambda$new$0(Runnable)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="11" alt="11"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a8"><a href="FailbackManager.java.html#L20" class="el_method">static {...}</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="FailbackManager.java.html#L248" class="el_method">close()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
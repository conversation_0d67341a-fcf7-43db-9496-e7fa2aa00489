#!/usr/bin/env python3
"""
Script to generate basic gRPC message classes for RustyCluster.
This is a temporary solution until proper protobuf compilation is set up.
"""

import os

# Define the message classes we need to create
messages = [
    # Get operations
    ("GetRequest", [("key", "String", "")]),
    ("GetResponse", [("value", "String", ""), ("found", "boolean", "false")]),
    
    # Delete operations
    ("DeleteRequest", [("key", "String", ""), ("skipReplication", "boolean", "false")]),
    ("DeleteResponse", [("success", "boolean", "false")]),
    
    # SetEx operations
    ("SetExRequest", [("key", "String", ""), ("value", "String", ""), ("ttl", "long", "0"), ("skipReplication", "boolean", "false")]),
    ("SetExResponse", [("success", "boolean", "false")]),
    
    # SetExpiry operations
    ("SetExpiryRequest", [("key", "String", ""), ("ttl", "long", "0"), ("skipReplication", "boolean", "false")]),
    ("SetExpiryResponse", [("success", "boolean", "false")]),
    
    # Numeric operations
    ("IncrByRequest", [("key", "String", ""), ("value", "long", "0"), ("skipReplication", "boolean", "false")]),
    ("IncrByResponse", [("newValue", "long", "0")]),
    ("DecrByRequest", [("key", "String", ""), ("value", "long", "0"), ("skipReplication", "boolean", "false")]),
    ("DecrByResponse", [("newValue", "long", "0")]),
    ("IncrByFloatRequest", [("key", "String", ""), ("value", "double", "0.0"), ("skipReplication", "boolean", "false")]),
    ("IncrByFloatResponse", [("newValue", "double", "0.0")]),
    
    # Hash operations
    ("HSetRequest", [("key", "String", ""), ("field", "String", ""), ("value", "String", ""), ("skipReplication", "boolean", "false")]),
    ("HSetResponse", [("success", "boolean", "false")]),
    ("HGetRequest", [("key", "String", ""), ("field", "String", "")]),
    ("HGetResponse", [("value", "String", ""), ("found", "boolean", "false")]),
    ("HGetAllRequest", [("key", "String", "")]),
    ("HIncrByRequest", [("key", "String", ""), ("field", "String", ""), ("value", "long", "0"), ("skipReplication", "boolean", "false")]),
    ("HIncrByResponse", [("newValue", "long", "0")]),
    ("HDecrByRequest", [("key", "String", ""), ("field", "String", ""), ("value", "long", "0"), ("skipReplication", "boolean", "false")]),
    ("HDecrByResponse", [("newValue", "long", "0")]),
    ("HIncrByFloatRequest", [("key", "String", ""), ("field", "String", ""), ("value", "double", "0.0"), ("skipReplication", "boolean", "false")]),
    ("HIncrByFloatResponse", [("newValue", "double", "0.0")]),
]

def generate_class(class_name, fields):
    """Generate a Java class for a protobuf message."""
    
    # Generate field declarations
    field_declarations = []
    for field_name, field_type, default_value in fields:
        field_declarations.append(f"    private final {field_type} {field_name};")
    
    # Generate constructor assignments
    constructor_assignments = []
    for field_name, field_type, default_value in fields:
        constructor_assignments.append(f"        this.{field_name} = builder.{field_name};")
    
    # Generate getters
    getters = []
    for field_name, field_type, default_value in fields:
        getter_name = f"get{field_name[0].upper()}{field_name[1:]}"
        getters.append(f"""    public {field_type} {getter_name}() {{
        return {field_name};
    }}""")
    
    # Generate builder field declarations
    builder_fields = []
    for field_name, field_type, default_value in fields:
        builder_fields.append(f"        private {field_type} {field_name} = {default_value};")
    
    # Generate builder setters
    builder_setters = []
    for field_name, field_type, default_value in fields:
        setter_name = f"set{field_name[0].upper()}{field_name[1:]}"
        builder_setters.append(f"""        public Builder {setter_name}({field_type} {field_name}) {{
            this.{field_name} = {field_name};
            return this;
        }}""")
    
    # Generate the complete class
    class_content = f"""package com.rustycluster.grpc;

/**
 * Generated protobuf message for {class_name}.
 */
public final class {class_name} {{
{chr(10).join(field_declarations)}

    private {class_name}(Builder builder) {{
{chr(10).join(constructor_assignments)}
    }}

    public static Builder newBuilder() {{
        return new Builder();
    }}

{chr(10).join(getters)}

    public static final class Builder {{
{chr(10).join(builder_fields)}

{chr(10).join(builder_setters)}

        public {class_name} build() {{
            return new {class_name}(this);
        }}
    }}
}}"""
    
    return class_content

def main():
    # Create the grpc package directory if it doesn't exist
    grpc_dir = "src/main/java/com/rustycluster/grpc"
    os.makedirs(grpc_dir, exist_ok=True)
    
    # Generate all message classes
    for class_name, fields in messages:
        class_content = generate_class(class_name, fields)
        file_path = os.path.join(grpc_dir, f"{class_name}.java")
        
        with open(file_path, 'w') as f:
            f.write(class_content)
        
        print(f"Generated {file_path}")

if __name__ == "__main__":
    main()

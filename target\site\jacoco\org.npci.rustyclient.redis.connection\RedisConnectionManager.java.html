<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RedisConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.redis.connection</a> &gt; <span class="el_source">RedisConnectionManager.java</span></div><h1>RedisConnectionManager.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.redis.connection;

import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeConfig;
import org.npci.rustyclient.redis.exception.NoAvailableRedisNodesException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages Redis connections, handling prioritization and failover.
 */
public class RedisConnectionManager implements AutoCloseable {
<span class="fc" id="L18">    private static final Logger logger = LoggerFactory.getLogger(RedisConnectionManager.class);</span>

    private final RedisClientConfig config;
    private final RedisConnectionPool connectionPool;
    private final AtomicReference&lt;RedisNodeConfig&gt; currentNode;
    private final List&lt;RedisNodeConfig&gt; sortedNodes;

    /**
     * Create a new RedisConnectionManager.
     *
     * @param config The Redis client configuration
     */
<span class="fc" id="L30">    public RedisConnectionManager(RedisClientConfig config) {</span>
<span class="fc" id="L31">        this.config = config;</span>
<span class="fc" id="L32">        this.connectionPool = new RedisConnectionPool(config);</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="fc" id="L35">        this.sortedNodes = config.getNodes().stream()</span>
<span class="pc" id="L36">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="fc" id="L37">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="fc" id="L40">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

<span class="fc" id="L42">        logger.info(&quot;RedisConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="fc" id="L43">    }</span>

    /**
     * Execute an operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableRedisNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(RedisOperation&lt;T&gt; operation) throws NoAvailableRedisNodesException {
<span class="nc" id="L54">        return executeWithFailover(operation, RedisOperationType.READ);</span>
    }

    /**
     * Execute an operation with automatic failover and operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ/WRITE)
     * @param &lt;T&gt;           The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableRedisNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(RedisOperation&lt;T&gt; operation, RedisOperationType operationType) 
            throws NoAvailableRedisNodesException {
        
<span class="nc" id="L69">        RedisNodeConfig node = currentNode.get();</span>
        
<span class="nc bnc" id="L71" title="All 2 branches missed.">        for (int attempt = 0; attempt &lt;= config.getMaxRetries(); attempt++) {</span>
<span class="nc" id="L72">            Jedis connection = null;</span>
            
            try {
<span class="nc" id="L75">                connection = connectionPool.borrowConnection(node);</span>
<span class="nc" id="L76">                T result = operation.execute(connection);</span>
                
<span class="nc" id="L78">                logger.debug(&quot;Operation executed successfully on node: {} (attempt {})&quot;, node, attempt + 1);</span>
<span class="nc" id="L79">                return result;</span>
                
<span class="nc" id="L81">            } catch (Exception e) {</span>
<span class="nc" id="L82">                logger.warn(&quot;Operation failed on node: {} (attempt {}): {}&quot;, node, attempt + 1, e.getMessage());</span>
                
                // Invalidate the connection
<span class="nc bnc" id="L85" title="All 2 branches missed.">                if (connection != null) {</span>
<span class="nc" id="L86">                    connectionPool.invalidateConnection(node, connection);</span>
<span class="nc" id="L87">                    connection = null;</span>
                }
                
<span class="nc bnc" id="L90" title="All 2 branches missed.">                if (attempt &lt; config.getMaxRetries()) {</span>
                    try {
<span class="nc" id="L92">                        Thread.sleep(config.getRetryDelayMs());</span>
<span class="nc" id="L93">                    } catch (InterruptedException ie) {</span>
<span class="nc" id="L94">                        Thread.currentThread().interrupt();</span>
<span class="nc" id="L95">                        throw new RuntimeException(&quot;Interrupted during retry delay&quot;, ie);</span>
<span class="nc" id="L96">                    }</span>
                } else {
                    // Try to find the next available node
<span class="nc" id="L99">                    var nextNode = findNextAvailableNode(node);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">                    if (nextNode != null) {</span>
<span class="nc" id="L101">                        currentNode.set(nextNode);</span>
<span class="nc" id="L102">                        logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>
                    } else {
<span class="nc" id="L104">                        logger.warn(&quot;No available nodes found after failure&quot;);</span>
                    }
                }
            } finally {
                // Return connection to pool if it's still valid
<span class="nc bnc" id="L109" title="All 2 branches missed.">                if (connection != null) {</span>
<span class="nc" id="L110">                    connectionPool.returnConnection(node, connection);</span>
                }
            }
        }
        
<span class="nc" id="L115">        throw new NoAvailableRedisNodesException(&quot;All Redis nodes are unavailable after retries&quot;);</span>
    }

    /**
     * Execute an operation with failover but return null on failure (for read operations).
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation, or null if all nodes are unavailable
     */
    public &lt;T&gt; T executeWithFailoverSilent(RedisOperation&lt;T&gt; operation) {
        try {
<span class="nc" id="L127">            return executeWithFailover(operation, RedisOperationType.READ);</span>
<span class="nc" id="L128">        } catch (NoAvailableRedisNodesException e) {</span>
<span class="nc" id="L129">            logger.warn(&quot;All Redis nodes unavailable for read operation, returning null&quot;);</span>
<span class="nc" id="L130">            return null;</span>
        }
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none found
     */
    private RedisNodeConfig findNextAvailableNode(RedisNodeConfig failedNode) {
<span class="nc" id="L141">        logger.debug(&quot;Looking for next available node after failure of: {}&quot;, failedNode);</span>
        
<span class="nc bnc" id="L143" title="All 2 branches missed.">        for (RedisNodeConfig node : sortedNodes) {</span>
<span class="nc bnc" id="L144" title="All 4 branches missed.">            if (!node.equals(failedNode) &amp;&amp; isNodeHealthy(node)) {</span>
<span class="nc" id="L145">                logger.debug(&quot;Found available node: {}&quot;, node);</span>
<span class="nc" id="L146">                return node;</span>
            }
<span class="nc" id="L148">        }</span>
        
<span class="nc" id="L150">        logger.warn(&quot;No available nodes found&quot;);</span>
<span class="nc" id="L151">        return null;</span>
    }

    /**
     * Check if a node is healthy.
     *
     * @param node The node to check
     * @return True if the node is healthy
     */
    private boolean isNodeHealthy(RedisNodeConfig node) {
<span class="nc" id="L161">        Jedis connection = null;</span>
        try {
<span class="nc" id="L163">            connection = connectionPool.borrowConnection(node);</span>
<span class="nc" id="L164">            String response = connection.ping();</span>
<span class="nc" id="L165">            boolean healthy = &quot;PONG&quot;.equals(response);</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">            logger.debug(&quot;Health check for node {}: {}&quot;, node, healthy ? &quot;HEALTHY&quot; : &quot;UNHEALTHY&quot;);</span>
<span class="nc" id="L167">            return healthy;</span>
<span class="nc" id="L168">        } catch (Exception e) {</span>
<span class="nc" id="L169">            logger.debug(&quot;Health check failed for node {}: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L170">            return false;</span>
        } finally {
<span class="nc bnc" id="L172" title="All 2 branches missed.">            if (connection != null) {</span>
<span class="nc" id="L173">                connectionPool.returnConnection(node, connection);</span>
            }
        }
    }

    /**
     * Get the current active node.
     *
     * @return The current active node
     */
    public RedisNodeConfig getCurrentNode() {
<span class="nc" id="L184">        return currentNode.get();</span>
    }

    /**
     * Get all configured nodes.
     *
     * @return List of all nodes
     */
    public List&lt;RedisNodeConfig&gt; getAllNodes() {
<span class="nc" id="L193">        return sortedNodes;</span>
    }

    /**
     * Get connection pool statistics.
     *
     * @return Pool statistics for all nodes
     */
    public String getPoolStats() {
<span class="nc" id="L202">        StringBuilder stats = new StringBuilder(&quot;Redis Connection Pool Statistics:\n&quot;);</span>
<span class="nc bnc" id="L203" title="All 2 branches missed.">        for (RedisNodeConfig node : sortedNodes) {</span>
<span class="nc" id="L204">            stats.append(connectionPool.getPoolStats(node)).append(&quot;\n&quot;);</span>
<span class="nc" id="L205">        }</span>
<span class="nc" id="L206">        return stats.toString();</span>
    }

    /**
     * Perform a health check on all nodes.
     *
     * @return True if at least one node is healthy
     */
    public boolean healthCheck() {
<span class="nc" id="L215">        logger.debug(&quot;Performing health check on all Redis nodes&quot;);</span>
        
<span class="nc bnc" id="L217" title="All 2 branches missed.">        for (RedisNodeConfig node : sortedNodes) {</span>
<span class="nc bnc" id="L218" title="All 2 branches missed.">            if (isNodeHealthy(node)) {</span>
<span class="nc" id="L219">                logger.debug(&quot;Health check passed - at least one node is healthy&quot;);</span>
<span class="nc" id="L220">                return true;</span>
            }
<span class="nc" id="L222">        }</span>
        
<span class="nc" id="L224">        logger.warn(&quot;Health check failed - no healthy nodes found&quot;);</span>
<span class="nc" id="L225">        return false;</span>
    }

    @Override
    public void close() {
<span class="fc" id="L230">        logger.info(&quot;Closing RedisConnectionManager&quot;);</span>
<span class="fc" id="L231">        connectionPool.close();</span>
<span class="fc" id="L232">        logger.info(&quot;RedisConnectionManager closed&quot;);</span>
<span class="fc" id="L233">    }</span>

    /**
     * Functional interface for Redis operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface RedisOperation&lt;T&gt; {
        T execute(Jedis connection) throws Exception;
    }

    /**
     * Enum for Redis operation types.
     */
<span class="nc" id="L248">    public enum RedisOperationType {</span>
<span class="nc" id="L249">        READ, WRITE</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
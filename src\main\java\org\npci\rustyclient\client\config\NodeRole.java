package org.npci.rustyclient.client.config;

/**
 * Enum representing the role of a node in the RustyCluster.
 */
public enum NodeRole {
    /**
     * Primary node - first choice for operations.
     */
    PRIMARY(1),

    /**
     * Secondary node - used when primary is unavailable.
     */
    SECONDARY(2),

    /**
     * Tertiary node - used when primary and secondary are unavailable.
     */
    TERTIARY(3);

    private final int priority;

    NodeRole(int priority) {
        this.priority = priority;
    }

    /**
     * Get the priority of the node role.
     * Lower number means higher priority.
     *
     * @return The priority value
     */
    public int getPriority() {
        return priority;
    }
}

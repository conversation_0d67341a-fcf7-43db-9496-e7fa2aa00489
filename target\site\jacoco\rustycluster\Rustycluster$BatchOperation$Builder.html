<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Rustycluster.BatchOperation.Builder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">rustycluster</a> &gt; <span class="el_class">Rustycluster.BatchOperation.Builder</span></div><h1>Rustycluster.BatchOperation.Builder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,458 of 1,705</td><td class="ctr2">14%</td><td class="bar">139 of 161</td><td class="ctr2">13%</td><td class="ctr1">166</td><td class="ctr2">182</td><td class="ctr1">403</td><td class="ctr2">479</td><td class="ctr1">85</td><td class="ctr2">96</td></tr></tfoot><tbody><tr><td id="a71"><a href="Rustycluster.java.html#L29571" class="el_method">mergeFrom(Rustycluster.BatchOperation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="177" alt="177"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="26" alt="26"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f1">14</td><td class="ctr2" id="g1">14</td><td class="ctr1" id="h1">45</td><td class="ctr2" id="i1">45</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a69"><a href="Rustycluster.java.html#L29642" class="el_method">mergeFrom(CodedInputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="115" height="10" title="170" alt="170"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="19" alt="19"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f0">16</td><td class="ctr2" id="g0">16</td><td class="ctr1" id="h0">53</td><td class="ctr2" id="i0">53</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="Rustycluster.java.html#L29435" class="el_method">clear()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="41" alt="41"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i3">16</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="Rustycluster.java.html#L29482" class="el_method">buildPartial0(Rustycluster.BatchOperation)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="29" alt="29"/><img src="../jacoco-resources/greenbar.gif" width="67" height="10" title="100" alt="100"/></td><td class="ctr2" id="c9">77%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="73" height="10" title="16" alt="16"/></td><td class="ctr2" id="e0">72%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i2">34</td><td class="ctr1" id="j85">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a74"><a href="Rustycluster.java.html#L30332" class="el_method">putHashFields(String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="29" alt="29"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h16">6</td><td class="ctr2" id="i17">6</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a66"><a href="Rustycluster.java.html#L30211" class="el_method">internalGetMutableHashFields()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="27" alt="27"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a77"><a href="Rustycluster.java.html#L29430" class="el_method">Rustycluster.BatchOperation.Builder(GeneratedMessageV3.BuilderParent)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="25" alt="25"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a38"><a href="Rustycluster.java.html#L30285" class="el_method">getHashFieldsOrThrow(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="24" alt="24"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h17">6</td><td class="ctr2" id="i18">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a89"><a href="Rustycluster.java.html#L30423" class="el_method">setScriptKeys(int, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="24" alt="24"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h18">6</td><td class="ctr2" id="i19">6</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a6"><a href="Rustycluster.java.html#L30492" class="el_method">addScriptKeysBytes(ByteString)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="24" alt="24"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a88"><a href="Rustycluster.java.html#L30570" class="el_method">setScriptArgs(int, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="24" alt="24"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h19">6</td><td class="ctr2" id="i20">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a4"><a href="Rustycluster.java.html#L30639" class="el_method">addScriptArgsBytes(ByteString)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="24" alt="24"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a5"><a href="Rustycluster.java.html#L30441" class="el_method">addScriptKeys(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="23" alt="23"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h20">6</td><td class="ctr2" id="i21">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a3"><a href="Rustycluster.java.html#L30588" class="el_method">addScriptArgs(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="23" alt="23"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h21">6</td><td class="ctr2" id="i22">6</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a37"><a href="Rustycluster.java.html#L30270" class="el_method">getHashFieldsOrDefault(String, String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="22" alt="22"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h31">4</td><td class="ctr2" id="i36">4</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a84"><a href="Rustycluster.java.html#L29848" class="el_method">setKeyBytes(ByteString)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="21" alt="21"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h22">6</td><td class="ctr2" id="i23">6</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a95"><a href="Rustycluster.java.html#L29920" class="el_method">setValueBytes(ByteString)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="21" alt="21"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h23">6</td><td class="ctr2" id="i24">6</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a80"><a href="Rustycluster.java.html#L30023" class="el_method">setFieldBytes(ByteString)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="21" alt="21"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h24">6</td><td class="ctr2" id="i25">6</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a91"><a href="Rustycluster.java.html#L30743" class="el_method">setScriptShaBytes(ByteString)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="21" alt="21"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h25">6</td><td class="ctr2" id="i26">6</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a40"><a href="Rustycluster.java.html#L29790" class="el_method">getKey()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="20" alt="20"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g17">2</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a56"><a href="Rustycluster.java.html#L29862" class="el_method">getValue()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="20" alt="20"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h9">7</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a31"><a href="Rustycluster.java.html#L29949" class="el_method">getField()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="20" alt="20"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h10">7</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a53"><a href="Rustycluster.java.html#L30669" class="el_method">getScriptSha()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="20" alt="20"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h11">7</td><td class="ctr2" id="i12">7</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a90"><a href="Rustycluster.java.html#L30712" class="el_method">setScriptSha(String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="19" alt="19"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h26">5</td><td class="ctr2" id="i28">5</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a41"><a href="Rustycluster.java.html#L29807" class="el_method">getKeyBytes()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="18" alt="18"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h12">7</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a57"><a href="Rustycluster.java.html#L29879" class="el_method">getValueBytes()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="18" alt="18"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h13">7</td><td class="ctr2" id="i14">7</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a32"><a href="Rustycluster.java.html#L29970" class="el_method">getFieldBytes()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="18" alt="18"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h14">7</td><td class="ctr2" id="i15">7</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a27"><a href="Rustycluster.java.html#L30357" class="el_method">ensureScriptKeysIsMutable()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="18" alt="18"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h32">4</td><td class="ctr2" id="i37">4</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a26"><a href="Rustycluster.java.html#L30504" class="el_method">ensureScriptArgsIsMutable()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="18" alt="18"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h33">4</td><td class="ctr2" id="i38">4</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a54"><a href="Rustycluster.java.html#L30690" class="el_method">getScriptShaBytes()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="18" alt="18"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g27">2</td><td class="ctr1" id="h15">7</td><td class="ctr2" id="i16">7</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a1"><a href="Rustycluster.java.html#L30459" class="el_method">addAllScriptKeys(Iterable)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h27">5</td><td class="ctr2" id="i29">5</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a0"><a href="Rustycluster.java.html#L30606" class="el_method">addAllScriptArgs(Iterable)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h28">5</td><td class="ctr2" id="i30">5</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a75"><a href="Rustycluster.java.html#L30308" class="el_method">removeHashFields(String)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="15" alt="15"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f28">2</td><td class="ctr2" id="g28">2</td><td class="ctr1" id="h34">4</td><td class="ctr2" id="i39">4</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a70"><a href="Rustycluster.java.html#L29562" class="el_method">mergeFrom(Message)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="14" alt="14"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g29">2</td><td class="ctr1" id="h35">4</td><td class="ctr2" id="i40">4</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a16"><a href="Rustycluster.java.html#L29836" class="el_method">clearKey()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="14" alt="14"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h36">4</td><td class="ctr2" id="i41">4</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a23"><a href="Rustycluster.java.html#L29908" class="el_method">clearValue()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="14" alt="14"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h37">4</td><td class="ctr2" id="i42">4</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a11"><a href="Rustycluster.java.html#L30007" class="el_method">clearField()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="14" alt="14"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h38">4</td><td class="ctr2" id="i43">4</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a21"><a href="Rustycluster.java.html#L30727" class="el_method">clearScriptSha()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="14" alt="14"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h39">4</td><td class="ctr2" id="i44">4</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a86"><a href="Rustycluster.java.html#L29745" class="el_method">setOperationTypeValue(int)</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h40">4</td><td class="ctr2" id="i45">4</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a18"><a href="Rustycluster.java.html#L29778" class="el_method">clearOperationType()</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h41">4</td><td class="ctr2" id="i46">4</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a15"><a href="Rustycluster.java.html#L30081" class="el_method">clearIntValue()</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h42">4</td><td class="ctr2" id="i47">4</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a13"><a href="Rustycluster.java.html#L30137" class="el_method">clearFloatValue()</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h43">4</td><td class="ctr2" id="i48">4</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a22"><a href="Rustycluster.java.html#L30193" class="el_method">clearTtl()</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h44">4</td><td class="ctr2" id="i49">4</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a25"><a href="Rustycluster.java.html#L30235" class="el_method">containsHashFields(String)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f30">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h50">2</td><td class="ctr2" id="i60">2</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a73"><a href="Rustycluster.java.html#L30348" class="el_method">putAllHashFields(Map)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h45">4</td><td class="ctr2" id="i50">4</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a20"><a href="Rustycluster.java.html#L30475" class="el_method">clearScriptKeys()</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h29">5</td><td class="ctr2" id="i31">5</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a19"><a href="Rustycluster.java.html#L30622" class="el_method">clearScriptArgs()</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h30">5</td><td class="ctr2" id="i32">5</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a14"><a href="Rustycluster.java.html#L30294" class="el_method">clearHashFields()</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="12" alt="12"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h46">4</td><td class="ctr2" id="i51">4</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a65"><a href="Rustycluster.java.html#L29396" class="el_method">internalGetMapField(int)</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="11" alt="11"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e37">0%</td><td class="ctr1" id="f31">2</td><td class="ctr2" id="g31">2</td><td class="ctr1" id="h47">3</td><td class="ctr2" id="i57">3</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a67"><a href="Rustycluster.java.html#L29407" class="el_method">internalGetMutableMapField(int)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="11" alt="11"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e38">0%</td><td class="ctr1" id="f32">2</td><td class="ctr2" id="g32">2</td><td class="ctr1" id="h48">3</td><td class="ctr2" id="i58">3</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a43"><a href="Rustycluster.java.html#L29756" class="el_method">getOperationType()</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="10" alt="10"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d33"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e39">0%</td><td class="ctr1" id="f33">2</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h51">2</td><td class="ctr2" id="i61">2</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a42"><a href="Rustycluster.java.html#L30319" class="el_method">getMutableHashFields()</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="10" alt="10"/></td><td class="ctr2" id="c61">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h52">2</td><td class="ctr2" id="i62">2</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a58"><a href="Rustycluster.java.html#L29938" class="el_method">hasField()</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c62">0%</td><td class="bar" id="d34"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e40">0%</td><td class="ctr1" id="f34">2</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h56">1</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a60"><a href="Rustycluster.java.html#L30042" class="el_method">hasIntValue()</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c63">0%</td><td class="bar" id="d35"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e41">0%</td><td class="ctr1" id="f35">2</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h57">1</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a59"><a href="Rustycluster.java.html#L30098" class="el_method">hasFloatValue()</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c64">0%</td><td class="bar" id="d36"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e42">0%</td><td class="ctr1" id="f36">2</td><td class="ctr2" id="g36">2</td><td class="ctr1" id="h58">1</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a62"><a href="Rustycluster.java.html#L30154" class="el_method">hasTtl()</a></td><td class="bar" id="b55"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c65">0%</td><td class="bar" id="d37"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e43">0%</td><td class="ctr1" id="f37">2</td><td class="ctr2" id="g37">2</td><td class="ctr1" id="h59">1</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a64"><a href="Rustycluster.java.html#L30203" class="el_method">internalGetHashFields()</a></td><td class="bar" id="b56"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c66">0%</td><td class="bar" id="d38"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e44">0%</td><td class="ctr1" id="f38">2</td><td class="ctr2" id="g38">2</td><td class="ctr1" id="h49">3</td><td class="ctr2" id="i59">3</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a61"><a href="Rustycluster.java.html#L30658" class="el_method">hasScriptSha()</a></td><td class="bar" id="b57"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c67">0%</td><td class="bar" id="d39"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e45">0%</td><td class="ctr1" id="f39">2</td><td class="ctr2" id="g39">2</td><td class="ctr1" id="h60">1</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a87"><a href="Rustycluster.java.html#L29552" class="el_method">setRepeatedField(Descriptors.FieldDescriptor, int, Object)</a></td><td class="bar" id="b58"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c68">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h61">1</td><td class="ctr2" id="i71">1</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a78"><a href="Rustycluster.java.html#L29536" class="el_method">setField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b59"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c69">0%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h62">1</td><td class="ctr2" id="i72">1</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a2"><a href="Rustycluster.java.html#L29558" class="el_method">addRepeatedField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b60"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c70">0%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h63">1</td><td class="ctr2" id="i73">1</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a52"><a href="Rustycluster.java.html#L30372" class="el_method">getScriptKeysList()</a></td><td class="bar" id="b61"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c71">0%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h53">2</td><td class="ctr2" id="i63">2</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a48"><a href="Rustycluster.java.html#L30519" class="el_method">getScriptArgsList()</a></td><td class="bar" id="b62"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c72">0%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h54">2</td><td class="ctr2" id="i64">2</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a63"><a href="Rustycluster.java.html#L29418" class="el_method">internalGetFieldAccessorTable()</a></td><td class="bar" id="b63"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c73">0%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h55">2</td><td class="ctr2" id="i65">2</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a12"><a href="Rustycluster.java.html#L29541" class="el_method">clearField(Descriptors.FieldDescriptor)</a></td><td class="bar" id="b64"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c74">0%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f64">1</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h64">1</td><td class="ctr2" id="i74">1</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a17"><a href="Rustycluster.java.html#L29546" class="el_method">clearOneof(Descriptors.OneofDescriptor)</a></td><td class="bar" id="b65"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c75">0%</td><td class="bar" id="d71"/><td class="ctr2" id="e71">n/a</td><td class="ctr1" id="f65">1</td><td class="ctr2" id="g71">1</td><td class="ctr1" id="h65">1</td><td class="ctr2" id="i75">1</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a35"><a href="Rustycluster.java.html#L30223" class="el_method">getHashFieldsCount()</a></td><td class="bar" id="b66"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c76">0%</td><td class="bar" id="d72"/><td class="ctr2" id="e72">n/a</td><td class="ctr1" id="f66">1</td><td class="ctr2" id="g72">1</td><td class="ctr1" id="h66">1</td><td class="ctr2" id="i76">1</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k66">1</td></tr><tr><td id="a49"><a href="Rustycluster.java.html#L30396" class="el_method">getScriptKeys(int)</a></td><td class="bar" id="b67"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c77">0%</td><td class="bar" id="d73"/><td class="ctr2" id="e73">n/a</td><td class="ctr1" id="f67">1</td><td class="ctr2" id="g73">1</td><td class="ctr1" id="h67">1</td><td class="ctr2" id="i77">1</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k67">1</td></tr><tr><td id="a50"><a href="Rustycluster.java.html#L30409" class="el_method">getScriptKeysBytes(int)</a></td><td class="bar" id="b68"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c78">0%</td><td class="bar" id="d74"/><td class="ctr2" id="e74">n/a</td><td class="ctr1" id="f68">1</td><td class="ctr2" id="g74">1</td><td class="ctr1" id="h68">1</td><td class="ctr2" id="i78">1</td><td class="ctr1" id="j67">1</td><td class="ctr2" id="k68">1</td></tr><tr><td id="a45"><a href="Rustycluster.java.html#L30543" class="el_method">getScriptArgs(int)</a></td><td class="bar" id="b69"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c79">0%</td><td class="bar" id="d75"/><td class="ctr2" id="e75">n/a</td><td class="ctr1" id="f69">1</td><td class="ctr2" id="g75">1</td><td class="ctr1" id="h69">1</td><td class="ctr2" id="i79">1</td><td class="ctr1" id="j68">1</td><td class="ctr2" id="k69">1</td></tr><tr><td id="a46"><a href="Rustycluster.java.html#L30556" class="el_method">getScriptArgsBytes(int)</a></td><td class="bar" id="b70"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c80">0%</td><td class="bar" id="d76"/><td class="ctr2" id="e76">n/a</td><td class="ctr1" id="f70">1</td><td class="ctr2" id="g76">1</td><td class="ctr1" id="h70">1</td><td class="ctr2" id="i80">1</td><td class="ctr1" id="j69">1</td><td class="ctr2" id="k70">1</td></tr><tr><td id="a93"><a href="Rustycluster.java.html#L30753" class="el_method">setUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b71"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c81">0%</td><td class="bar" id="d77"/><td class="ctr2" id="e77">n/a</td><td class="ctr1" id="f71">1</td><td class="ctr2" id="g77">1</td><td class="ctr1" id="h71">1</td><td class="ctr2" id="i81">1</td><td class="ctr1" id="j70">1</td><td class="ctr2" id="k71">1</td></tr><tr><td id="a72"><a href="Rustycluster.java.html#L30759" class="el_method">mergeUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b72"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c82">0%</td><td class="bar" id="d78"/><td class="ctr2" id="e78">n/a</td><td class="ctr1" id="f72">1</td><td class="ctr2" id="g78">1</td><td class="ctr1" id="h72">1</td><td class="ctr2" id="i82">1</td><td class="ctr1" id="j71">1</td><td class="ctr2" id="k72">1</td></tr><tr><td id="a85"><a href="Rustycluster.java.html#L29765" class="el_method">setOperationType(Rustycluster.BatchOperation.OperationType)</a></td><td class="bar" id="b73"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c5">80%</td><td class="bar" id="d40"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f73">1</td><td class="ctr2" id="g40">2</td><td class="ctr1" id="h73">1</td><td class="ctr2" id="i27">6</td><td class="ctr1" id="j86">0</td><td class="ctr2" id="k73">1</td></tr><tr><td id="a83"><a href="Rustycluster.java.html#L29825" class="el_method">setKey(String)</a></td><td class="bar" id="b74"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="15" alt="15"/></td><td class="ctr2" id="c6">78%</td><td class="bar" id="d41"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f74">1</td><td class="ctr2" id="g41">2</td><td class="ctr1" id="h88">0</td><td class="ctr2" id="i33">5</td><td class="ctr1" id="j87">0</td><td class="ctr2" id="k74">1</td></tr><tr><td id="a94"><a href="Rustycluster.java.html#L29897" class="el_method">setValue(String)</a></td><td class="bar" id="b75"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="15" alt="15"/></td><td class="ctr2" id="c7">78%</td><td class="bar" id="d42"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f75">1</td><td class="ctr2" id="g42">2</td><td class="ctr1" id="h89">0</td><td class="ctr2" id="i34">5</td><td class="ctr1" id="j88">0</td><td class="ctr2" id="k75">1</td></tr><tr><td id="a79"><a href="Rustycluster.java.html#L29992" class="el_method">setField(String)</a></td><td class="bar" id="b76"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="15" alt="15"/></td><td class="ctr2" id="c8">78%</td><td class="bar" id="d43"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f76">1</td><td class="ctr2" id="g43">2</td><td class="ctr1" id="h90">0</td><td class="ctr2" id="i35">5</td><td class="ctr1" id="j89">0</td><td class="ctr2" id="k76">1</td></tr><tr><td id="a24"><a href="Rustycluster.java.html#L29530" class="el_method">clone()</a></td><td class="bar" id="b77"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c83">0%</td><td class="bar" id="d79"/><td class="ctr2" id="e79">n/a</td><td class="ctr1" id="f77">1</td><td class="ctr2" id="g79">1</td><td class="ctr1" id="h74">1</td><td class="ctr2" id="i83">1</td><td class="ctr1" id="j72">1</td><td class="ctr2" id="k77">1</td></tr><tr><td id="a36"><a href="Rustycluster.java.html#L30255" class="el_method">getHashFieldsMap()</a></td><td class="bar" id="b78"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c84">0%</td><td class="bar" id="d80"/><td class="ctr2" id="e80">n/a</td><td class="ctr1" id="f78">1</td><td class="ctr2" id="g80">1</td><td class="ctr1" id="h75">1</td><td class="ctr2" id="i84">1</td><td class="ctr1" id="j73">1</td><td class="ctr2" id="k78">1</td></tr><tr><td id="a51"><a href="Rustycluster.java.html#L30384" class="el_method">getScriptKeysCount()</a></td><td class="bar" id="b79"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c85">0%</td><td class="bar" id="d81"/><td class="ctr2" id="e81">n/a</td><td class="ctr1" id="f79">1</td><td class="ctr2" id="g81">1</td><td class="ctr1" id="h76">1</td><td class="ctr2" id="i85">1</td><td class="ctr1" id="j74">1</td><td class="ctr2" id="k79">1</td></tr><tr><td id="a47"><a href="Rustycluster.java.html#L30531" class="el_method">getScriptArgsCount()</a></td><td class="bar" id="b80"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c86">0%</td><td class="bar" id="d82"/><td class="ctr2" id="e82">n/a</td><td class="ctr1" id="f80">1</td><td class="ctr2" id="g82">1</td><td class="ctr1" id="h77">1</td><td class="ctr2" id="i86">1</td><td class="ctr1" id="j75">1</td><td class="ctr2" id="k80">1</td></tr><tr><td id="a7"><a href="Rustycluster.java.html#L29466" class="el_method">build()</a></td><td class="bar" id="b81"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">72%</td><td class="bar" id="d44"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f81">1</td><td class="ctr2" id="g44">2</td><td class="ctr1" id="h78">1</td><td class="ctr2" id="i52">4</td><td class="ctr1" id="j90">0</td><td class="ctr2" id="k81">1</td></tr><tr><td id="a44"><a href="Rustycluster.java.html#L29737" class="el_method">getOperationTypeValue()</a></td><td class="bar" id="b82"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c87">0%</td><td class="bar" id="d83"/><td class="ctr2" id="e83">n/a</td><td class="ctr1" id="f82">1</td><td class="ctr2" id="g83">1</td><td class="ctr1" id="h79">1</td><td class="ctr2" id="i87">1</td><td class="ctr1" id="j76">1</td><td class="ctr2" id="k82">1</td></tr><tr><td id="a39"><a href="Rustycluster.java.html#L30054" class="el_method">getIntValue()</a></td><td class="bar" id="b83"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c88">0%</td><td class="bar" id="d84"/><td class="ctr2" id="e84">n/a</td><td class="ctr1" id="f83">1</td><td class="ctr2" id="g84">1</td><td class="ctr1" id="h80">1</td><td class="ctr2" id="i88">1</td><td class="ctr1" id="j77">1</td><td class="ctr2" id="k83">1</td></tr><tr><td id="a33"><a href="Rustycluster.java.html#L30110" class="el_method">getFloatValue()</a></td><td class="bar" id="b84"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c89">0%</td><td class="bar" id="d85"/><td class="ctr2" id="e85">n/a</td><td class="ctr1" id="f84">1</td><td class="ctr2" id="g85">1</td><td class="ctr1" id="h81">1</td><td class="ctr2" id="i89">1</td><td class="ctr1" id="j78">1</td><td class="ctr2" id="k84">1</td></tr><tr><td id="a55"><a href="Rustycluster.java.html#L30166" class="el_method">getTtl()</a></td><td class="bar" id="b85"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c90">0%</td><td class="bar" id="d86"/><td class="ctr2" id="e86">n/a</td><td class="ctr1" id="f85">1</td><td class="ctr2" id="g86">1</td><td class="ctr1" id="h82">1</td><td class="ctr2" id="i90">1</td><td class="ctr1" id="j79">1</td><td class="ctr2" id="k85">1</td></tr><tr><td id="a34"><a href="Rustycluster.java.html#L30244" class="el_method">getHashFields()</a></td><td class="bar" id="b86"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c91">0%</td><td class="bar" id="d87"/><td class="ctr2" id="e87">n/a</td><td class="ctr1" id="f86">1</td><td class="ctr2" id="g87">1</td><td class="ctr1" id="h83">1</td><td class="ctr2" id="i91">1</td><td class="ctr1" id="j80">1</td><td class="ctr2" id="k86">1</td></tr><tr><td id="a29"><a href="Rustycluster.java.html#L29390" class="el_method">getDescriptor()</a></td><td class="bar" id="b87"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c92">0%</td><td class="bar" id="d88"/><td class="ctr2" id="e88">n/a</td><td class="ctr1" id="f87">1</td><td class="ctr2" id="g88">1</td><td class="ctr1" id="h84">1</td><td class="ctr2" id="i92">1</td><td class="ctr1" id="j81">1</td><td class="ctr2" id="k87">1</td></tr><tr><td id="a30"><a href="Rustycluster.java.html#L29456" class="el_method">getDescriptorForType()</a></td><td class="bar" id="b88"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c93">0%</td><td class="bar" id="d89"/><td class="ctr2" id="e89">n/a</td><td class="ctr1" id="f88">1</td><td class="ctr2" id="g89">1</td><td class="ctr1" id="h85">1</td><td class="ctr2" id="i93">1</td><td class="ctr1" id="j82">1</td><td class="ctr2" id="k88">1</td></tr><tr><td id="a28"><a href="Rustycluster.java.html#L29461" class="el_method">getDefaultInstanceForType()</a></td><td class="bar" id="b89"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c94">0%</td><td class="bar" id="d90"/><td class="ctr2" id="e90">n/a</td><td class="ctr1" id="f89">1</td><td class="ctr2" id="g90">1</td><td class="ctr1" id="h86">1</td><td class="ctr2" id="i94">1</td><td class="ctr1" id="j83">1</td><td class="ctr2" id="k89">1</td></tr><tr><td id="a68"><a href="Rustycluster.java.html#L29634" class="el_method">isInitialized()</a></td><td class="bar" id="b90"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c95">0%</td><td class="bar" id="d91"/><td class="ctr2" id="e91">n/a</td><td class="ctr1" id="f90">1</td><td class="ctr2" id="g91">1</td><td class="ctr1" id="h87">1</td><td class="ctr2" id="i95">1</td><td class="ctr1" id="j84">1</td><td class="ctr2" id="k90">1</td></tr><tr><td id="a76"><a href="Rustycluster.java.html#L29424" class="el_method">Rustycluster.BatchOperation.Builder()</a></td><td class="bar" id="b91"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="24" alt="24"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d92"/><td class="ctr2" id="e92">n/a</td><td class="ctr1" id="f92">0</td><td class="ctr2" id="g92">1</td><td class="ctr1" id="h91">0</td><td class="ctr2" id="i5">11</td><td class="ctr1" id="j91">0</td><td class="ctr2" id="k91">1</td></tr><tr><td id="a8"><a href="Rustycluster.java.html#L29475" class="el_method">buildPartial()</a></td><td class="bar" id="b92"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d45"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f91">1</td><td class="ctr2" id="g45">2</td><td class="ctr1" id="h92">0</td><td class="ctr2" id="i53">4</td><td class="ctr1" id="j92">0</td><td class="ctr2" id="k92">1</td></tr><tr><td id="a82"><a href="Rustycluster.java.html#L30067" class="el_method">setIntValue(long)</a></td><td class="bar" id="b93"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d93"/><td class="ctr2" id="e93">n/a</td><td class="ctr1" id="f93">0</td><td class="ctr2" id="g93">1</td><td class="ctr1" id="h93">0</td><td class="ctr2" id="i54">4</td><td class="ctr1" id="j93">0</td><td class="ctr2" id="k93">1</td></tr><tr><td id="a81"><a href="Rustycluster.java.html#L30123" class="el_method">setFloatValue(double)</a></td><td class="bar" id="b94"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d94"/><td class="ctr2" id="e94">n/a</td><td class="ctr1" id="f94">0</td><td class="ctr2" id="g94">1</td><td class="ctr1" id="h94">0</td><td class="ctr2" id="i55">4</td><td class="ctr1" id="j94">0</td><td class="ctr2" id="k94">1</td></tr><tr><td id="a92"><a href="Rustycluster.java.html#L30179" class="el_method">setTtl(long)</a></td><td class="bar" id="b95"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d95"/><td class="ctr2" id="e95">n/a</td><td class="ctr1" id="f95">0</td><td class="ctr2" id="g95">1</td><td class="ctr1" id="h95">0</td><td class="ctr2" id="i56">4</td><td class="ctr1" id="j95">0</td><td class="ctr2" id="k95">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
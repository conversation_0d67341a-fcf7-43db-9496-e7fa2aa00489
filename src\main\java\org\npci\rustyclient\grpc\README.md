# gRPC Generated Stubs

This directory contains the gRPC service stubs and protobuf message classes generated from the `rustycluster.proto` file.

## Generated Files

### gRPC Service Stub
- `KeyValueServiceGrpc.java` - Main gRPC service stub containing client stubs for all RPC methods

### Protobuf Message Classes
All request and response message classes for the RustyCluster key-value operations:

#### String Operations
- `SetRequest.java` / `SetResponse.java`
- `GetRequest.java` / `GetResponse.java`
- `DeleteRequest.java` / `DeleteResponse.java`
- `SetExRequest.java` / `SetExResponse.java`
- `SetExpiryRequest.java` / `SetExpiryResponse.java`

#### Numeric Operations
- `IncrByRequest.java` / `IncrByResponse.java`
- `DecrByRequest.java` / `DecrByResponse.java`
- `IncrByFloatRequest.java` / `IncrByFloatResponse.java`

#### Hash Operations
- `HSetRequest.java` / `HSetResponse.java`
- `HGetRequest.java` / `HGetResponse.java`
- `HGetAllRequest.java` / `HGetAllResponse.java`
- `HIncrByRequest.java` / `HIncrByResponse.java`
- `HDecrByRequest.java` / `HDecrByResponse.java`
- `HIncrByFloatRequest.java` / `HIncrByFloatResponse.java`

#### Batch Operations
- `BatchOperation.java` - Represents a single operation in a batch
- `BatchWriteRequest.java` / `BatchWriteResponse.java`

#### Supporting Classes
- `RustyClusterProto.java` - Contains protobuf descriptors and metadata
- Various `*OrBuilder.java` interfaces for protobuf message builders

## Generation Approach

These files were generated using the protobuf Maven plugin and then copied to the source directory to avoid Windows file lock issues with temporary directories during the build process. This approach provides:

1. **Reliability** - No temporary directory cleanup issues
2. **Consistency** - Generated stubs are always available
3. **Performance** - No need to regenerate on every build
4. **Simplicity** - Standard Java source files that can be easily inspected and debugged
5. **Java 9+ Compatibility** - Fixed `javax.annotation.Generated` annotation issues by replacing with comments

## Regeneration

If the `rustycluster.proto` file is updated, these files can be regenerated by:

1. Temporarily adding the protobuf Maven plugin back to `pom.xml`
2. Running `mvn protobuf:compile` and `mvn protobuf:compile-custom`
3. Copying the generated files from `target/generated-sources/protobuf/java/com/rustycluster/grpc/` to this directory
4. Removing the protobuf plugin from `pom.xml`

## Usage

These classes are used by the `RustyClusterClient` to communicate with RustyCluster servers via gRPC. The main entry points are:

- `KeyValueServiceGrpc.newBlockingStub(channel)` - For synchronous calls
- `KeyValueServiceGrpc.newStub(channel)` - For asynchronous calls
- `KeyValueServiceGrpc.newFutureStub(channel)` - For future-based calls

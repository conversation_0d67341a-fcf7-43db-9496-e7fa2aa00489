package org.npci.rustyclient.client.connection;

import com.google.common.util.concurrent.ListenableFuture;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.exception.NoAvailableNodesException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rustycluster.KeyValueServiceGrpc;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Unified connection manager that uses a shared connection pool for both
 * synchronous and asynchronous operations, optimizing resource usage.
 */
public class SharedConnectionManager implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(SharedConnectionManager.class);

    private final RustyClusterClientConfig config;
    private final SharedConnectionPool connectionPool;
    private final List<NodeConfig> sortedNodes;
    private final AtomicReference<NodeConfig> currentNode;
    private final SharedFailbackManager failbackManager;

    /**
     * Create a new SharedConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public SharedConnectionManager(RustyClusterClientConfig config, 
                                 org.npci.rustyclient.client.auth.AuthenticationManager authenticationManager) {
        this.config = config;
        this.connectionPool = new SharedConnectionPool(config, authenticationManager);

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
        this.sortedNodes = config.getNodes().stream()
                .sorted(Comparator.comparingInt(node -> node.role().getPriority()))
                .toList();

        // Set the initial node to the highest priority node
        this.currentNode = new AtomicReference<>(sortedNodes.get(0));

        // Initialize failback manager - we'll create a custom one for shared pool
        this.failbackManager = new SharedFailbackManager(config, connectionPool, sortedNodes, currentNode);
        this.failbackManager.start();

        logger.info("SharedConnectionManager initialized with {} nodes", sortedNodes.size());
    }

    /**
     * Execute a synchronous operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param <T>       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public <T> T executeWithFailover(BlockingOperation<T> operation) throws NoAvailableNodesException {
        return executeWithFailover(operation, OperationType.READ);
    }

    /**
     * Execute a synchronous operation with automatic failover and operation type.
     *
     * @param operation The operation to execute
     * @param operationType The type of operation (READ or WRITE)
     * @param <T>       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public <T> T executeWithFailover(BlockingOperation<T> operation, OperationType operationType) 
            throws NoAvailableNodesException {
        
        NodeConfig node = currentNode.get();
        long timeoutMs = operationType == OperationType.READ ? config.getReadTimeoutMs() : config.getWriteTimeoutMs();

        for (int attempt = 0; attempt <= config.getMaxRetries(); attempt++) {
            try (SharedConnectionPool.SharedConnectionWrapper wrapper = 
                     connectionPool.borrowBlockingConnection(node)) {
                
                // Apply deadline per operation
                KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline =
                    wrapper.getBlockingStub().withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);
                
                return operation.execute(stubWithDeadline);
                
            } catch (Exception e) {
                logger.warn("Operation failed on node {} (attempt {}/{}): {}", 
                           node, attempt + 1, config.getMaxRetries() + 1, e.getMessage());

                if (attempt < config.getMaxRetries()) {
                    try {
                        Thread.sleep(config.getRetryDelayMs());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry delay", ie);
                    }
                } else {
                    // Try to find the next available node
                    var nextNode = findNextAvailableNode(node);
                    if (nextNode != null) {
                        currentNode.set(nextNode);
                        logger.info("Switched to node: {}", nextNode);
                        
                        // Clear authentication state when switching nodes
                        if (config.hasAuthentication()) {
                            connectionPool.getAuthenticationManager().clearAuthentication();
                            logger.debug("Cleared authentication state for node switch to: {}", nextNode);
                        }
                    } else {
                        logger.warn("No available nodes found after failure");
                    }
                }
            }
        }

        throw new NoAvailableNodesException("All nodes are unavailable after retries");
    }

    /**
     * Execute an asynchronous operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param <T>       The return type of the operation
     * @return CompletableFuture with the result of the operation
     */
    public <T> CompletableFuture<T> executeWithFailoverAsync(AsyncOperation<T> operation) {
        return executeWithFailoverAsync(operation, OperationType.READ);
    }

    /**
     * Execute an asynchronous operation with automatic failover and operation type.
     *
     * @param operation The operation to execute
     * @param operationType The type of operation (READ or WRITE)
     * @param <T>       The return type of the operation
     * @return CompletableFuture with the result of the operation
     */
    public <T> CompletableFuture<T> executeWithFailoverAsync(AsyncOperation<T> operation, OperationType operationType) {
        NodeConfig node = currentNode.get();
        long timeoutMs = operationType == OperationType.READ ? config.getReadTimeoutMs() : config.getWriteTimeoutMs();

        return connectionPool.borrowAsyncConnection(node)
            .thenCompose(wrapper -> {
                try {
                    // Apply deadline per operation
                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =
                        wrapper.getFutureStub().withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);
                    ListenableFuture<T> listenableFuture = operation.execute(stubWithDeadline);
                    return toCompletableFuture(listenableFuture)
                        .whenComplete((result, throwable) -> {
                            wrapper.close(); // Return connection to pool
                        });
                } catch (Exception e) {
                    wrapper.close(); // Return connection to pool
                    return CompletableFuture.failedFuture(e);
                }
            })
            .exceptionally(throwable -> {
                logger.warn("Async operation failed on node {}: {}", node, throwable.getMessage());
                // For now, we don't implement async retry logic - that would be more complex
                // The caller can handle retries if needed
                throw new RuntimeException(throwable);
            });
    }

    private NodeConfig findNextAvailableNode(NodeConfig currentNode) {
        // Simple round-robin to next node
        int currentIndex = sortedNodes.indexOf(currentNode);
        for (int i = 1; i < sortedNodes.size(); i++) {
            int nextIndex = (currentIndex + i) % sortedNodes.size();
            NodeConfig nextNode = sortedNodes.get(nextIndex);
            // In a real implementation, we'd check if the node is healthy
            return nextNode;
        }
        return null;
    }

    private <T> CompletableFuture<T> toCompletableFuture(ListenableFuture<T> listenableFuture) {
        CompletableFuture<T> completableFuture = new CompletableFuture<>();
        listenableFuture.addListener(() -> {
            try {
                completableFuture.complete(listenableFuture.get());
            } catch (Exception e) {
                completableFuture.completeExceptionally(e);
            }
        }, Runnable::run);
        return completableFuture;
    }

    @Override
    public void close() {
        if (failbackManager != null) {
            failbackManager.stop();
        }
        if (connectionPool != null) {
            connectionPool.close();
        }
        logger.info("SharedConnectionManager closed");
    }

    /**
     * Functional interface for blocking operations.
     */
    @FunctionalInterface
    public interface BlockingOperation<T> {
        T execute(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) throws Exception;
    }

    /**
     * Functional interface for async operations.
     */
    @FunctionalInterface
    public interface AsyncOperation<T> {
        ListenableFuture<T> execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }

    /**
     * Operation type enumeration.
     */
    public enum OperationType {
        READ, WRITE
    }

    /**
     * Custom failback manager for shared connection pool.
     */
    private static class SharedFailbackManager implements AutoCloseable {
        private static final Logger logger = LoggerFactory.getLogger(SharedFailbackManager.class);

        private final RustyClusterClientConfig config;
        private final SharedConnectionPool connectionPool;
        private final List<NodeConfig> sortedNodes;
        private final AtomicReference<NodeConfig> currentNode;
        private final java.util.concurrent.ScheduledExecutorService scheduler;
        private volatile boolean running = false;

        public SharedFailbackManager(RustyClusterClientConfig config,
                                   SharedConnectionPool connectionPool,
                                   List<NodeConfig> sortedNodes,
                                   AtomicReference<NodeConfig> currentNode) {
            this.config = config;
            this.connectionPool = connectionPool;
            this.sortedNodes = sortedNodes;
            this.currentNode = currentNode;
            this.scheduler = java.util.concurrent.Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "SharedFailbackManager");
                t.setDaemon(true);
                return t;
            });
        }

        public void start() {
            if (!config.isEnableFailback()) {
                logger.debug("Failback is disabled, not starting SharedFailbackManager");
                return;
            }

            if (running) {
                logger.warn("SharedFailbackManager is already running");
                return;
            }

            running = true;
            scheduler.scheduleWithFixedDelay(
                this::checkForFailback,
                config.getFailbackCheckIntervalMs(),
                config.getFailbackCheckIntervalMs(),
                TimeUnit.MILLISECONDS
            );

            logger.info("SharedFailbackManager started with check interval: {}ms", config.getFailbackCheckIntervalMs());
        }

        public void stop() {
            running = false;
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
            logger.info("SharedFailbackManager stopped");
        }

        private void checkForFailback() {
            if (!running) {
                return;
            }

            try {
                NodeConfig current = currentNode.get();
                if (current == null) {
                    return;
                }

                // Find the highest priority node that's available
                NodeConfig bestAvailableNode = findBestAvailableNode();

                if (bestAvailableNode != null &&
                    bestAvailableNode.role().getPriority() < current.role().getPriority()) {

                    logger.info("Failing back from {} (priority {}) to {} (priority {})",
                        current, current.role().getPriority(),
                        bestAvailableNode, bestAvailableNode.role().getPriority());

                    currentNode.set(bestAvailableNode);

                    // Clear authentication state when switching nodes during failback
                    if (config.hasAuthentication()) {
                        connectionPool.getAuthenticationManager().clearAuthentication();
                        logger.debug("Cleared authentication state for failback to: {}", bestAvailableNode);
                    }
                }
            } catch (Exception e) {
                logger.warn("Error during failback check: {}", e.getMessage());
            }
        }

        private NodeConfig findBestAvailableNode() {
            NodeConfig current = currentNode.get();
            if (current == null) {
                return null;
            }

            // Only check nodes with higher priority (lower priority number) than current
            for (NodeConfig node : sortedNodes) {
                if (node.role().getPriority() < current.role().getPriority()) {
                    if (isNodeHealthy(node)) {
                        return node;
                    }
                } else {
                    break; // Nodes are sorted by priority
                }
            }
            return null;
        }

        private boolean isNodeHealthy(NodeConfig node) {
            try (SharedConnectionPool.SharedConnectionWrapper wrapper =
                     connectionPool.borrowBlockingConnection(node)) {

                // Perform a simple health check
                rustycluster.Rustycluster.GetRequest healthCheckRequest =
                    rustycluster.Rustycluster.GetRequest.newBuilder()
                        .setKey("__health_check__")
                        .build();

                wrapper.getBlockingStub()
                    .withDeadlineAfter(1000, TimeUnit.MILLISECONDS)
                    .get(healthCheckRequest);

                return true;
            } catch (Exception e) {
                logger.debug("Health check failed for node {}: {}", node, e.getMessage());
                return false;
            }
        }

        @Override
        public void close() {
            stop();
        }
    }
}

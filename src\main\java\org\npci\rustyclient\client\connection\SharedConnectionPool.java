package org.npci.rustyclient.client.connection;

import io.grpc.Channel;
import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannel;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.npci.rustyclient.client.interceptor.AuthenticationInterceptor;
import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rustycluster.KeyValueServiceGrpc;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


/**
 * Shared connection pool that provides both synchronous and asynchronous gRPC stubs
 * from the same underlying connection pool, optimizing resource usage.
 */
public class SharedConnectionPool implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(SharedConnectionPool.class);

    private final GrpcChannelFactory channelFactory;
    private final AuthenticationManager authenticationManager;
    private final Map<String, GenericObjectPool<SharedConnection>> connectionPools;

    /**
     * Create a new SharedConnectionPool.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public SharedConnectionPool(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
        this.channelFactory = new GrpcChannelFactory(config);
        this.authenticationManager = authenticationManager;
        this.connectionPools = new HashMap<>();

        // Initialize connection pools for each node
        for (NodeConfig nodeConfig : config.getNodes()) {
            GenericObjectPoolConfig<SharedConnection> poolConfig = new GenericObjectPoolConfig<>();
            
            // Optimized settings for shared pool
            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());
            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());
            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm
            
            // Performance-oriented validation
            poolConfig.setTestOnBorrow(false); // Disable for performance
            poolConfig.setTestOnReturn(false); // Disable expensive validation
            poolConfig.setTestWhileIdle(true); // Only validate idle connections
            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30));
            
            // High-throughput settings
            poolConfig.setBlockWhenExhausted(false); // Fail fast instead of blocking
            poolConfig.setMaxWait(java.time.Duration.ofMillis(100)); // Quick timeout
            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer
            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead
            
            // JMX monitoring for production
            poolConfig.setJmxEnabled(true);
            poolConfig.setJmxNamePrefix("RustyClusterShared-" + nodeConfig.getAddress());

            GenericObjectPool<SharedConnection> pool =
                new GenericObjectPool<>(new SharedConnectionFactory(nodeConfig), poolConfig);

            connectionPools.put(nodeConfig.getAddress(), pool);
            logger.info("Created shared connection pool for node: {}", nodeConfig);
        }
    }

    /**
     * Borrow a connection wrapper for blocking operations.
     *
     * @param nodeConfig The node configuration
     * @return A connection wrapper for blocking operations
     * @throws Exception If borrowing fails
     */
    public SharedConnectionWrapper borrowBlockingConnection(NodeConfig nodeConfig) throws Exception {
        SharedConnection connection = borrowConnection(nodeConfig);
        return new SharedConnectionWrapper(connection, nodeConfig, this, true);
    }

    /**
     * Borrow a connection wrapper for async operations.
     *
     * @param nodeConfig The node configuration
     * @return CompletableFuture that completes with a connection wrapper for async operations
     */
    public CompletableFuture<SharedConnectionWrapper> borrowAsyncConnection(NodeConfig nodeConfig) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                SharedConnection connection = borrowConnection(nodeConfig);
                return new SharedConnectionWrapper(connection, nodeConfig, this, false);
            } catch (Exception e) {
                throw new RuntimeException("Failed to borrow connection from shared pool", e);
            }
        });
    }

    /**
     * Get the authentication manager.
     *
     * @return The authentication manager
     */
    public AuthenticationManager getAuthenticationManager() {
        return authenticationManager;
    }

    private SharedConnection borrowConnection(NodeConfig nodeConfig) throws Exception {
        GenericObjectPool<SharedConnection> pool = connectionPools.get(nodeConfig.getAddress());
        if (pool == null) {
            throw new IllegalArgumentException("No pool found for node: " + nodeConfig);
        }
        return pool.borrowObject();
    }

    /**
     * Return a connection to the pool.
     *
     * @param nodeConfig The node configuration
     * @param connection The connection to return
     */
    void returnConnection(NodeConfig nodeConfig, SharedConnection connection) {
        GenericObjectPool<SharedConnection> pool = connectionPools.get(nodeConfig.getAddress());
        if (pool == null) {
            logger.warn("No pool found for node: {}", nodeConfig);
            return;
        }

        pool.returnObject(connection);
        logger.debug("Connection returned to pool for node: {}", nodeConfig);
    }

    /**
     * Close the connection pool and release all resources.
     */
    @Override
    public void close() {
        for (GenericObjectPool<SharedConnection> pool : connectionPools.values()) {
            pool.close();
        }
        connectionPools.clear();
        channelFactory.close();
        logger.info("Shared connection pool closed");
    }

    /**
     * Wrapper class that holds both blocking and future stubs created from the same channel.
     */
    public static class SharedConnection {
        private final KeyValueServiceGrpc.KeyValueServiceBlockingStub blockingStub;
        private final KeyValueServiceGrpc.KeyValueServiceFutureStub futureStub;

        public SharedConnection(KeyValueServiceGrpc.KeyValueServiceBlockingStub blockingStub,
                               KeyValueServiceGrpc.KeyValueServiceFutureStub futureStub) {
            this.blockingStub = blockingStub;
            this.futureStub = futureStub;
        }

        public KeyValueServiceGrpc.KeyValueServiceBlockingStub getBlockingStub() {
            return blockingStub;
        }

        public KeyValueServiceGrpc.KeyValueServiceFutureStub getFutureStub() {
            return futureStub;
        }
    }

    /**
     * Factory for creating and validating shared connections.
     */
    private class SharedConnectionFactory extends BasePooledObjectFactory<SharedConnection> {
        private final NodeConfig nodeConfig;

        SharedConnectionFactory(NodeConfig nodeConfig) {
            this.nodeConfig = nodeConfig;
        }

        @Override
        public SharedConnection create() {
            ManagedChannel channel = channelFactory.createChannel(nodeConfig);

            // Create channel with authentication interceptor
            Channel interceptedChannel = ClientInterceptors.intercept(channel,
                    new AuthenticationInterceptor(authenticationManager));

            // Create both stub types from the same channel
            KeyValueServiceGrpc.KeyValueServiceBlockingStub blockingStub =
                KeyValueServiceGrpc.newBlockingStub(interceptedChannel);
            KeyValueServiceGrpc.KeyValueServiceFutureStub futureStub =
                KeyValueServiceGrpc.newFutureStub(interceptedChannel);

            return new SharedConnection(blockingStub, futureStub);
        }

        @Override
        public PooledObject<SharedConnection> wrap(SharedConnection connection) {
            return new DefaultPooledObject<>(connection);
        }

        @Override
        public boolean validateObject(PooledObject<SharedConnection> pooledObject) {
            // Basic validation - check if stubs are not null
            SharedConnection connection = pooledObject.getObject();
            return connection != null &&
                   connection.getBlockingStub() != null &&
                   connection.getFutureStub() != null;
        }
    }

    /**
     * Wrapper that automatically returns connections to the pool when closed.
     */
    public static class SharedConnectionWrapper implements AutoCloseable {
        private final SharedConnection connection;
        private final NodeConfig nodeConfig;
        private final SharedConnectionPool pool;
        private final boolean isBlocking;
        private volatile boolean closed = false;

        SharedConnectionWrapper(SharedConnection connection, NodeConfig nodeConfig,
                               SharedConnectionPool pool, boolean isBlocking) {
            this.connection = connection;
            this.nodeConfig = nodeConfig;
            this.pool = pool;
            this.isBlocking = isBlocking;
        }

        public KeyValueServiceGrpc.KeyValueServiceBlockingStub getBlockingStub() {
            if (closed) {
                throw new IllegalStateException("Connection wrapper is closed");
            }
            return connection.getBlockingStub();
        }

        public KeyValueServiceGrpc.KeyValueServiceFutureStub getFutureStub() {
            if (closed) {
                throw new IllegalStateException("Connection wrapper is closed");
            }
            return connection.getFutureStub();
        }

        @Override
        public void close() {
            if (!closed) {
                closed = true;
                pool.returnConnection(nodeConfig, connection);
            }
        }
    }
}

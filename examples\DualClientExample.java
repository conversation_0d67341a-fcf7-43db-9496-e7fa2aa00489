import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.redis.RedisClient;
import org.npci.rustyclient.redis.config.RedisClientConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Example showing how to use both RustyClusterClient and RedisClient together
 * in a microservice that needs both cluster operations and direct Redis operations.
 */
public class DualClientExample {

    public static void main(String[] args) {
        // Example 1: Service using both RustyCluster and Redis
        dualClientMicroserviceExample();

        // Example 2: Routing operations based on use case
        operationRoutingExample();

        // Example 3: Async operations with both clients
        asyncDualClientExample();
    }

    /**
     * Example of a microservice using both RustyCluster and Redis clients.
     */
    public static void dualClientMicroserviceExample() {
        System.out.println("=== Dual Client Microservice Example ===");

        // Configure RustyCluster client for distributed operations
        RustyClusterClientConfig rustyConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("rusty-primary.example.com", 50051)
                .addSecondaryNode("rusty-secondary.example.com", 50051)
                .authentication("rusty-user", "rusty-password")
                .maxConnectionsPerNode(20)
                .build();

        // Configure Redis client for local caching and session management
        RedisClientConfig redisConfig = RedisClientConfig.builder()
                .addPrimaryNode("redis-cache.example.com", 6379)
                .addSecondaryNode("redis-replica.example.com", 6379)
                .authentication("redis-user", "redis-password")
                .database(1) // Use database 1 for this service
                .maxConnectionsPerNode(15)
                .build();

        try (RustyClusterClient rustyClient = new RustyClusterClient(rustyConfig);
             RedisClient redisClient = new RedisClient(redisConfig)) {

            // Use RustyCluster for persistent, distributed data
            System.out.println("Storing user profile in RustyCluster...");
            rustyClient.hSet("user:1001:profile", "name", "John Doe");
            rustyClient.hSet("user:1001:profile", "email", "<EMAIL>");
            rustyClient.hSet("user:1001:profile", "department", "Engineering");

            // Use Redis for session data and caching
            System.out.println("Storing session data in Redis...");
            Map<String, String> sessionData = new HashMap<>();
            sessionData.put("user_id", "1001");
            sessionData.put("login_time", String.valueOf(System.currentTimeMillis()));
            sessionData.put("session_token", "abc123xyz");
            
            redisClient.hMSet("session:abc123", sessionData);
            redisClient.expire("session:abc123", 3600); // 1 hour session

            // Cache frequently accessed data in Redis
            String userProfile = rustyClient.hGet("user:1001:profile", "name");
            redisClient.setEx("cache:user:1001:name", userProfile, 300); // 5 minute cache

            // Retrieve data
            String cachedName = redisClient.get("cache:user:1001:name");
            String sessionUserId = redisClient.hGet("session:abc123", "user_id");

            System.out.println("Cached user name: " + cachedName);
            System.out.println("Session user ID: " + sessionUserId);

            // Cleanup
            rustyClient.delete("user:1001:profile");
            redisClient.delete("session:abc123");
            redisClient.delete("cache:user:1001:name");

            System.out.println("Dual client operations completed successfully");

        } catch (Exception e) {
            System.err.println("Error in dual client example: " + e.getMessage());
        }
    }

    /**
     * Example showing how to route operations based on use case.
     */
    public static void operationRoutingExample() {
        System.out.println("\n=== Operation Routing Example ===");

        RustyClusterClientConfig rustyConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .build();

        RedisClientConfig redisConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .build();

        try (RustyClusterClient rustyClient = new RustyClusterClient(rustyConfig);
             RedisClient redisClient = new RedisClient(redisConfig)) {

            // Route operations based on data characteristics
            
            // 1. Persistent business data -> RustyCluster
            System.out.println("Storing business data in RustyCluster...");
            rustyClient.set("business:customer:1001", "CustomerData");
            rustyClient.hSet("business:order:2001", "customer_id", "1001");
            rustyClient.hSet("business:order:2001", "amount", "299.99");
            rustyClient.hSet("business:order:2001", "status", "pending");

            // 2. Temporary/cache data -> Redis
            System.out.println("Storing cache data in Redis...");
            redisClient.setEx("temp:verification:1001", "pending", 300); // 5 min verification
            redisClient.set("cache:popular:products", "product1,product2,product3");

            // 3. Session data -> Redis
            System.out.println("Storing session data in Redis...");
            redisClient.hSet("session:user:1001", "cart_items", "3");
            redisClient.hSet("session:user:1001", "last_activity", String.valueOf(System.currentTimeMillis()));
            redisClient.expire("session:user:1001", 1800); // 30 min session

            // 4. Counters and metrics -> Both (depending on persistence needs)
            System.out.println("Updating counters...");
            
            // Temporary counters in Redis
            redisClient.incr("temp:page_views:today");
            redisClient.expire("temp:page_views:today", 86400); // 24 hours
            
            // Persistent counters in RustyCluster
            rustyClient.incrBy("persistent:total_orders", 1);

            // Read operations - try cache first, then persistent store
            String cachedProducts = redisClient.get("cache:popular:products");
            if (cachedProducts != null) {
                System.out.println("Found cached products: " + cachedProducts);
            } else {
                // Fallback to RustyCluster if not in cache
                System.out.println("Cache miss, would fetch from RustyCluster...");
            }

            System.out.println("Operation routing completed successfully");

        } catch (Exception e) {
            System.err.println("Error in operation routing example: " + e.getMessage());
        }
    }

    /**
     * Example showing async operations with both clients.
     */
    public static void asyncDualClientExample() {
        System.out.println("\n=== Async Dual Client Example ===");

        RustyClusterClientConfig rustyConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .build();

        RedisClientConfig redisConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .build();

        try (RustyClusterClient rustyClient = new RustyClusterClient(rustyConfig);
             RedisClient redisClient = new RedisClient(redisConfig)) {

            System.out.println("Starting async operations...");

            // Parallel async operations
            CompletableFuture<Boolean> rustySetFuture = rustyClient.setAsync("async:rusty:key1", "rusty_value1");
            CompletableFuture<Boolean> redisSetFuture = redisClient.setAsync("async:redis:key1", "redis_value1");

            // Wait for both to complete
            CompletableFuture.allOf(rustySetFuture, redisSetFuture)
                    .thenRun(() -> System.out.println("Both async sets completed"))
                    .join();

            // Chained async operations
            rustyClient.setAsync("async:user:data", "user_data")
                    .thenCompose(result -> {
                        if (result) {
                            // Cache the data in Redis after storing in RustyCluster
                            return redisClient.setExAsync("cache:user:data", "user_data", 300);
                        } else {
                            return CompletableFuture.completedFuture(false);
                        }
                    })
                    .thenAccept(cacheResult -> {
                        if (cacheResult) {
                            System.out.println("Data stored in RustyCluster and cached in Redis");
                        } else {
                            System.out.println("Failed to store or cache data");
                        }
                    })
                    .join();

            // Parallel reads from both systems
            CompletableFuture<String> rustyGetFuture = rustyClient.getAsync("async:rusty:key1");
            CompletableFuture<String> redisGetFuture = redisClient.getAsync("async:redis:key1");

            CompletableFuture.allOf(rustyGetFuture, redisGetFuture)
                    .thenRun(() -> {
                        try {
                            String rustyValue = rustyGetFuture.get();
                            String redisValue = redisGetFuture.get();
                            System.out.println("RustyCluster value: " + rustyValue);
                            System.out.println("Redis value: " + redisValue);
                        } catch (Exception e) {
                            System.err.println("Error retrieving async values: " + e.getMessage());
                        }
                    })
                    .join();

            // Cleanup async
            CompletableFuture<Boolean> rustyCleanup1 = rustyClient.deleteAsync("async:rusty:key1");
            CompletableFuture<Boolean> rustyCleanup2 = rustyClient.deleteAsync("async:user:data");
            CompletableFuture<Boolean> redisCleanup1 = redisClient.deleteAsync("async:redis:key1");
            CompletableFuture<Boolean> redisCleanup2 = redisClient.deleteAsync("cache:user:data");

            CompletableFuture.allOf(rustyCleanup1, rustyCleanup2, redisCleanup1, redisCleanup2)
                    .thenRun(() -> System.out.println("Async cleanup completed"))
                    .join();

            System.out.println("Async dual client operations completed successfully");

        } catch (Exception e) {
            System.err.println("Error in async dual client example: " + e.getMessage());
        }
    }
}

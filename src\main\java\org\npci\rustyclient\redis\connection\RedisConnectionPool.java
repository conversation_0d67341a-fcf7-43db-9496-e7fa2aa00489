package org.npci.rustyclient.redis.connection;

import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Manages connection pools for Redis nodes using Jedis.
 */
public class RedisConnectionPool implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(RedisConnectionPool.class);

    private final RedisClientConfig config;
    private final ConcurrentMap<String, GenericObjectPool<Jedis>> connectionPools;

    /**
     * Create a new RedisConnectionPool.
     *
     * @param config The Redis client configuration
     */
    public RedisConnectionPool(RedisClientConfig config) {
        this.config = config;
        this.connectionPools = new ConcurrentHashMap<>();
        initializePools();
    }

    private void initializePools() {
        logger.info("Initializing Redis connection pools for {} nodes", config.getNodes().size());

        // Initialize connection pools for each node
        for (RedisNodeConfig nodeConfig : config.getNodes()) {
            GenericObjectPoolConfig<Jedis> poolConfig = new GenericObjectPoolConfig<>();
            
            // High-throughput optimizations
            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());
            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());
            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm

            // Performance-oriented validation
            poolConfig.setTestOnBorrow(false); // Disable for performance
            poolConfig.setTestOnReturn(false); // Disable expensive validation
            poolConfig.setTestWhileIdle(true); // Only validate idle connections
            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30));

            // High-throughput settings
            poolConfig.setBlockWhenExhausted(false); // Fail fast instead of blocking
            poolConfig.setMaxWait(java.time.Duration.ofMillis(100)); // Quick timeout
            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer
            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead

            // JMX monitoring for production
            poolConfig.setJmxEnabled(true);
            poolConfig.setJmxNamePrefix("RedisClient-" + nodeConfig.getAddress());

            GenericObjectPool<Jedis> pool = new GenericObjectPool<>(
                new RedisConnectionFactory(nodeConfig, config), poolConfig);

            connectionPools.put(nodeConfig.getAddress(), pool);
            logger.info("Created Redis connection pool for node: {}", nodeConfig);
        }
    }

    /**
     * Borrow a Jedis connection from the pool for the specified node.
     *
     * @param nodeConfig The node configuration
     * @return A Jedis connection
     * @throws Exception If unable to borrow connection
     */
    public Jedis borrowConnection(RedisNodeConfig nodeConfig) throws Exception {
        GenericObjectPool<Jedis> pool = connectionPools.get(nodeConfig.getAddress());
        if (pool == null) {
            throw new IllegalArgumentException("No pool found for node: " + nodeConfig);
        }
        
        return pool.borrowObject();
    }

    /**
     * Return a Jedis connection to the pool.
     *
     * @param nodeConfig The node configuration
     * @param connection The connection to return
     */
    public void returnConnection(RedisNodeConfig nodeConfig, Jedis connection) {
        GenericObjectPool<Jedis> pool = connectionPools.get(nodeConfig.getAddress());
        if (pool != null && connection != null) {
            pool.returnObject(connection);
        }
    }

    /**
     * Invalidate a connection (remove from pool).
     *
     * @param nodeConfig The node configuration
     * @param connection The connection to invalidate
     */
    public void invalidateConnection(RedisNodeConfig nodeConfig, Jedis connection) {
        GenericObjectPool<Jedis> pool = connectionPools.get(nodeConfig.getAddress());
        if (pool != null && connection != null) {
            try {
                pool.invalidateObject(connection);
            } catch (Exception e) {
                logger.warn("Failed to invalidate connection for node: {}", nodeConfig, e);
            }
        }
    }

    /**
     * Get pool statistics for a node.
     *
     * @param nodeConfig The node configuration
     * @return Pool statistics as a string
     */
    public String getPoolStats(RedisNodeConfig nodeConfig) {
        GenericObjectPool<Jedis> pool = connectionPools.get(nodeConfig.getAddress());
        if (pool == null) {
            return "No pool found for node: " + nodeConfig;
        }

        return String.format("Node: %s, Active: %d, Idle: %d, Total: %d, Borrowed: %d, Returned: %d",
            nodeConfig.getAddress(),
            pool.getNumActive(),
            pool.getNumIdle(),
            pool.getNumActive() + pool.getNumIdle(),
            pool.getBorrowedCount(),
            pool.getReturnedCount());
    }

    /**
     * Test if a connection is valid.
     *
     * @param connection The connection to test
     * @return True if connection is valid
     */
    public boolean isConnectionValid(Jedis connection) {
        if (connection == null) {
            return false;
        }

        try {
            // Simple ping test
            String response = connection.ping();
            return "PONG".equals(response);
        } catch (Exception e) {
            logger.debug("Connection validation failed", e);
            return false;
        }
    }

    @Override
    public void close() {
        logger.info("Closing Redis connection pools");
        
        for (GenericObjectPool<Jedis> pool : connectionPools.values()) {
            try {
                pool.close();
            } catch (Exception e) {
                logger.warn("Error closing connection pool", e);
            }
        }
        
        connectionPools.clear();
        logger.info("Redis connection pools closed");
    }
}

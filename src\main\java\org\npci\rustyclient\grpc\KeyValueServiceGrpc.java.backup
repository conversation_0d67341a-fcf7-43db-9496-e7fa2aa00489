package com.rustycluster.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.59.0)",
    comments = "Source: rustycluster.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class KeyValueServiceGrpc {

  private KeyValueServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = "rustycluster.KeyValueService";

  // Static method descriptors that strictly reflect the proto.

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Authenticate",
      requestType = com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest,
      com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse> getAuthenticateMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest, com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse> getAuthenticateMethod;
    if ((getAuthenticateMethod = KeyValueServiceGrpc.getAuthenticateMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getAuthenticateMethod = KeyValueServiceGrpc.getAuthenticateMethod) == null) {
          KeyValueServiceGrpc.getAuthenticateMethod = getAuthenticateMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest, com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Authenticate"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Authenticate"))
              .build();
        }
      }
    }
    return getAuthenticateMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.PingRequest,
      com.rustycluster.grpc.RustyClusterProto.PingResponse> getPingMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Ping",
      requestType = com.rustycluster.grpc.RustyClusterProto.PingRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.PingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.PingRequest,
      com.rustycluster.grpc.RustyClusterProto.PingResponse> getPingMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.PingRequest, com.rustycluster.grpc.RustyClusterProto.PingResponse> getPingMethod;
    if ((getPingMethod = KeyValueServiceGrpc.getPingMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getPingMethod = KeyValueServiceGrpc.getPingMethod) == null) {
          KeyValueServiceGrpc.getPingMethod = getPingMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.PingRequest, com.rustycluster.grpc.RustyClusterProto.PingResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Ping"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.PingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.PingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Ping"))
              .build();
        }
      }
    }
    return getPingMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetRequest,
      com.rustycluster.grpc.RustyClusterProto.SetResponse> getSetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Set",
      requestType = com.rustycluster.grpc.RustyClusterProto.SetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.SetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetRequest,
      com.rustycluster.grpc.RustyClusterProto.SetResponse> getSetMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetRequest, com.rustycluster.grpc.RustyClusterProto.SetResponse> getSetMethod;
    if ((getSetMethod = KeyValueServiceGrpc.getSetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetMethod = KeyValueServiceGrpc.getSetMethod) == null) {
          KeyValueServiceGrpc.getSetMethod = getSetMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.SetRequest, com.rustycluster.grpc.RustyClusterProto.SetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Set"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Set"))
              .build();
        }
      }
    }
    return getSetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.GetRequest,
      com.rustycluster.grpc.RustyClusterProto.GetResponse> getGetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Get",
      requestType = com.rustycluster.grpc.RustyClusterProto.GetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.GetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.GetRequest,
      com.rustycluster.grpc.RustyClusterProto.GetResponse> getGetMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.GetRequest, com.rustycluster.grpc.RustyClusterProto.GetResponse> getGetMethod;
    if ((getGetMethod = KeyValueServiceGrpc.getGetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getGetMethod = KeyValueServiceGrpc.getGetMethod) == null) {
          KeyValueServiceGrpc.getGetMethod = getGetMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.GetRequest, com.rustycluster.grpc.RustyClusterProto.GetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Get"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.GetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.GetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Get"))
              .build();
        }
      }
    }
    return getGetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.DeleteRequest,
      com.rustycluster.grpc.RustyClusterProto.DeleteResponse> getDeleteMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Delete",
      requestType = com.rustycluster.grpc.RustyClusterProto.DeleteRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.DeleteResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.DeleteRequest,
      com.rustycluster.grpc.RustyClusterProto.DeleteResponse> getDeleteMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.DeleteRequest, com.rustycluster.grpc.RustyClusterProto.DeleteResponse> getDeleteMethod;
    if ((getDeleteMethod = KeyValueServiceGrpc.getDeleteMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getDeleteMethod = KeyValueServiceGrpc.getDeleteMethod) == null) {
          KeyValueServiceGrpc.getDeleteMethod = getDeleteMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.DeleteRequest, com.rustycluster.grpc.RustyClusterProto.DeleteResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Delete"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DeleteRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DeleteResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("Delete"))
              .build();
        }
      }
    }
    return getDeleteMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetExRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExResponse> getSetExMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetEx",
      requestType = com.rustycluster.grpc.RustyClusterProto.SetExRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.SetExResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetExRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExResponse> getSetExMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetExRequest, com.rustycluster.grpc.RustyClusterProto.SetExResponse> getSetExMethod;
    if ((getSetExMethod = KeyValueServiceGrpc.getSetExMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetExMethod = KeyValueServiceGrpc.getSetExMethod) == null) {
          KeyValueServiceGrpc.getSetExMethod = getSetExMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.SetExRequest, com.rustycluster.grpc.RustyClusterProto.SetExResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetEx"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("SetEx"))
              .build();
        }
      }
    }
    return getSetExMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse> getSetExpiryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetExpiry",
      requestType = com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse> getSetExpiryMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest, com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse> getSetExpiryMethod;
    if ((getSetExpiryMethod = KeyValueServiceGrpc.getSetExpiryMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetExpiryMethod = KeyValueServiceGrpc.getSetExpiryMethod) == null) {
          KeyValueServiceGrpc.getSetExpiryMethod = getSetExpiryMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest, com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetExpiry"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("SetExpiry"))
              .build();
        }
      }
    }
    return getSetExpiryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.IncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByResponse> getIncrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "IncrBy",
      requestType = com.rustycluster.grpc.RustyClusterProto.IncrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.IncrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.IncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByResponse> getIncrByMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.IncrByRequest, com.rustycluster.grpc.RustyClusterProto.IncrByResponse> getIncrByMethod;
    if ((getIncrByMethod = KeyValueServiceGrpc.getIncrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getIncrByMethod = KeyValueServiceGrpc.getIncrByMethod) == null) {
          KeyValueServiceGrpc.getIncrByMethod = getIncrByMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.IncrByRequest, com.rustycluster.grpc.RustyClusterProto.IncrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "IncrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("IncrBy"))
              .build();
        }
      }
    }
    return getIncrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.DecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.DecrByResponse> getDecrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "DecrBy",
      requestType = com.rustycluster.grpc.RustyClusterProto.DecrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.DecrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.DecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.DecrByResponse> getDecrByMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.DecrByRequest, com.rustycluster.grpc.RustyClusterProto.DecrByResponse> getDecrByMethod;
    if ((getDecrByMethod = KeyValueServiceGrpc.getDecrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getDecrByMethod = KeyValueServiceGrpc.getDecrByMethod) == null) {
          KeyValueServiceGrpc.getDecrByMethod = getDecrByMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.DecrByRequest, com.rustycluster.grpc.RustyClusterProto.DecrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "DecrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DecrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DecrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("DecrBy"))
              .build();
        }
      }
    }
    return getDecrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse> getIncrByFloatMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "IncrByFloat",
      requestType = com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse> getIncrByFloatMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse> getIncrByFloatMethod;
    if ((getIncrByFloatMethod = KeyValueServiceGrpc.getIncrByFloatMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getIncrByFloatMethod = KeyValueServiceGrpc.getIncrByFloatMethod) == null) {
          KeyValueServiceGrpc.getIncrByFloatMethod = getIncrByFloatMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "IncrByFloat"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("IncrByFloat"))
              .build();
        }
      }
    }
    return getIncrByFloatMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HSetRequest,
      com.rustycluster.grpc.RustyClusterProto.HSetResponse> getHSetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HSet",
      requestType = com.rustycluster.grpc.RustyClusterProto.HSetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HSetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HSetRequest,
      com.rustycluster.grpc.RustyClusterProto.HSetResponse> getHSetMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HSetRequest, com.rustycluster.grpc.RustyClusterProto.HSetResponse> getHSetMethod;
    if ((getHSetMethod = KeyValueServiceGrpc.getHSetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHSetMethod = KeyValueServiceGrpc.getHSetMethod) == null) {
          KeyValueServiceGrpc.getHSetMethod = getHSetMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.HSetRequest, com.rustycluster.grpc.RustyClusterProto.HSetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HSet"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HSetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HSetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HSet"))
              .build();
        }
      }
    }
    return getHSetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HGetRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetResponse> getHGetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HGet",
      requestType = com.rustycluster.grpc.RustyClusterProto.HGetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HGetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HGetRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetResponse> getHGetMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HGetRequest, com.rustycluster.grpc.RustyClusterProto.HGetResponse> getHGetMethod;
    if ((getHGetMethod = KeyValueServiceGrpc.getHGetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHGetMethod = KeyValueServiceGrpc.getHGetMethod) == null) {
          KeyValueServiceGrpc.getHGetMethod = getHGetMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.HGetRequest, com.rustycluster.grpc.RustyClusterProto.HGetResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HGet"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HGet"))
              .build();
        }
      }
    }
    return getHGetMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HGetAllRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetAllResponse> getHGetAllMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HGetAll",
      requestType = com.rustycluster.grpc.RustyClusterProto.HGetAllRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HGetAllResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HGetAllRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetAllResponse> getHGetAllMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HGetAllRequest, com.rustycluster.grpc.RustyClusterProto.HGetAllResponse> getHGetAllMethod;
    if ((getHGetAllMethod = KeyValueServiceGrpc.getHGetAllMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHGetAllMethod = KeyValueServiceGrpc.getHGetAllMethod) == null) {
          KeyValueServiceGrpc.getHGetAllMethod = getHGetAllMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.HGetAllRequest, com.rustycluster.grpc.RustyClusterProto.HGetAllResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HGetAll"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetAllRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetAllResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HGetAll"))
              .build();
        }
      }
    }
    return getHGetAllMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HIncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByResponse> getHIncrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HIncrBy",
      requestType = com.rustycluster.grpc.RustyClusterProto.HIncrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HIncrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HIncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByResponse> getHIncrByMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HIncrByRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByResponse> getHIncrByMethod;
    if ((getHIncrByMethod = KeyValueServiceGrpc.getHIncrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHIncrByMethod = KeyValueServiceGrpc.getHIncrByMethod) == null) {
          KeyValueServiceGrpc.getHIncrByMethod = getHIncrByMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.HIncrByRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HIncrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HIncrBy"))
              .build();
        }
      }
    }
    return getHIncrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HDecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HDecrByResponse> getHDecrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HDecrBy",
      requestType = com.rustycluster.grpc.RustyClusterProto.HDecrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HDecrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HDecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HDecrByResponse> getHDecrByMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HDecrByRequest, com.rustycluster.grpc.RustyClusterProto.HDecrByResponse> getHDecrByMethod;
    if ((getHDecrByMethod = KeyValueServiceGrpc.getHDecrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHDecrByMethod = KeyValueServiceGrpc.getHDecrByMethod) == null) {
          KeyValueServiceGrpc.getHDecrByMethod = getHDecrByMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.HDecrByRequest, com.rustycluster.grpc.RustyClusterProto.HDecrByResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HDecrBy"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HDecrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HDecrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HDecrBy"))
              .build();
        }
      }
    }
    return getHDecrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse> getHIncrByFloatMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "HIncrByFloat",
      requestType = com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse> getHIncrByFloatMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse> getHIncrByFloatMethod;
    if ((getHIncrByFloatMethod = KeyValueServiceGrpc.getHIncrByFloatMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHIncrByFloatMethod = KeyValueServiceGrpc.getHIncrByFloatMethod) == null) {
          KeyValueServiceGrpc.getHIncrByFloatMethod = getHIncrByFloatMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "HIncrByFloat"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("HIncrByFloat"))
              .build();
        }
      }
    }
    return getHIncrByFloatMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest,
      com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse> getBatchWriteMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "BatchWrite",
      requestType = com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest,
      com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse> getBatchWriteMethod() {
    io.grpc.MethodDescriptor<com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest, com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse> getBatchWriteMethod;
    if ((getBatchWriteMethod = KeyValueServiceGrpc.getBatchWriteMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getBatchWriteMethod = KeyValueServiceGrpc.getBatchWriteMethod) == null) {
          KeyValueServiceGrpc.getBatchWriteMethod = getBatchWriteMethod =
              io.grpc.MethodDescriptor.<com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest, com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "BatchWrite"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier("BatchWrite"))
              .build();
        }
      }
    }
    return getBatchWriteMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static KeyValueServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceStub>() {
        @java.lang.Override
        public KeyValueServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new KeyValueServiceStub(channel, callOptions);
        }
      };
    return KeyValueServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static KeyValueServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceBlockingStub>() {
        @java.lang.Override
        public KeyValueServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new KeyValueServiceBlockingStub(channel, callOptions);
        }
      };
    return KeyValueServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static KeyValueServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<KeyValueServiceFutureStub>() {
        @java.lang.Override
        public KeyValueServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new KeyValueServiceFutureStub(channel, callOptions);
        }
      };
    return KeyValueServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    default void authenticate(com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAuthenticateMethod(), responseObserver);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    default void ping(com.rustycluster.grpc.RustyClusterProto.PingRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.PingResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getPingMethod(), responseObserver);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    default void set(com.rustycluster.grpc.RustyClusterProto.SetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetMethod(), responseObserver);
    }

    /**
     */
    default void get(com.rustycluster.grpc.RustyClusterProto.GetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.GetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetMethod(), responseObserver);
    }

    /**
     */
    default void delete(com.rustycluster.grpc.RustyClusterProto.DeleteRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.DeleteResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteMethod(), responseObserver);
    }

    /**
     */
    default void setEx(com.rustycluster.grpc.RustyClusterProto.SetExRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetExResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetExMethod(), responseObserver);
    }

    /**
     */
    default void setExpiry(com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetExpiryMethod(), responseObserver);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    default void incrBy(com.rustycluster.grpc.RustyClusterProto.IncrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.IncrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getIncrByMethod(), responseObserver);
    }

    /**
     */
    default void decrBy(com.rustycluster.grpc.RustyClusterProto.DecrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.DecrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDecrByMethod(), responseObserver);
    }

    /**
     */
    default void incrByFloat(com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getIncrByFloatMethod(), responseObserver);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    default void hSet(com.rustycluster.grpc.RustyClusterProto.HSetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HSetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHSetMethod(), responseObserver);
    }

    /**
     */
    default void hGet(com.rustycluster.grpc.RustyClusterProto.HGetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HGetResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHGetMethod(), responseObserver);
    }

    /**
     */
    default void hGetAll(com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HGetAllResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHGetAllMethod(), responseObserver);
    }

    /**
     */
    default void hIncrBy(com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HIncrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHIncrByMethod(), responseObserver);
    }

    /**
     */
    default void hDecrBy(com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HDecrByResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHDecrByMethod(), responseObserver);
    }

    /**
     */
    default void hIncrByFloat(com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHIncrByFloatMethod(), responseObserver);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    default void batchWrite(com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getBatchWriteMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service KeyValueService.
   */
  public static abstract class KeyValueServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return KeyValueServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceStub
      extends io.grpc.stub.AbstractAsyncStub<KeyValueServiceStub> {
    private KeyValueServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected KeyValueServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new KeyValueServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    public void authenticate(com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getAuthenticateMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    public void ping(com.rustycluster.grpc.RustyClusterProto.PingRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.PingResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getPingMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    public void set(com.rustycluster.grpc.RustyClusterProto.SetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void get(com.rustycluster.grpc.RustyClusterProto.GetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.GetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void delete(com.rustycluster.grpc.RustyClusterProto.DeleteRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.DeleteResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void setEx(com.rustycluster.grpc.RustyClusterProto.SetExRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetExResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetExMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void setExpiry(com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetExpiryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    public void incrBy(com.rustycluster.grpc.RustyClusterProto.IncrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.IncrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getIncrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void decrBy(com.rustycluster.grpc.RustyClusterProto.DecrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.DecrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDecrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void incrByFloat(com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getIncrByFloatMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    public void hSet(com.rustycluster.grpc.RustyClusterProto.HSetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HSetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHSetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hGet(com.rustycluster.grpc.RustyClusterProto.HGetRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HGetResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHGetMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hGetAll(com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HGetAllResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHGetAllMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hIncrBy(com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HIncrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHIncrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hDecrBy(com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HDecrByResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHDecrByMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void hIncrByFloat(com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHIncrByFloatMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    public void batchWrite(com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request,
        io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getBatchWriteMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<KeyValueServiceBlockingStub> {
    private KeyValueServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected KeyValueServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new KeyValueServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    public com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse authenticate(com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getAuthenticateMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    public com.rustycluster.grpc.RustyClusterProto.PingResponse ping(com.rustycluster.grpc.RustyClusterProto.PingRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getPingMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    public com.rustycluster.grpc.RustyClusterProto.SetResponse set(com.rustycluster.grpc.RustyClusterProto.SetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.GetResponse get(com.rustycluster.grpc.RustyClusterProto.GetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.DeleteResponse delete(com.rustycluster.grpc.RustyClusterProto.DeleteRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.SetExResponse setEx(com.rustycluster.grpc.RustyClusterProto.SetExRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetExMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse setExpiry(com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetExpiryMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    public com.rustycluster.grpc.RustyClusterProto.IncrByResponse incrBy(com.rustycluster.grpc.RustyClusterProto.IncrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getIncrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.DecrByResponse decrBy(com.rustycluster.grpc.RustyClusterProto.DecrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDecrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse incrByFloat(com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getIncrByFloatMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    public com.rustycluster.grpc.RustyClusterProto.HSetResponse hSet(com.rustycluster.grpc.RustyClusterProto.HSetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHSetMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HGetResponse hGet(com.rustycluster.grpc.RustyClusterProto.HGetRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHGetMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HGetAllResponse hGetAll(com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHGetAllMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HIncrByResponse hIncrBy(com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHIncrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HDecrByResponse hDecrBy(com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHDecrByMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse hIncrByFloat(com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHIncrByFloatMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    public com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse batchWrite(com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getBatchWriteMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<KeyValueServiceFutureStub> {
    private KeyValueServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected KeyValueServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new KeyValueServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * Authentication operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse> authenticate(
        com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getAuthenticateMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * System operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.PingResponse> ping(
        com.rustycluster.grpc.RustyClusterProto.PingRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getPingMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * String operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.SetResponse> set(
        com.rustycluster.grpc.RustyClusterProto.SetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.GetResponse> get(
        com.rustycluster.grpc.RustyClusterProto.GetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.DeleteResponse> delete(
        com.rustycluster.grpc.RustyClusterProto.DeleteRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.SetExResponse> setEx(
        com.rustycluster.grpc.RustyClusterProto.SetExRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetExMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse> setExpiry(
        com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetExpiryMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Numeric operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.IncrByResponse> incrBy(
        com.rustycluster.grpc.RustyClusterProto.IncrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getIncrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.DecrByResponse> decrBy(
        com.rustycluster.grpc.RustyClusterProto.DecrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDecrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse> incrByFloat(
        com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getIncrByFloatMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Hash operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.HSetResponse> hSet(
        com.rustycluster.grpc.RustyClusterProto.HSetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHSetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.HGetResponse> hGet(
        com.rustycluster.grpc.RustyClusterProto.HGetRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHGetMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.HGetAllResponse> hGetAll(
        com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHGetAllMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.HIncrByResponse> hIncrBy(
        com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHIncrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.HDecrByResponse> hDecrBy(
        com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHDecrByMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse> hIncrByFloat(
        com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHIncrByFloatMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Batch operations
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse> batchWrite(
        com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getBatchWriteMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_AUTHENTICATE = 0;
  private static final int METHODID_PING = 1;
  private static final int METHODID_SET = 2;
  private static final int METHODID_GET = 3;
  private static final int METHODID_DELETE = 4;
  private static final int METHODID_SET_EX = 5;
  private static final int METHODID_SET_EXPIRY = 6;
  private static final int METHODID_INCR_BY = 7;
  private static final int METHODID_DECR_BY = 8;
  private static final int METHODID_INCR_BY_FLOAT = 9;
  private static final int METHODID_HSET = 10;
  private static final int METHODID_HGET = 11;
  private static final int METHODID_HGET_ALL = 12;
  private static final int METHODID_HINCR_BY = 13;
  private static final int METHODID_HDECR_BY = 14;
  private static final int METHODID_HINCR_BY_FLOAT = 15;
  private static final int METHODID_BATCH_WRITE = 16;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_AUTHENTICATE:
          serviceImpl.authenticate((com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse>) responseObserver);
          break;
        case METHODID_PING:
          serviceImpl.ping((com.rustycluster.grpc.RustyClusterProto.PingRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.PingResponse>) responseObserver);
          break;
        case METHODID_SET:
          serviceImpl.set((com.rustycluster.grpc.RustyClusterProto.SetRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetResponse>) responseObserver);
          break;
        case METHODID_GET:
          serviceImpl.get((com.rustycluster.grpc.RustyClusterProto.GetRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.GetResponse>) responseObserver);
          break;
        case METHODID_DELETE:
          serviceImpl.delete((com.rustycluster.grpc.RustyClusterProto.DeleteRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.DeleteResponse>) responseObserver);
          break;
        case METHODID_SET_EX:
          serviceImpl.setEx((com.rustycluster.grpc.RustyClusterProto.SetExRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetExResponse>) responseObserver);
          break;
        case METHODID_SET_EXPIRY:
          serviceImpl.setExpiry((com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse>) responseObserver);
          break;
        case METHODID_INCR_BY:
          serviceImpl.incrBy((com.rustycluster.grpc.RustyClusterProto.IncrByRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.IncrByResponse>) responseObserver);
          break;
        case METHODID_DECR_BY:
          serviceImpl.decrBy((com.rustycluster.grpc.RustyClusterProto.DecrByRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.DecrByResponse>) responseObserver);
          break;
        case METHODID_INCR_BY_FLOAT:
          serviceImpl.incrByFloat((com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse>) responseObserver);
          break;
        case METHODID_HSET:
          serviceImpl.hSet((com.rustycluster.grpc.RustyClusterProto.HSetRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HSetResponse>) responseObserver);
          break;
        case METHODID_HGET:
          serviceImpl.hGet((com.rustycluster.grpc.RustyClusterProto.HGetRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HGetResponse>) responseObserver);
          break;
        case METHODID_HGET_ALL:
          serviceImpl.hGetAll((com.rustycluster.grpc.RustyClusterProto.HGetAllRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HGetAllResponse>) responseObserver);
          break;
        case METHODID_HINCR_BY:
          serviceImpl.hIncrBy((com.rustycluster.grpc.RustyClusterProto.HIncrByRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HIncrByResponse>) responseObserver);
          break;
        case METHODID_HDECR_BY:
          serviceImpl.hDecrBy((com.rustycluster.grpc.RustyClusterProto.HDecrByRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HDecrByResponse>) responseObserver);
          break;
        case METHODID_HINCR_BY_FLOAT:
          serviceImpl.hIncrByFloat((com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse>) responseObserver);
          break;
        case METHODID_BATCH_WRITE:
          serviceImpl.batchWrite((com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest) request,
              (io.grpc.stub.StreamObserver<com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getAuthenticateMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest,
              com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse>(
                service, METHODID_AUTHENTICATE)))
        .addMethod(
          getPingMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.PingRequest,
              com.rustycluster.grpc.RustyClusterProto.PingResponse>(
                service, METHODID_PING)))
        .addMethod(
          getSetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.SetRequest,
              com.rustycluster.grpc.RustyClusterProto.SetResponse>(
                service, METHODID_SET)))
        .addMethod(
          getGetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.GetRequest,
              com.rustycluster.grpc.RustyClusterProto.GetResponse>(
                service, METHODID_GET)))
        .addMethod(
          getDeleteMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.DeleteRequest,
              com.rustycluster.grpc.RustyClusterProto.DeleteResponse>(
                service, METHODID_DELETE)))
        .addMethod(
          getSetExMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.SetExRequest,
              com.rustycluster.grpc.RustyClusterProto.SetExResponse>(
                service, METHODID_SET_EX)))
        .addMethod(
          getSetExpiryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest,
              com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse>(
                service, METHODID_SET_EXPIRY)))
        .addMethod(
          getIncrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.IncrByRequest,
              com.rustycluster.grpc.RustyClusterProto.IncrByResponse>(
                service, METHODID_INCR_BY)))
        .addMethod(
          getDecrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.DecrByRequest,
              com.rustycluster.grpc.RustyClusterProto.DecrByResponse>(
                service, METHODID_DECR_BY)))
        .addMethod(
          getIncrByFloatMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest,
              com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse>(
                service, METHODID_INCR_BY_FLOAT)))
        .addMethod(
          getHSetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.HSetRequest,
              com.rustycluster.grpc.RustyClusterProto.HSetResponse>(
                service, METHODID_HSET)))
        .addMethod(
          getHGetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.HGetRequest,
              com.rustycluster.grpc.RustyClusterProto.HGetResponse>(
                service, METHODID_HGET)))
        .addMethod(
          getHGetAllMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.HGetAllRequest,
              com.rustycluster.grpc.RustyClusterProto.HGetAllResponse>(
                service, METHODID_HGET_ALL)))
        .addMethod(
          getHIncrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.HIncrByRequest,
              com.rustycluster.grpc.RustyClusterProto.HIncrByResponse>(
                service, METHODID_HINCR_BY)))
        .addMethod(
          getHDecrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.HDecrByRequest,
              com.rustycluster.grpc.RustyClusterProto.HDecrByResponse>(
                service, METHODID_HDECR_BY)))
        .addMethod(
          getHIncrByFloatMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest,
              com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse>(
                service, METHODID_HINCR_BY_FLOAT)))
        .addMethod(
          getBatchWriteMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest,
              com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse>(
                service, METHODID_BATCH_WRITE)))
        .build();
  }

  private static abstract class KeyValueServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    KeyValueServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.rustycluster.grpc.RustyClusterProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("KeyValueService");
    }
  }

  private static final class KeyValueServiceFileDescriptorSupplier
      extends KeyValueServiceBaseDescriptorSupplier {
    KeyValueServiceFileDescriptorSupplier() {}
  }

  private static final class KeyValueServiceMethodDescriptorSupplier
      extends KeyValueServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    KeyValueServiceMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (KeyValueServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new KeyValueServiceFileDescriptorSupplier())
              .addMethod(getAuthenticateMethod())
              .addMethod(getPingMethod())
              .addMethod(getSetMethod())
              .addMethod(getGetMethod())
              .addMethod(getDeleteMethod())
              .addMethod(getSetExMethod())
              .addMethod(getSetExpiryMethod())
              .addMethod(getIncrByMethod())
              .addMethod(getDecrByMethod())
              .addMethod(getIncrByFloatMethod())
              .addMethod(getHSetMethod())
              .addMethod(getHGetMethod())
              .addMethod(getHGetAllMethod())
              .addMethod(getHIncrByMethod())
              .addMethod(getHDecrByMethod())
              .addMethod(getHIncrByFloatMethod())
              .addMethod(getBatchWriteMethod())
              .build();
        }
      }
    }
    return result;
  }
}

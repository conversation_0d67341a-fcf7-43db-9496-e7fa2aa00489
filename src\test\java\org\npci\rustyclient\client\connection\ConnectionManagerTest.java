package org.npci.rustyclient.client.connection;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.NodeRole;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

import org.npci.rustyclient.client.exception.NoAvailableNodesException;
import rustycluster.KeyValueServiceGrpc;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConnectionManagerTest {

    @Mock
    private ConnectionPool connectionPool;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub stub;

    private ConnectionManager connectionManager;
    private RustyClusterClientConfig config;

    @BeforeEach
    void setUp() {
        // Create a test configuration with three nodes
        config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052", "localhost:50053")
                .maxRetries(2)
                .build();

        // Create a ConnectionManager with a mocked ConnectionPool
        connectionManager = new ConnectionManager(config, connectionPool);
    }

    @Test
    @DisplayName("Should execute operation successfully on the primary node")
    void shouldExecuteOperationSuccessfullyOnPrimaryNode() throws Exception {
        // Given
        when(connectionPool.borrowStub(any(NodeConfig.class))).thenReturn(stub);

        // When
        String result = connectionManager.executeWithFailover(s -> "success");

        // Then
        assertThat(result).isEqualTo("success");
        verify(connectionPool).borrowStub(argThat(node ->
                node.host().equals("localhost") &&
                node.port() == 50051 &&
                node.role() == NodeRole.PRIMARY));
        verify(connectionPool).returnStub(any(NodeConfig.class), eq(stub));
    }

    @Test
    @DisplayName("Should fail over to secondary node when primary fails")
    void shouldFailOverToSecondaryNodeWhenPrimaryFails() throws Exception {
        // Given
        NodeConfig primaryNode = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);
        NodeConfig secondaryNode = new NodeConfig("localhost", 50052, NodeRole.SECONDARY);

        // First call fails, second call succeeds
        when(connectionPool.borrowStub(eq(primaryNode)))
                .thenThrow(new RuntimeException("Primary node failed"));
        when(connectionPool.borrowStub(eq(secondaryNode)))
                .thenReturn(stub);

        // When
        String result = connectionManager.executeWithFailover(s -> "success");

        // Then
        assertThat(result).isEqualTo("success");
        verify(connectionPool, atLeast(1)).borrowStub(primaryNode);
        verify(connectionPool, atLeast(1)).borrowStub(secondaryNode);
        verify(connectionPool, atLeast(1)).returnStub(eq(secondaryNode), eq(stub));
    }

    @Test
    @DisplayName("Should fail over to tertiary node when primary and secondary fail")
    void shouldFailOverToTertiaryNodeWhenPrimaryAndSecondaryFail() throws Exception {
        // Given
        NodeConfig primaryNode = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);
        NodeConfig secondaryNode = new NodeConfig("localhost", 50052, NodeRole.SECONDARY);
        NodeConfig tertiaryNode = new NodeConfig("localhost", 50053, NodeRole.TERTIARY);

        // First and second calls fail, third call succeeds
        when(connectionPool.borrowStub(eq(primaryNode)))
                .thenThrow(new RuntimeException("Primary node failed"));
        when(connectionPool.borrowStub(eq(secondaryNode)))
                .thenThrow(new RuntimeException("Secondary node failed"));
        when(connectionPool.borrowStub(eq(tertiaryNode)))
                .thenReturn(stub);

        // When
        String result = connectionManager.executeWithFailover(s -> "success");

        // Then
        assertThat(result).isEqualTo("success");
        verify(connectionPool, atLeast(1)).borrowStub(primaryNode);
        verify(connectionPool, atLeast(1)).borrowStub(secondaryNode);
        verify(connectionPool, atLeast(1)).borrowStub(tertiaryNode);
        verify(connectionPool, atLeast(1)).returnStub(eq(tertiaryNode), eq(stub));
    }

    @Test
    @DisplayName("Should throw NoAvailableNodesException when all nodes fail")
    void shouldThrowNoAvailableNodesExceptionWhenAllNodesFail() throws Exception {
        // Given
        when(connectionPool.borrowStub(any(NodeConfig.class)))
                .thenThrow(new RuntimeException("Node failed"));

        // When/Then
        assertThatThrownBy(() -> connectionManager.executeWithFailover(s -> "success"))
                .isInstanceOf(NoAvailableNodesException.class)
                .hasMessageContaining("Operation failed after");

        // Verify that we tried multiple times (including health checks)
        verify(connectionPool, atLeast(3)).borrowStub(any(NodeConfig.class));
    }

    @Test
    @DisplayName("Should respect max retries configuration")
    void shouldRespectMaxRetriesConfiguration() throws Exception {
        // Given
        when(connectionPool.borrowStub(any(NodeConfig.class)))
                .thenThrow(new RuntimeException("Node failed"));

        // When/Then
        assertThatThrownBy(() -> connectionManager.executeWithFailover(s -> "success"))
                .isInstanceOf(NoAvailableNodesException.class);

        // Verify that we tried multiple times (including health checks and retries)
        verify(connectionPool, atLeast(3)).borrowStub(any(NodeConfig.class));
    }

    @Test
    @DisplayName("Should close connection pool when closed")
    void shouldCloseConnectionPoolWhenClosed() {
        // When
        connectionManager.close();

        // Then
        verify(connectionPool).close();
    }
}

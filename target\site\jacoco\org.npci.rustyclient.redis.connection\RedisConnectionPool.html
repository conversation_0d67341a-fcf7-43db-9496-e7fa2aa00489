<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RedisConnectionPool</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">org.npci.rustyclient.redis.connection</a> &gt; <span class="el_class">RedisConnectionPool</span></div><h1>RedisConnectionPool</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">134 of 279</td><td class="ctr2">51%</td><td class="bar">14 of 18</td><td class="ctr2">22%</td><td class="ctr1">12</td><td class="ctr2">18</td><td class="ctr1">34</td><td class="ctr2">69</td><td class="ctr1">5</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a2"><a href="RedisConnectionPool.java.html#L123" class="el_method">getPoolStats(RedisNodeConfig)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="55" alt="55"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="RedisConnectionPool.java.html#L106" class="el_method">invalidateConnection(RedisNodeConfig, Jedis)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="22" alt="22"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h1">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="RedisConnectionPool.java.html#L78" class="el_method">borrowConnection(RedisNodeConfig)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="19" alt="19"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">4</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="RedisConnectionPool.java.html#L144" class="el_method">isConnectionValid(Jedis)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="18" alt="18"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h2">7</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="RedisConnectionPool.java.html#L93" class="el_method">returnConnection(RedisNodeConfig, Jedis)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="15" alt="15"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h4">4</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="RedisConnectionPool.java.html#L160" class="el_method">close()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="26" alt="26"/></td><td class="ctr2" id="c3">83%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">2</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="RedisConnectionPool.java.html#L35" class="el_method">initializePools()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="102" alt="102"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i0">21</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="RedisConnectionPool.java.html#L28" class="el_method">RedisConnectionPool(RedisClientConfig)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="13" alt="13"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a8"><a href="RedisConnectionPool.java.html#L18" class="el_method">static {...}</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
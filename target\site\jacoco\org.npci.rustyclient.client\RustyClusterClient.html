<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClient</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">org.npci.rustyclient.client</a> &gt; <span class="el_class">RustyClusterClient</span></div><h1>RustyClusterClient</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,147 of 1,489</td><td class="ctr2">22%</td><td class="bar">33 of 40</td><td class="ctr2">17%</td><td class="ctr1">106</td><td class="ctr2">133</td><td class="ctr1">287</td><td class="ctr2">386</td><td class="ctr1">88</td><td class="ctr2">113</td></tr></tfoot><tbody><tr><td id="a14"><a href="RustyClusterClient.java.html#L752" class="el_method">evalSha(String, List, List, boolean)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="58" alt="58"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a33"><a href="RustyClusterClient.java.html#L417" class="el_method">hIncrBy(String, String, long, boolean)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="42" alt="42"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a23"><a href="RustyClusterClient.java.html#L454" class="el_method">hDecrBy(String, String, long, boolean)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="42" alt="42"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h4">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a35"><a href="RustyClusterClient.java.html#L491" class="el_method">hIncrByFloat(String, String, double, boolean)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="40" alt="40"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h6">9</td><td class="ctr2" id="i8">9</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a37"><a href="RustyClusterClient.java.html#L600" class="el_method">hMSet(String, Map, boolean)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="37" alt="37"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a109"><a href="RustyClusterClient.java.html#L683" class="el_method">setNX(String, String, boolean)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="34" alt="34"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a94"><a href="RustyClusterClient.java.html#L724" class="el_method">loadScript(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="34" alt="34"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h7">9</td><td class="ctr2" id="i9">9</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a98"><a href="RustyClusterClient.java.html#L53" class="el_method">RustyClusterClient(RustyClusterClientConfig)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="31" alt="31"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h15">7</td><td class="ctr2" id="i19">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a41"><a href="RustyClusterClient.java.html#L342" class="el_method">hSet(String, String, String, boolean)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="29" alt="29"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a0"><a href="RustyClusterClient.java.html#L554" class="el_method">authenticate()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="28" alt="28"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">8</td><td class="ctr2" id="i11">8</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a39"><a href="RustyClusterClient.java.html#L1045" class="el_method">hMSetAsync(String, Map, boolean)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="27" alt="27"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">6</td><td class="ctr2" id="i28">6</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a16"><a href="RustyClusterClient.java.html#L1156" class="el_method">evalShaAsync(String, List, List, boolean)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="27" alt="27"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h28">3</td><td class="ctr2" id="i40">3</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a107"><a href="RustyClusterClient.java.html#L209" class="el_method">setExpiry(String, long, boolean)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h10">8</td><td class="ctr2" id="i12">8</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a8"><a href="RustyClusterClient.java.html#L275" class="el_method">decrBy(String, long, boolean)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h11">8</td><td class="ctr2" id="i13">8</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a49"><a href="RustyClusterClient.java.html#L308" class="el_method">incrByFloat(String, double, boolean)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h12">8</td><td class="ctr2" id="i14">8</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a96"><a href="RustyClusterClient.java.html#L809" class="el_method">ping()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="25" alt="25"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h16">7</td><td class="ctr2" id="i20">7</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a43"><a href="RustyClusterClient.java.html#L973" class="el_method">hSetAsync(String, String, String, boolean)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="24" alt="24"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h8">9</td><td class="ctr2" id="i10">9</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a47"><a href="RustyClusterClient.java.html#L940" class="el_method">incrByAsync(String, long, boolean)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="23" alt="23"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h13">8</td><td class="ctr2" id="i15">8</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a103"><a href="RustyClusterClient.java.html#L833" class="el_method">setAsync(String, String, boolean)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="21" alt="21"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h14">8</td><td class="ctr2" id="i16">8</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a5"><a href="RustyClusterClient.java.html#L910" class="el_method">batchWriteAsync(List, boolean)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="21" alt="21"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h17">7</td><td class="ctr2" id="i21">7</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a31"><a href="RustyClusterClient.java.html#L1006" class="el_method">hGetAsync(String, String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="20" alt="20"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h18">7</td><td class="ctr2" id="i22">7</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a12"><a href="RustyClusterClient.java.html#L881" class="el_method">deleteAsync(String, boolean)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="19" alt="19"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h19">7</td><td class="ctr2" id="i23">7</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a1"><a href="RustyClusterClient.java.html#L1214" class="el_method">authenticateAsync()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="18" alt="18"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h24">5</td><td class="ctr2" id="i35">5</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a20"><a href="RustyClusterClient.java.html#L863" class="el_method">getAsync(String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="17" alt="17"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h21">6</td><td class="ctr2" id="i29">6</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a30"><a href="RustyClusterClient.java.html#L1024" class="el_method">hGetAllAsync(String)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="17" alt="17"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h22">6</td><td class="ctr2" id="i30">6</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a18"><a href="RustyClusterClient.java.html#L1089" class="el_method">existsAsync(String)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="17" alt="17"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h23">6</td><td class="ctr2" id="i31">6</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a97"><a href="RustyClusterClient.java.html#L1196" class="el_method">pingAsync()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="16" alt="16"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h25">5</td><td class="ctr2" id="i36">5</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a24"><a href="RustyClusterClient.java.html#L792" class="el_method">healthCheck()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="14" alt="14"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h26">5</td><td class="ctr2" id="i37">5</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a111"><a href="RustyClusterClient.java.html#L1109" class="el_method">setNXAsync(String, String, boolean)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="14" alt="14"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h31">2</td><td class="ctr2" id="i43">2</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a52"><a href="RustyClusterClient.java.html#L1227" class="el_method">lambda$authenticateAsync$42()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="13" alt="13"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h27">4</td><td class="ctr2" id="i39">4</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a93"><a href="RustyClusterClient.java.html#L1113" class="el_method">lambda$setNXAsync$38(String, String, boolean, Boolean)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="13" alt="13"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h29">3</td><td class="ctr2" id="i41">3</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a27"><a href="RustyClusterClient.java.html#L1076" class="el_method">hExistsAsync(String, String)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="12" alt="12"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h32">2</td><td class="ctr2" id="i44">2</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a95"><a href="RustyClusterClient.java.html#L1138" class="el_method">loadScriptAsync(String)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="12" alt="12"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h30">3</td><td class="ctr2" id="i42">3</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a77"><a href="RustyClusterClient.java.html#L1050" class="el_method">lambda$hMSetAsync$33(String, boolean, Map.Entry)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="11" alt="11"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a61"><a href="RustyClusterClient.java.html#L1097" class="el_method">lambda$existsAsync$37(Rustycluster.ExistsResponse)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="10" alt="10"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a73"><a href="RustyClusterClient.java.html#L1014" class="el_method">lambda$hGetAsync$31(Rustycluster.HGetResponse)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="10" alt="10"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a64"><a href="RustyClusterClient.java.html#L870" class="el_method">lambda$getAsync$25(Rustycluster.GetResponse)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="10" alt="10"/></td><td class="ctr2" id="c61">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a25"><a href="RustyClusterClient.java.html#L1181" class="el_method">healthCheckAsync()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/></td><td class="ctr2" id="c62">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h33">2</td><td class="ctr2" id="i45">2</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a87"><a href="RustyClusterClient.java.html#L1203" class="el_method">lambda$pingAsync$41(Throwable)</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/></td><td class="ctr2" id="c63">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h34">2</td><td class="ctr2" id="i46">2</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a66"><a href="RustyClusterClient.java.html#L1185" class="el_method">lambda$healthCheckAsync$39(Throwable)</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/></td><td class="ctr2" id="c64">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h35">2</td><td class="ctr2" id="i47">2</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a40"><a href="RustyClusterClient.java.html#L366" class="el_method">hSet(String, String, String)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c65">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a32"><a href="RustyClusterClient.java.html#L441" class="el_method">hIncrBy(String, String, long)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c66">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a22"><a href="RustyClusterClient.java.html#L478" class="el_method">hDecrBy(String, String, long)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c67">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a34"><a href="RustyClusterClient.java.html#L514" class="el_method">hIncrByFloat(String, String, double)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c68">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a13"><a href="RustyClusterClient.java.html#L783" class="el_method">evalSha(String, List, List)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c69">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a42"><a href="RustyClusterClient.java.html#L995" class="el_method">hSetAsync(String, String, String)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c70">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a15"><a href="RustyClusterClient.java.html#L1172" class="el_method">evalShaAsync(String, List, List)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c71">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a68"><a href="RustyClusterClient.java.html#L1079" class="el_method">lambda$hExistsAsync$35(String)</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c72">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a28"><a href="RustyClusterClient.java.html#L377" class="el_method">hGet(String, String)</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="47" height="10" title="23" alt="23"/></td><td class="ctr2" id="c24">79%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">25%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h89">0</td><td class="ctr2" id="i24">7</td><td class="ctr1" id="j88">0</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a106"><a href="RustyClusterClient.java.html#L230" class="el_method">setExpiry(String, long)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c73">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i60">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a7"><a href="RustyClusterClient.java.html#L296" class="el_method">decrBy(String, long)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c74">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i61">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a48"><a href="RustyClusterClient.java.html#L329" class="el_method">incrByFloat(String, double)</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c75">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i62">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a36"><a href="RustyClusterClient.java.html#L629" class="el_method">hMSet(String, Map)</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c76">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a108"><a href="RustyClusterClient.java.html#L712" class="el_method">setNX(String, String)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c77">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a102"><a href="RustyClusterClient.java.html#L853" class="el_method">setAsync(String, String)</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c78">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a46"><a href="RustyClusterClient.java.html#L960" class="el_method">incrByAsync(String, long)</a></td><td class="bar" id="b55"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c79">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h55">1</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a38"><a href="RustyClusterClient.java.html#L1065" class="el_method">hMSetAsync(String, Map)</a></td><td class="bar" id="b56"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c80">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h56">1</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a110"><a href="RustyClusterClient.java.html#L1128" class="el_method">setNXAsync(String, String)</a></td><td class="bar" id="b57"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c81">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h57">1</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a78"><a href="RustyClusterClient.java.html#L1054" class="el_method">lambda$hMSetAsync$34(List, Void)</a></td><td class="bar" id="b58"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c82">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h58">1</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a51"><a href="RustyClusterClient.java.html#L564" class="el_method">lambda$authenticate$15(KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b59"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c83">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h59">1</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a26"><a href="RustyClusterClient.java.html#L660" class="el_method">hExists(String, String)</a></td><td class="bar" id="b60"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="47" height="10" title="23" alt="23"/></td><td class="ctr2" id="c22">82%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">25%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h90">0</td><td class="ctr2" id="i25">7</td><td class="ctr1" id="j89">0</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a17"><a href="RustyClusterClient.java.html#L639" class="el_method">exists(String)</a></td><td class="bar" id="b61"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="20" alt="20"/></td><td class="ctr2" id="c23">80%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">25%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h91">0</td><td class="ctr2" id="i32">6</td><td class="ctr1" id="j90">0</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a11"><a href="RustyClusterClient.java.html#L899" class="el_method">deleteAsync(String)</a></td><td class="bar" id="b62"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c84">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h60">1</td><td class="ctr2" id="i71">1</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a4"><a href="RustyClusterClient.java.html#L928" class="el_method">batchWriteAsync(List)</a></td><td class="bar" id="b63"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c85">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h61">1</td><td class="ctr2" id="i72">1</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a50"><a href="RustyClusterClient.java.html#L577" class="el_method">isAuthenticated()</a></td><td class="bar" id="b64"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c86">0%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f64">1</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h62">1</td><td class="ctr2" id="i73">1</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a21"><a href="RustyClusterClient.java.html#L586" class="el_method">getSessionToken()</a></td><td class="bar" id="b65"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c87">0%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f65">1</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h63">1</td><td class="ctr2" id="i74">1</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a86"><a href="RustyClusterClient.java.html#L1200" class="el_method">lambda$pingAsync$40(Rustycluster.PingRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b66"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c88">0%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f66">1</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h64">1</td><td class="ctr2" id="i75">1</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k66">1</td></tr><tr><td id="a60"><a href="RustyClusterClient.java.html#L1096" class="el_method">lambda$existsAsync$36(Rustycluster.ExistsRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b67"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c89">0%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f67">1</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h65">1</td><td class="ctr2" id="i76">1</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k67">1</td></tr><tr><td id="a71"><a href="RustyClusterClient.java.html#L1030" class="el_method">lambda$hGetAllAsync$32(Rustycluster.HGetAllRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b68"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c90">0%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f68">1</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h66">1</td><td class="ctr2" id="i77">1</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k68">1</td></tr><tr><td id="a72"><a href="RustyClusterClient.java.html#L1013" class="el_method">lambda$hGetAsync$30(Rustycluster.HGetRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b69"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c91">0%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f69">1</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h67">1</td><td class="ctr2" id="i78">1</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k69">1</td></tr><tr><td id="a80"><a href="RustyClusterClient.java.html#L982" class="el_method">lambda$hSetAsync$29(Rustycluster.HSetRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b70"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c92">0%</td><td class="bar" id="d71"/><td class="ctr2" id="e71">n/a</td><td class="ctr1" id="f70">1</td><td class="ctr2" id="g71">1</td><td class="ctr1" id="h68">1</td><td class="ctr2" id="i79">1</td><td class="ctr1" id="j67">1</td><td class="ctr2" id="k70">1</td></tr><tr><td id="a82"><a href="RustyClusterClient.java.html#L948" class="el_method">lambda$incrByAsync$28(Rustycluster.IncrByRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b71"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c93">0%</td><td class="bar" id="d72"/><td class="ctr2" id="e72">n/a</td><td class="ctr1" id="f71">1</td><td class="ctr2" id="g72">1</td><td class="ctr1" id="h69">1</td><td class="ctr2" id="i80">1</td><td class="ctr1" id="j68">1</td><td class="ctr2" id="k71">1</td></tr><tr><td id="a54"><a href="RustyClusterClient.java.html#L917" class="el_method">lambda$batchWriteAsync$27(Rustycluster.BatchWriteRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b72"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c94">0%</td><td class="bar" id="d73"/><td class="ctr2" id="e73">n/a</td><td class="ctr1" id="f72">1</td><td class="ctr2" id="g73">1</td><td class="ctr1" id="h70">1</td><td class="ctr2" id="i81">1</td><td class="ctr1" id="j69">1</td><td class="ctr2" id="k72">1</td></tr><tr><td id="a57"><a href="RustyClusterClient.java.html#L888" class="el_method">lambda$deleteAsync$26(Rustycluster.DeleteRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b73"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c95">0%</td><td class="bar" id="d74"/><td class="ctr2" id="e74">n/a</td><td class="ctr1" id="f73">1</td><td class="ctr2" id="g74">1</td><td class="ctr1" id="h71">1</td><td class="ctr2" id="i82">1</td><td class="ctr1" id="j70">1</td><td class="ctr2" id="k73">1</td></tr><tr><td id="a63"><a href="RustyClusterClient.java.html#L869" class="el_method">lambda$getAsync$24(Rustycluster.GetRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b74"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c96">0%</td><td class="bar" id="d75"/><td class="ctr2" id="e75">n/a</td><td class="ctr1" id="f74">1</td><td class="ctr2" id="g75">1</td><td class="ctr1" id="h72">1</td><td class="ctr2" id="i83">1</td><td class="ctr1" id="j71">1</td><td class="ctr2" id="k74">1</td></tr><tr><td id="a89"><a href="RustyClusterClient.java.html#L841" class="el_method">lambda$setAsync$23(Rustycluster.SetRequest, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b75"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c97">0%</td><td class="bar" id="d76"/><td class="ctr2" id="e76">n/a</td><td class="ctr1" id="f75">1</td><td class="ctr2" id="g76">1</td><td class="ctr1" id="h73">1</td><td class="ctr2" id="i84">1</td><td class="ctr1" id="j72">1</td><td class="ctr2" id="k75">1</td></tr><tr><td id="a85"><a href="RustyClusterClient.java.html#L814" class="el_method">lambda$ping$22(Rustycluster.PingRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b76"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c98">0%</td><td class="bar" id="d77"/><td class="ctr2" id="e77">n/a</td><td class="ctr1" id="f76">1</td><td class="ctr2" id="g77">1</td><td class="ctr1" id="h74">1</td><td class="ctr2" id="i85">1</td><td class="ctr1" id="j73">1</td><td class="ctr2" id="k76">1</td></tr><tr><td id="a58"><a href="RustyClusterClient.java.html#L764" class="el_method">lambda$evalSha$21(Rustycluster.EvalShaRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b77"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c99">0%</td><td class="bar" id="d78"/><td class="ctr2" id="e78">n/a</td><td class="ctr1" id="f77">1</td><td class="ctr2" id="g78">1</td><td class="ctr1" id="h75">1</td><td class="ctr2" id="i86">1</td><td class="ctr1" id="j74">1</td><td class="ctr2" id="k77">1</td></tr><tr><td id="a84"><a href="RustyClusterClient.java.html#L732" class="el_method">lambda$loadScript$20(Rustycluster.LoadScriptRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b78"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c100">0%</td><td class="bar" id="d79"/><td class="ctr2" id="e79">n/a</td><td class="ctr1" id="f78">1</td><td class="ctr2" id="g79">1</td><td class="ctr1" id="h76">1</td><td class="ctr2" id="i87">1</td><td class="ctr1" id="j75">1</td><td class="ctr2" id="k78">1</td></tr><tr><td id="a92"><a href="RustyClusterClient.java.html#L694" class="el_method">lambda$setNX$19(Rustycluster.SetNXRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b79"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c101">0%</td><td class="bar" id="d80"/><td class="ctr2" id="e80">n/a</td><td class="ctr1" id="f79">1</td><td class="ctr2" id="g80">1</td><td class="ctr1" id="h77">1</td><td class="ctr2" id="i88">1</td><td class="ctr1" id="j76">1</td><td class="ctr2" id="k79">1</td></tr><tr><td id="a67"><a href="RustyClusterClient.java.html#L668" class="el_method">lambda$hExists$18(Rustycluster.HExistsRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b80"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c102">0%</td><td class="bar" id="d81"/><td class="ctr2" id="e81">n/a</td><td class="ctr1" id="f80">1</td><td class="ctr2" id="g81">1</td><td class="ctr1" id="h78">1</td><td class="ctr2" id="i89">1</td><td class="ctr1" id="j77">1</td><td class="ctr2" id="k80">1</td></tr><tr><td id="a59"><a href="RustyClusterClient.java.html#L646" class="el_method">lambda$exists$17(Rustycluster.ExistsRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b81"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c103">0%</td><td class="bar" id="d82"/><td class="ctr2" id="e82">n/a</td><td class="ctr1" id="f81">1</td><td class="ctr2" id="g82">1</td><td class="ctr1" id="h79">1</td><td class="ctr2" id="i90">1</td><td class="ctr1" id="j78">1</td><td class="ctr2" id="k81">1</td></tr><tr><td id="a76"><a href="RustyClusterClient.java.html#L611" class="el_method">lambda$hMSet$16(Rustycluster.HMSetRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b82"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c104">0%</td><td class="bar" id="d83"/><td class="ctr2" id="e83">n/a</td><td class="ctr1" id="f82">1</td><td class="ctr2" id="g83">1</td><td class="ctr1" id="h80">1</td><td class="ctr2" id="i91">1</td><td class="ctr1" id="j79">1</td><td class="ctr2" id="k82">1</td></tr><tr><td id="a75"><a href="RustyClusterClient.java.html#L500" class="el_method">lambda$hIncrByFloat$13(Rustycluster.HIncrByFloatRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b83"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c105">0%</td><td class="bar" id="d84"/><td class="ctr2" id="e84">n/a</td><td class="ctr1" id="f83">1</td><td class="ctr2" id="g84">1</td><td class="ctr1" id="h81">1</td><td class="ctr2" id="i92">1</td><td class="ctr1" id="j80">1</td><td class="ctr2" id="k83">1</td></tr><tr><td id="a65"><a href="RustyClusterClient.java.html#L464" class="el_method">lambda$hDecrBy$12(Rustycluster.HDecrByRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b84"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c106">0%</td><td class="bar" id="d85"/><td class="ctr2" id="e85">n/a</td><td class="ctr1" id="f84">1</td><td class="ctr2" id="g85">1</td><td class="ctr1" id="h82">1</td><td class="ctr2" id="i93">1</td><td class="ctr1" id="j81">1</td><td class="ctr2" id="k84">1</td></tr><tr><td id="a74"><a href="RustyClusterClient.java.html#L427" class="el_method">lambda$hIncrBy$11(Rustycluster.HIncrByRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b85"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c107">0%</td><td class="bar" id="d86"/><td class="ctr2" id="e86">n/a</td><td class="ctr1" id="f85">1</td><td class="ctr2" id="g86">1</td><td class="ctr1" id="h83">1</td><td class="ctr2" id="i94">1</td><td class="ctr1" id="j82">1</td><td class="ctr2" id="k85">1</td></tr><tr><td id="a69"><a href="RustyClusterClient.java.html#L384" class="el_method">lambda$hGet$9(Rustycluster.HGetRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b86"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c108">0%</td><td class="bar" id="d87"/><td class="ctr2" id="e87">n/a</td><td class="ctr1" id="f86">1</td><td class="ctr2" id="g87">1</td><td class="ctr1" id="h84">1</td><td class="ctr2" id="i95">1</td><td class="ctr1" id="j83">1</td><td class="ctr2" id="k86">1</td></tr><tr><td id="a79"><a href="RustyClusterClient.java.html#L352" class="el_method">lambda$hSet$8(Rustycluster.HSetRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b87"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c109">0%</td><td class="bar" id="d88"/><td class="ctr2" id="e88">n/a</td><td class="ctr1" id="f87">1</td><td class="ctr2" id="g88">1</td><td class="ctr1" id="h85">1</td><td class="ctr2" id="i96">1</td><td class="ctr1" id="j84">1</td><td class="ctr2" id="k87">1</td></tr><tr><td id="a83"><a href="RustyClusterClient.java.html#L316" class="el_method">lambda$incrByFloat$7(Rustycluster.IncrByFloatRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b88"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c110">0%</td><td class="bar" id="d89"/><td class="ctr2" id="e89">n/a</td><td class="ctr1" id="f88">1</td><td class="ctr2" id="g89">1</td><td class="ctr1" id="h86">1</td><td class="ctr2" id="i97">1</td><td class="ctr1" id="j85">1</td><td class="ctr2" id="k88">1</td></tr><tr><td id="a55"><a href="RustyClusterClient.java.html#L283" class="el_method">lambda$decrBy$6(Rustycluster.DecrByRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b89"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c111">0%</td><td class="bar" id="d90"/><td class="ctr2" id="e90">n/a</td><td class="ctr1" id="f89">1</td><td class="ctr2" id="g90">1</td><td class="ctr1" id="h87">1</td><td class="ctr2" id="i98">1</td><td class="ctr1" id="j86">1</td><td class="ctr2" id="k89">1</td></tr><tr><td id="a91"><a href="RustyClusterClient.java.html#L217" class="el_method">lambda$setExpiry$4(Rustycluster.SetExpiryRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b90"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c112">0%</td><td class="bar" id="d91"/><td class="ctr2" id="e91">n/a</td><td class="ctr1" id="f90">1</td><td class="ctr2" id="g91">1</td><td class="ctr1" id="h88">1</td><td class="ctr2" id="i99">1</td><td class="ctr1" id="j87">1</td><td class="ctr2" id="k90">1</td></tr><tr><td id="a101"><a href="RustyClusterClient.java.html#L84" class="el_method">set(String, String, boolean)</a></td><td class="bar" id="b91"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c21">89%</td><td class="bar" id="d92"/><td class="ctr2" id="e92">n/a</td><td class="ctr1" id="f91">0</td><td class="ctr2" id="g92">1</td><td class="ctr1" id="h36">2</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j91">0</td><td class="ctr2" id="k91">1</td></tr><tr><td id="a105"><a href="RustyClusterClient.java.html#L173" class="el_method">setEx(String, String, long, boolean)</a></td><td class="bar" id="b92"><img src="../jacoco-resources/greenbar.gif" width="62" height="10" title="30" alt="30"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d93"/><td class="ctr2" id="e93">n/a</td><td class="ctr1" id="f92">0</td><td class="ctr2" id="g93">1</td><td class="ctr1" id="h92">0</td><td class="ctr2" id="i7">10</td><td class="ctr1" id="j92">0</td><td class="ctr2" id="k92">1</td></tr><tr><td id="a99"><a href="RustyClusterClient.java.html#L67" class="el_method">RustyClusterClient(RustyClusterClientConfig, ConnectionManager)</a></td><td class="bar" id="b93"><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d94"/><td class="ctr2" id="e94">n/a</td><td class="ctr1" id="f93">0</td><td class="ctr2" id="g94">1</td><td class="ctr1" id="h93">0</td><td class="ctr2" id="i26">7</td><td class="ctr1" id="j93">0</td><td class="ctr2" id="k93">1</td></tr><tr><td id="a19"><a href="RustyClusterClient.java.html#L121" class="el_method">get(String)</a></td><td class="bar" id="b94"><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d12"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f94">0</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h94">0</td><td class="ctr2" id="i33">6</td><td class="ctr1" id="j94">0</td><td class="ctr2" id="k94">1</td></tr><tr><td id="a45"><a href="RustyClusterClient.java.html#L242" class="el_method">incrBy(String, long, boolean)</a></td><td class="bar" id="b95"><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d95"/><td class="ctr2" id="e95">n/a</td><td class="ctr1" id="f95">0</td><td class="ctr2" id="g95">1</td><td class="ctr1" id="h95">0</td><td class="ctr2" id="i17">8</td><td class="ctr1" id="j95">0</td><td class="ctr2" id="k95">1</td></tr><tr><td id="a10"><a href="RustyClusterClient.java.html#L140" class="el_method">delete(String, boolean)</a></td><td class="bar" id="b96"><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="24" alt="24"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d96"/><td class="ctr2" id="e96">n/a</td><td class="ctr1" id="f96">0</td><td class="ctr2" id="g96">1</td><td class="ctr1" id="h96">0</td><td class="ctr2" id="i18">8</td><td class="ctr1" id="j96">0</td><td class="ctr2" id="k96">1</td></tr><tr><td id="a3"><a href="RustyClusterClient.java.html#L525" class="el_method">batchWrite(List, boolean)</a></td><td class="bar" id="b97"><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="24" alt="24"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d97"/><td class="ctr2" id="e97">n/a</td><td class="ctr1" id="f97">0</td><td class="ctr2" id="g97">1</td><td class="ctr1" id="h97">0</td><td class="ctr2" id="i27">7</td><td class="ctr1" id="j97">0</td><td class="ctr2" id="k97">1</td></tr><tr><td id="a29"><a href="RustyClusterClient.java.html#L396" class="el_method">hGetAll(String)</a></td><td class="bar" id="b98"><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="20" alt="20"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d98"/><td class="ctr2" id="e98">n/a</td><td class="ctr1" id="f98">0</td><td class="ctr2" id="g98">1</td><td class="ctr1" id="h98">0</td><td class="ctr2" id="i34">6</td><td class="ctr1" id="j98">0</td><td class="ctr2" id="k98">1</td></tr><tr><td id="a6"><a href="RustyClusterClient.java.html#L1240" class="el_method">close()</a></td><td class="bar" id="b99"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="13" alt="13"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d99"/><td class="ctr2" id="e99">n/a</td><td class="ctr1" id="f99">0</td><td class="ctr2" id="g99">1</td><td class="ctr1" id="h99">0</td><td class="ctr2" id="i38">5</td><td class="ctr1" id="j99">0</td><td class="ctr2" id="k99">1</td></tr><tr><td id="a104"><a href="RustyClusterClient.java.html#L197" class="el_method">setEx(String, String, long)</a></td><td class="bar" id="b100"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d100"/><td class="ctr2" id="e100">n/a</td><td class="ctr1" id="f100">0</td><td class="ctr2" id="g100">1</td><td class="ctr1" id="h100">0</td><td class="ctr2" id="i100">1</td><td class="ctr1" id="j100">0</td><td class="ctr2" id="k100">1</td></tr><tr><td id="a100"><a href="RustyClusterClient.java.html#L111" class="el_method">set(String, String)</a></td><td class="bar" id="b101"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d101"/><td class="ctr2" id="e101">n/a</td><td class="ctr1" id="f101">0</td><td class="ctr2" id="g101">1</td><td class="ctr1" id="h101">0</td><td class="ctr2" id="i101">1</td><td class="ctr1" id="j101">0</td><td class="ctr2" id="k101">1</td></tr><tr><td id="a44"><a href="RustyClusterClient.java.html#L263" class="el_method">incrBy(String, long)</a></td><td class="bar" id="b102"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d102"/><td class="ctr2" id="e102">n/a</td><td class="ctr1" id="f102">0</td><td class="ctr2" id="g102">1</td><td class="ctr1" id="h102">0</td><td class="ctr2" id="i102">1</td><td class="ctr1" id="j102">0</td><td class="ctr2" id="k102">1</td></tr><tr><td id="a9"><a href="RustyClusterClient.java.html#L160" class="el_method">delete(String)</a></td><td class="bar" id="b103"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d103"/><td class="ctr2" id="e103">n/a</td><td class="ctr1" id="f103">0</td><td class="ctr2" id="g103">1</td><td class="ctr1" id="h103">0</td><td class="ctr2" id="i103">1</td><td class="ctr1" id="j103">0</td><td class="ctr2" id="k103">1</td></tr><tr><td id="a2"><a href="RustyClusterClient.java.html#L544" class="el_method">batchWrite(List)</a></td><td class="bar" id="b104"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d104"/><td class="ctr2" id="e104">n/a</td><td class="ctr1" id="f104">0</td><td class="ctr2" id="g104">1</td><td class="ctr1" id="h104">0</td><td class="ctr2" id="i104">1</td><td class="ctr1" id="j104">0</td><td class="ctr2" id="k104">1</td></tr><tr><td id="a53"><a href="RustyClusterClient.java.html#L532" class="el_method">lambda$batchWrite$14(Rustycluster.BatchWriteRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b105"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d105"/><td class="ctr2" id="e105">n/a</td><td class="ctr1" id="f105">0</td><td class="ctr2" id="g105">1</td><td class="ctr1" id="h105">0</td><td class="ctr2" id="i105">1</td><td class="ctr1" id="j105">0</td><td class="ctr2" id="k105">1</td></tr><tr><td id="a70"><a href="RustyClusterClient.java.html#L402" class="el_method">lambda$hGetAll$10(Rustycluster.HGetAllRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b106"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d106"/><td class="ctr2" id="e106">n/a</td><td class="ctr1" id="f106">0</td><td class="ctr2" id="g106">1</td><td class="ctr1" id="h106">0</td><td class="ctr2" id="i106">1</td><td class="ctr1" id="j106">0</td><td class="ctr2" id="k106">1</td></tr><tr><td id="a81"><a href="RustyClusterClient.java.html#L250" class="el_method">lambda$incrBy$5(Rustycluster.IncrByRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b107"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d107"/><td class="ctr2" id="e107">n/a</td><td class="ctr1" id="f107">0</td><td class="ctr2" id="g107">1</td><td class="ctr1" id="h107">0</td><td class="ctr2" id="i107">1</td><td class="ctr1" id="j107">0</td><td class="ctr2" id="k107">1</td></tr><tr><td id="a90"><a href="RustyClusterClient.java.html#L183" class="el_method">lambda$setEx$3(Rustycluster.SetExRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b108"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d108"/><td class="ctr2" id="e108">n/a</td><td class="ctr1" id="f108">0</td><td class="ctr2" id="g108">1</td><td class="ctr1" id="h108">0</td><td class="ctr2" id="i108">1</td><td class="ctr1" id="j108">0</td><td class="ctr2" id="k108">1</td></tr><tr><td id="a56"><a href="RustyClusterClient.java.html#L148" class="el_method">lambda$delete$2(Rustycluster.DeleteRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b109"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d109"/><td class="ctr2" id="e109">n/a</td><td class="ctr1" id="f109">0</td><td class="ctr2" id="g109">1</td><td class="ctr1" id="h109">0</td><td class="ctr2" id="i109">1</td><td class="ctr1" id="j109">0</td><td class="ctr2" id="k109">1</td></tr><tr><td id="a62"><a href="RustyClusterClient.java.html#L127" class="el_method">lambda$get$1(Rustycluster.GetRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b110"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d110"/><td class="ctr2" id="e110">n/a</td><td class="ctr1" id="f110">0</td><td class="ctr2" id="g110">1</td><td class="ctr1" id="h110">0</td><td class="ctr2" id="i110">1</td><td class="ctr1" id="j110">0</td><td class="ctr2" id="k110">1</td></tr><tr><td id="a88"><a href="RustyClusterClient.java.html#L95" class="el_method">lambda$set$0(Rustycluster.SetRequest, KeyValueServiceGrpc.KeyValueServiceBlockingStub)</a></td><td class="bar" id="b111"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d111"/><td class="ctr2" id="e111">n/a</td><td class="ctr1" id="f111">0</td><td class="ctr2" id="g111">1</td><td class="ctr1" id="h111">0</td><td class="ctr2" id="i111">1</td><td class="ctr1" id="j111">0</td><td class="ctr2" id="k111">1</td></tr><tr><td id="a112"><a href="RustyClusterClient.java.html#L41" class="el_method">static {...}</a></td><td class="bar" id="b112"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d112"/><td class="ctr2" id="e112">n/a</td><td class="ctr1" id="f112">0</td><td class="ctr2" id="g112">1</td><td class="ctr1" id="h112">0</td><td class="ctr2" id="i112">1</td><td class="ctr1" id="j112">0</td><td class="ctr2" id="k112">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
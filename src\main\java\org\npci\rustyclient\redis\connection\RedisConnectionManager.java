package org.npci.rustyclient.redis.connection;

import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeConfig;
import org.npci.rustyclient.redis.exception.NoAvailableRedisNodesException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages Redis connections, handling prioritization and failover.
 */
public class RedisConnectionManager implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(RedisConnectionManager.class);

    private final RedisClientConfig config;
    private final RedisConnectionPool connectionPool;
    private final AtomicReference<RedisNodeConfig> currentNode;
    private final List<RedisNodeConfig> sortedNodes;

    /**
     * Create a new RedisConnectionManager.
     *
     * @param config The Redis client configuration
     */
    public RedisConnectionManager(RedisClientConfig config) {
        this.config = config;
        this.connectionPool = new RedisConnectionPool(config);

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
        this.sortedNodes = config.getNodes().stream()
                .sorted(Comparator.comparingInt(node -> node.role().getPriority()))
                .toList();

        // Set the initial node to the highest priority node
        this.currentNode = new AtomicReference<>(sortedNodes.get(0));

        logger.info("RedisConnectionManager initialized with {} nodes", sortedNodes.size());
    }

    /**
     * Execute an operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param <T>       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableRedisNodesException If no nodes are available
     */
    public <T> T executeWithFailover(RedisOperation<T> operation) throws NoAvailableRedisNodesException {
        return executeWithFailover(operation, RedisOperationType.READ);
    }

    /**
     * Execute an operation with automatic failover and operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ/WRITE)
     * @param <T>           The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableRedisNodesException If no nodes are available
     */
    public <T> T executeWithFailover(RedisOperation<T> operation, RedisOperationType operationType) 
            throws NoAvailableRedisNodesException {
        
        RedisNodeConfig node = currentNode.get();
        
        for (int attempt = 0; attempt <= config.getMaxRetries(); attempt++) {
            Jedis connection = null;
            
            try {
                connection = connectionPool.borrowConnection(node);
                T result = operation.execute(connection);
                
                logger.debug("Operation executed successfully on node: {} (attempt {})", node, attempt + 1);
                return result;
                
            } catch (Exception e) {
                logger.warn("Operation failed on node: {} (attempt {}): {}", node, attempt + 1, e.getMessage());
                
                // Invalidate the connection
                if (connection != null) {
                    connectionPool.invalidateConnection(node, connection);
                    connection = null;
                }
                
                if (attempt < config.getMaxRetries()) {
                    try {
                        Thread.sleep(config.getRetryDelayMs());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry delay", ie);
                    }
                } else {
                    // Try to find the next available node
                    var nextNode = findNextAvailableNode(node);
                    if (nextNode != null) {
                        currentNode.set(nextNode);
                        logger.info("Switched to node: {}", nextNode);
                    } else {
                        logger.warn("No available nodes found after failure");
                    }
                }
            } finally {
                // Return connection to pool if it's still valid
                if (connection != null) {
                    connectionPool.returnConnection(node, connection);
                }
            }
        }
        
        throw new NoAvailableRedisNodesException("All Redis nodes are unavailable after retries");
    }

    /**
     * Execute an operation with failover but return null on failure (for read operations).
     *
     * @param operation The operation to execute
     * @param <T>       The return type of the operation
     * @return The result of the operation, or null if all nodes are unavailable
     */
    public <T> T executeWithFailoverSilent(RedisOperation<T> operation) {
        try {
            return executeWithFailover(operation, RedisOperationType.READ);
        } catch (NoAvailableRedisNodesException e) {
            logger.warn("All Redis nodes unavailable for read operation, returning null");
            return null;
        }
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none found
     */
    private RedisNodeConfig findNextAvailableNode(RedisNodeConfig failedNode) {
        logger.debug("Looking for next available node after failure of: {}", failedNode);
        
        for (RedisNodeConfig node : sortedNodes) {
            if (!node.equals(failedNode) && isNodeHealthy(node)) {
                logger.debug("Found available node: {}", node);
                return node;
            }
        }
        
        logger.warn("No available nodes found");
        return null;
    }

    /**
     * Check if a node is healthy.
     *
     * @param node The node to check
     * @return True if the node is healthy
     */
    private boolean isNodeHealthy(RedisNodeConfig node) {
        Jedis connection = null;
        try {
            connection = connectionPool.borrowConnection(node);
            String response = connection.ping();
            boolean healthy = "PONG".equals(response);
            logger.debug("Health check for node {}: {}", node, healthy ? "HEALTHY" : "UNHEALTHY");
            return healthy;
        } catch (Exception e) {
            logger.debug("Health check failed for node {}: {}", node, e.getMessage());
            return false;
        } finally {
            if (connection != null) {
                connectionPool.returnConnection(node, connection);
            }
        }
    }

    /**
     * Get the current active node.
     *
     * @return The current active node
     */
    public RedisNodeConfig getCurrentNode() {
        return currentNode.get();
    }

    /**
     * Get all configured nodes.
     *
     * @return List of all nodes
     */
    public List<RedisNodeConfig> getAllNodes() {
        return sortedNodes;
    }

    /**
     * Get connection pool statistics.
     *
     * @return Pool statistics for all nodes
     */
    public String getPoolStats() {
        StringBuilder stats = new StringBuilder("Redis Connection Pool Statistics:\n");
        for (RedisNodeConfig node : sortedNodes) {
            stats.append(connectionPool.getPoolStats(node)).append("\n");
        }
        return stats.toString();
    }

    /**
     * Perform a health check on all nodes.
     *
     * @return True if at least one node is healthy
     */
    public boolean healthCheck() {
        logger.debug("Performing health check on all Redis nodes");
        
        for (RedisNodeConfig node : sortedNodes) {
            if (isNodeHealthy(node)) {
                logger.debug("Health check passed - at least one node is healthy");
                return true;
            }
        }
        
        logger.warn("Health check failed - no healthy nodes found");
        return false;
    }

    @Override
    public void close() {
        logger.info("Closing RedisConnectionManager");
        connectionPool.close();
        logger.info("RedisConnectionManager closed");
    }

    /**
     * Functional interface for Redis operations.
     *
     * @param <T> The return type of the operation
     */
    @FunctionalInterface
    public interface RedisOperation<T> {
        T execute(Jedis connection) throws Exception;
    }

    /**
     * Enum for Redis operation types.
     */
    public enum RedisOperationType {
        READ, WRITE
    }
}

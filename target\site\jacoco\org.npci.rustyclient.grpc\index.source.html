<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.grpc</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.grpc</span></div><h1>org.npci.rustyclient.grpc</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">30,134 of 30,134</td><td class="ctr2">0%</td><td class="bar">2,533 of 2,533</td><td class="ctr2">0%</td><td class="ctr1">3,832</td><td class="ctr2">3,832</td><td class="ctr1">8,551</td><td class="ctr2">8,551</td><td class="ctr1">2,514</td><td class="ctr2">2,514</td><td class="ctr1">121</td><td class="ctr2">121</td></tr></tfoot><tbody><tr><td id="a1"><a href="RustyClusterProto.java.html" class="el_source">RustyClusterProto.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="29,356" alt="29,356"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2,515" alt="2,515"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">3,724</td><td class="ctr2" id="g0">3,724</td><td class="ctr1" id="h0">8,330</td><td class="ctr2" id="i0">8,330</td><td class="ctr1" id="j0">2,423</td><td class="ctr2" id="k0">2,423</td><td class="ctr1" id="l0">109</td><td class="ctr2" id="m0">109</td></tr><tr><td id="a0"><a href="KeyValueServiceGrpc.java.html" class="el_source">KeyValueServiceGrpc.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="778" alt="778"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">108</td><td class="ctr2" id="g1">108</td><td class="ctr1" id="h1">221</td><td class="ctr2" id="i1">221</td><td class="ctr1" id="j1">91</td><td class="ctr2" id="k1">91</td><td class="ctr1" id="l1">12</td><td class="ctr2" id="m1">12</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
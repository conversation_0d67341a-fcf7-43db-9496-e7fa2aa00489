package org.npci.rustyclient.redis.exception;

/**
 * Exception thrown when no Redis nodes are available for operations.
 */
public class NoAvailableRedisNodesException extends RuntimeException {
    
    /**
     * Create a new NoAvailableRedisNodesException with a message.
     *
     * @param message The exception message
     */
    public NoAvailableRedisNodesException(String message) {
        super(message);
    }

    /**
     * Create a new NoAvailableRedisNodesException with a message and cause.
     *
     * @param message The exception message
     * @param cause   The underlying cause
     */
    public NoAvailableRedisNodesException(String message, Throwable cause) {
        super(message, cause);
    }
}

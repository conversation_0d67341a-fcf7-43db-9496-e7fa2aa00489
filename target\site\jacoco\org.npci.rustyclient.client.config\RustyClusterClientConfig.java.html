<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClientConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.config</a> &gt; <span class="el_source">RustyClusterClientConfig.java</span></div><h1>RustyClusterClientConfig.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Configuration for the RustyCluster client.
 */
<span class="fc" id="L11">public class RustyClusterClientConfig {</span>
<span class="fc" id="L12">    private List&lt;NodeConfig&gt; nodes = new ArrayList&lt;&gt;();</span>
    // High-throughput optimized defaults
<span class="fc" id="L14">    private int maxConnectionsPerNode = 20; // Increased from 10 for better concurrency</span>
<span class="fc" id="L15">    private long connectionTimeoutMs = 3000; // Reduced from 5000 for faster failover</span>
<span class="fc" id="L16">    private long readTimeoutMs = 2000; // Reduced from 5000 for faster response detection</span>
<span class="fc" id="L17">    private long writeTimeoutMs = 2000; // Reduced from 5000 for faster write detection</span>
<span class="fc" id="L18">    private int maxRetries = 2; // Reduced from 3 for faster failure detection</span>
<span class="fc" id="L19">    private long retryDelayMs = 100; // Reduced from 500 for faster retry cycles</span>
<span class="fc" id="L20">    private boolean useSecureConnection = false;</span>
<span class="fc" id="L21">    private String tlsCertPath = null;</span>
<span class="fc" id="L22">    private String username = null;</span>
<span class="fc" id="L23">    private String password = null;</span>

    // Failback configuration
<span class="fc" id="L26">    private boolean enableFailback = true; // Enable automatic failback to higher-priority nodes</span>
<span class="fc" id="L27">    private long failbackCheckIntervalMs = 30000; // Check every 30 seconds by default</span>
<span class="fc" id="L28">    private int failbackHealthCheckRetries = 2; // Number of health check retries before considering a node healthy</span>

    /**
     * Builder for RustyClusterClientConfig.
     */
<span class="fc" id="L33">    public static class Builder {</span>
<span class="fc" id="L34">        private final RustyClusterClientConfig config = new RustyClusterClientConfig();</span>

        /**
         * Add a node to the configuration with a specific role.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @param role The role of the node
         * @return The builder instance
         */
        public Builder addNode(String host, int port, NodeRole role) {
<span class="fc" id="L45">            config.nodes.add(new NodeConfig(host, port, role));</span>
<span class="fc" id="L46">            return this;</span>
        }

        /**
         * Add a primary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addPrimaryNode(String host, int port) {
<span class="fc" id="L57">            return addNode(host, port, NodeRole.PRIMARY);</span>
        }

        /**
         * Add a secondary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addSecondaryNode(String host, int port) {
<span class="fc" id="L68">            return addNode(host, port, NodeRole.SECONDARY);</span>
        }

        /**
         * Add a tertiary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addTertiaryNode(String host, int port) {
<span class="fc" id="L79">            return addNode(host, port, NodeRole.TERTIARY);</span>
        }

        /**
         * Add multiple nodes with the same role.
         *
         * @param role The role for all nodes
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50051&quot;)
         * @return The builder instance
         */
        public Builder addNodes(NodeRole role, String... hostPorts) {
<span class="fc bfc" id="L90" title="All 2 branches covered.">            for (String hostPort : hostPorts) {</span>
<span class="fc" id="L91">                String[] parts = hostPort.split(&quot;:&quot;);</span>
<span class="fc bfc" id="L92" title="All 2 branches covered.">                if (parts.length != 2) {</span>
<span class="fc" id="L93">                    throw new IllegalArgumentException(&quot;Invalid host:port format: &quot; + hostPort);</span>
                }
<span class="fc" id="L95">                String host = parts[0];</span>
                int port;
                try {
<span class="fc" id="L98">                    port = Integer.parseInt(parts[1]);</span>
<span class="fc" id="L99">                } catch (NumberFormatException e) {</span>
<span class="fc" id="L100">                    throw new IllegalArgumentException(&quot;Invalid port number in: &quot; + hostPort, e);</span>
<span class="fc" id="L101">                }</span>
<span class="fc" id="L102">                addNode(host, port, role);</span>
            }
<span class="fc" id="L104">            return this;</span>
        }

        /**
         * Add multiple primary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50051&quot;)
         * @return The builder instance
         */
        public Builder addPrimaryNodes(String... hostPorts) {
<span class="fc" id="L114">            return addNodes(NodeRole.PRIMARY, hostPorts);</span>
        }

        /**
         * Add multiple secondary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50052&quot;)
         * @return The builder instance
         */
        public Builder addSecondaryNodes(String... hostPorts) {
<span class="fc" id="L124">            return addNodes(NodeRole.SECONDARY, hostPorts);</span>
        }

        /**
         * Add multiple tertiary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50053&quot;)
         * @return The builder instance
         */
        public Builder addTertiaryNodes(String... hostPorts) {
<span class="fc" id="L134">            return addNodes(NodeRole.TERTIARY, hostPorts);</span>
        }

        /**
         * Add nodes with automatic role assignment based on order:
         * - First node: PRIMARY
         * - Second node: SECONDARY
         * - Third and subsequent nodes: TERTIARY
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50051&quot;)
         * @return The builder instance
         */
        public Builder addNodes(String... hostPorts) {
<span class="pc bpc" id="L147" title="2 of 4 branches missed.">            if (hostPorts == null || hostPorts.length == 0) {</span>
<span class="nc" id="L148">                return this;</span>
            }

            // Count existing nodes to determine roles for new nodes
<span class="fc" id="L152">            int existingNodeCount = config.nodes.size();</span>

<span class="fc bfc" id="L154" title="All 2 branches covered.">            for (int i = 0; i &lt; hostPorts.length; i++) {</span>
<span class="fc" id="L155">                String hostPort = hostPorts[i];</span>
<span class="fc" id="L156">                String[] parts = hostPort.split(&quot;:&quot;);</span>
<span class="pc bpc" id="L157" title="1 of 2 branches missed.">                if (parts.length != 2) {</span>
<span class="nc" id="L158">                    throw new IllegalArgumentException(&quot;Invalid host:port format: &quot; + hostPort);</span>
                }

<span class="fc" id="L161">                String host = parts[0];</span>
                int port;
                try {
<span class="fc" id="L164">                    port = Integer.parseInt(parts[1]);</span>
<span class="nc" id="L165">                } catch (NumberFormatException e) {</span>
<span class="nc" id="L166">                    throw new IllegalArgumentException(&quot;Invalid port number in: &quot; + hostPort, e);</span>
<span class="fc" id="L167">                }</span>

                // Determine role based on position
                NodeRole role;
<span class="fc" id="L171">                int position = existingNodeCount + i;</span>

<span class="fc bfc" id="L173" title="All 2 branches covered.">                if (position == 0) {</span>
<span class="fc" id="L174">                    role = NodeRole.PRIMARY;</span>
<span class="fc bfc" id="L175" title="All 2 branches covered.">                } else if (position == 1) {</span>
<span class="fc" id="L176">                    role = NodeRole.SECONDARY;</span>
                } else {
<span class="fc" id="L178">                    role = NodeRole.TERTIARY;</span>
                }

<span class="fc" id="L181">                addNode(host, port, role);</span>
            }

<span class="fc" id="L184">            return this;</span>
        }

        /**
         * Set the maximum number of connections per node.
         *
         * @param maxConnections The maximum number of connections
         * @return The builder instance
         */
        public Builder maxConnectionsPerNode(int maxConnections) {
<span class="fc" id="L194">            config.maxConnectionsPerNode = maxConnections;</span>
<span class="fc" id="L195">            return this;</span>
        }

        /**
         * Set the connection timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder connectionTimeout(long timeout, TimeUnit unit) {
<span class="fc" id="L206">            config.connectionTimeoutMs = unit.toMillis(timeout);</span>
<span class="fc" id="L207">            return this;</span>
        }

        /**
         * Set the read timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder readTimeout(long timeout, TimeUnit unit) {
<span class="fc" id="L218">            config.readTimeoutMs = unit.toMillis(timeout);</span>
<span class="fc" id="L219">            return this;</span>
        }

        /**
         * Set the write timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder writeTimeout(long timeout, TimeUnit unit) {
<span class="fc" id="L230">            config.writeTimeoutMs = unit.toMillis(timeout);</span>
<span class="fc" id="L231">            return this;</span>
        }

        /**
         * Set the maximum number of retries.
         *
         * @param maxRetries The maximum number of retries
         * @return The builder instance
         */
        public Builder maxRetries(int maxRetries) {
<span class="fc" id="L241">            config.maxRetries = maxRetries;</span>
<span class="fc" id="L242">            return this;</span>
        }

        /**
         * Set the retry delay.
         *
         * @param delay The delay value
         * @param unit  The time unit
         * @return The builder instance
         */
        public Builder retryDelay(long delay, TimeUnit unit) {
<span class="fc" id="L253">            config.retryDelayMs = unit.toMillis(delay);</span>
<span class="fc" id="L254">            return this;</span>
        }

        /**
         * Enable secure connection using TLS.
         *
         * @param tlsCertPath Path to the TLS certificate
         * @return The builder instance
         */
        public Builder useSecureConnection(String tlsCertPath) {
<span class="fc" id="L264">            config.useSecureConnection = true;</span>
<span class="fc" id="L265">            config.tlsCertPath = tlsCertPath;</span>
<span class="fc" id="L266">            return this;</span>
        }

        /**
         * Set authentication credentials.
         *
         * @param username The username for authentication
         * @param password The password for authentication
         * @return The builder instance
         */
        public Builder authentication(String username, String password) {
<span class="fc" id="L277">            config.username = username;</span>
<span class="fc" id="L278">            config.password = password;</span>
<span class="fc" id="L279">            return this;</span>
        }

        /**
         * Apply high-throughput optimized settings.
         * This preset is optimized for maximum performance in high-load scenarios.
         *
         * @return The builder instance
         */
        public Builder highThroughputPreset() {
<span class="fc" id="L289">            config.maxConnectionsPerNode = 50;</span>
<span class="fc" id="L290">            config.connectionTimeoutMs = 1000;</span>
<span class="fc" id="L291">            config.readTimeoutMs = 1000;</span>
<span class="fc" id="L292">            config.writeTimeoutMs = 1000;</span>
<span class="fc" id="L293">            config.maxRetries = 1;</span>
<span class="fc" id="L294">            config.retryDelayMs = 50;</span>
<span class="fc" id="L295">            return this;</span>
        }

        /**
         * Apply low-latency optimized settings.
         * This preset is optimized for minimal latency at the cost of some throughput.
         *
         * @return The builder instance
         */
        public Builder lowLatencyPreset() {
<span class="fc" id="L305">            config.maxConnectionsPerNode = 10;</span>
<span class="fc" id="L306">            config.connectionTimeoutMs = 500;</span>
<span class="fc" id="L307">            config.readTimeoutMs = 500;</span>
<span class="fc" id="L308">            config.writeTimeoutMs = 500;</span>
<span class="fc" id="L309">            config.maxRetries = 0;</span>
<span class="fc" id="L310">            config.retryDelayMs = 0;</span>
<span class="fc" id="L311">            return this;</span>
        }

        /**
         * Apply balanced settings for general use.
         * This preset balances throughput, latency, and reliability.
         *
         * @return The builder instance
         */
        public Builder balancedPreset() {
<span class="fc" id="L321">            config.maxConnectionsPerNode = 20;</span>
<span class="fc" id="L322">            config.connectionTimeoutMs = 3000;</span>
<span class="fc" id="L323">            config.readTimeoutMs = 2000;</span>
<span class="fc" id="L324">            config.writeTimeoutMs = 2000;</span>
<span class="fc" id="L325">            config.maxRetries = 2;</span>
<span class="fc" id="L326">            config.retryDelayMs = 100;</span>
<span class="fc" id="L327">            return this;</span>
        }

        /**
         * Enable or disable automatic failback to higher-priority nodes.
         *
         * @param enableFailback Whether to enable failback
         * @return The builder instance
         */
        public Builder enableFailback(boolean enableFailback) {
<span class="fc" id="L337">            config.enableFailback = enableFailback;</span>
<span class="fc" id="L338">            return this;</span>
        }

        /**
         * Set the interval for checking if higher-priority nodes have recovered.
         *
         * @param interval The check interval
         * @param unit     The time unit
         * @return The builder instance
         */
        public Builder failbackCheckInterval(long interval, TimeUnit unit) {
<span class="fc" id="L349">            config.failbackCheckIntervalMs = unit.toMillis(interval);</span>
<span class="fc" id="L350">            return this;</span>
        }

        /**
         * Set the number of health check retries before considering a node healthy for failback.
         *
         * @param retries The number of retries
         * @return The builder instance
         */
        public Builder failbackHealthCheckRetries(int retries) {
<span class="fc" id="L360">            config.failbackHealthCheckRetries = retries;</span>
<span class="fc" id="L361">            return this;</span>
        }

        /**
         * Build the configuration.
         *
         * @return The built configuration
         */
        public RustyClusterClientConfig build() {
<span class="fc bfc" id="L370" title="All 2 branches covered.">            if (config.nodes.isEmpty()) {</span>
<span class="fc" id="L371">                throw new IllegalStateException(&quot;At least one node must be configured&quot;);</span>
            }
<span class="fc" id="L373">            return config;</span>
        }
    }

    /**
     * Create a new builder for RustyClusterClientConfig.
     *
     * @return A new builder instance
     */
    public static Builder builder() {
<span class="fc" id="L383">        return new Builder();</span>
    }

    // Getters
    public List&lt;NodeConfig&gt; getNodes() {
<span class="fc" id="L388">        return Collections.unmodifiableList(nodes);</span>
    }

    public int getMaxConnectionsPerNode() {
<span class="fc" id="L392">        return maxConnectionsPerNode;</span>
    }

    public long getConnectionTimeoutMs() {
<span class="fc" id="L396">        return connectionTimeoutMs;</span>
    }

    public long getReadTimeoutMs() {
<span class="fc" id="L400">        return readTimeoutMs;</span>
    }

    public long getWriteTimeoutMs() {
<span class="fc" id="L404">        return writeTimeoutMs;</span>
    }

    public int getMaxRetries() {
<span class="fc" id="L408">        return maxRetries;</span>
    }

    public long getRetryDelayMs() {
<span class="fc" id="L412">        return retryDelayMs;</span>
    }

    public boolean isUseSecureConnection() {
<span class="fc" id="L416">        return useSecureConnection;</span>
    }

    public String getTlsCertPath() {
<span class="fc" id="L420">        return tlsCertPath;</span>
    }

    public String getUsername() {
<span class="fc" id="L424">        return username;</span>
    }

    public String getPassword() {
<span class="fc" id="L428">        return password;</span>
    }

    public boolean hasAuthentication() {
<span class="pc bpc" id="L432" title="1 of 4 branches missed.">        return username != null &amp;&amp; password != null;</span>
    }

    public boolean isEnableFailback() {
<span class="fc" id="L436">        return enableFailback;</span>
    }

    public long getFailbackCheckIntervalMs() {
<span class="fc" id="L440">        return failbackCheckIntervalMs;</span>
    }

    public int getFailbackHealthCheckRetries() {
<span class="fc" id="L444">        return failbackHealthCheckRetries;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
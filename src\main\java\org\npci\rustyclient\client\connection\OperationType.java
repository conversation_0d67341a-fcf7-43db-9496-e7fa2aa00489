package org.npci.rustyclient.client.connection;

/**
 * Enum representing different types of operations for timeout configuration.
 */
public enum OperationType {
    /**
     * Read operations (get, hGet, hGetAll, etc.)
     * These operations should use readTimeoutMs.
     */
    READ,
    
    /**
     * Write operations (set, setEx, hSet, delete, etc.)
     * These operations should use writeTimeoutMs.
     */
    WRITE,
    
    /**
     * Authentication operations
     * These operations should use connectionTimeoutMs.
     */
    AUTH
}

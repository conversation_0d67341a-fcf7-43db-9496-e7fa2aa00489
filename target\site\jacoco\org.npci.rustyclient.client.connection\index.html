<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.client.connection</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.client.connection</span></div><h1>org.npci.rustyclient.client.connection</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,106 of 2,433</td><td class="ctr2">54%</td><td class="bar">103 of 202</td><td class="ctr2">49%</td><td class="ctr1">120</td><td class="ctr2">211</td><td class="ctr1">278</td><td class="ctr2">611</td><td class="ctr1">51</td><td class="ctr2">108</td><td class="ctr1">1</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a0"><a href="AsyncConnectionManager.html" class="el_class">AsyncConnectionManager</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="432" alt="432"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="75" alt="75"/></td><td class="ctr2" id="c7">14%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="52" alt="52"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">49</td><td class="ctr2" id="g0">53</td><td class="ctr1" id="h0">95</td><td class="ctr2" id="i0">114</td><td class="ctr1" id="j0">22</td><td class="ctr2" id="k0">26</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a6"><a href="ConnectionPool.html" class="el_class">ConnectionPool</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="170" alt="170"/></td><td class="ctr2" id="c9">2%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">9</td><td class="ctr2" id="g5">10</td><td class="ctr1" id="h1">42</td><td class="ctr2" id="i5">43</td><td class="ctr1" id="j1">5</td><td class="ctr2" id="k6">6</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a9"><a href="GrpcChannelFactory.html" class="el_class">GrpcChannelFactory</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="106" alt="106"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="20" alt="20"/></td><td class="ctr2" id="c6">15%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">6</td><td class="ctr2" id="g6">8</td><td class="ctr1" id="h2">36</td><td class="ctr2" id="i6">43</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k4">7</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="AsyncFailbackManager.html" class="el_class">AsyncFailbackManager</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="103" alt="103"/><img src="../jacoco-resources/greenbar.gif" width="79" height="10" title="337" alt="337"/></td><td class="ctr2" id="c3">76%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="17" alt="17"/></td><td class="ctr2" id="e2">60%</td><td class="ctr1" id="f2">14</td><td class="ctr2" id="g2">37</td><td class="ctr1" id="h3">25</td><td class="ctr2" id="i2">110</td><td class="ctr1" id="j6">3</td><td class="ctr2" id="k1">23</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="ConnectionManager.html" class="el_class">ConnectionManager</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="71" alt="71"/><img src="../jacoco-resources/greenbar.gif" width="91" height="10" title="386" alt="386"/></td><td class="ctr2" id="c2">84%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="86" height="10" title="46" alt="46"/></td><td class="ctr2" id="e1">71%</td><td class="ctr1" id="f1">18</td><td class="ctr2" id="g1">48</td><td class="ctr1" id="h5">16</td><td class="ctr2" id="i1">111</td><td class="ctr1" id="j7">2</td><td class="ctr2" id="k2">15</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a7"><a href="ConnectionPool$StubFactory.html" class="el_class">ConnectionPool.StubFactory</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="63" alt="63"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h6">16</td><td class="ctr2" id="i7">16</td><td class="ctr1" id="j3">5</td><td class="ctr2" id="k7">5</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a2"><a href="AsyncConnectionPool$AsyncStubFactory.html" class="el_class">AsyncConnectionPool.AsyncStubFactory</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="54" alt="54"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="9" alt="9"/></td><td class="ctr2" id="c8">14%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h8">13</td><td class="ctr2" id="i8">16</td><td class="ctr1" id="j4">4</td><td class="ctr2" id="k8">5</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a1"><a href="AsyncConnectionPool.html" class="el_class">AsyncConnectionPool</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="52" alt="52"/><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="134" alt="134"/></td><td class="ctr2" id="c4">72%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f6">6</td><td class="ctr2" id="g4">11</td><td class="ctr1" id="h7">14</td><td class="ctr2" id="i4">46</td><td class="ctr1" id="j5">4</td><td class="ctr2" id="k5">7</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a8"><a href="FailbackManager.html" class="el_class">FailbackManager</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="49" alt="49"/><img src="../jacoco-resources/greenbar.gif" width="77" height="10" title="326" alt="326"/></td><td class="ctr2" id="c1">86%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="32" alt="32"/></td><td class="ctr2" id="e0">80%</td><td class="ctr1" id="f4">8</td><td class="ctr2" id="g3">30</td><td class="ctr1" id="h4">19</td><td class="ctr2" id="i3">103</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k3">10</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a4"><a href="AsyncFailbackManager$1.html" class="el_class">AsyncFailbackManager.new FutureCallback() {...}</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="15" alt="15"/></td><td class="ctr2" id="c5">71%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k9">3</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a10"><a href="OperationType.html" class="el_class">OperationType</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="21" alt="21"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td><td class="ctr1" id="l10">0</td><td class="ctr2" id="m10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
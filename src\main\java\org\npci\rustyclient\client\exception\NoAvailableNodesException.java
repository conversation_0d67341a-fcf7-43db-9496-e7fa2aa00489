package org.npci.rustyclient.client.exception;

/**
 * Exception thrown when no available nodes can be found.
 */
public class NoAvailableNodesException extends RuntimeException {
    /**
     * Create a new NoAvailableNodesException with a message.
     *
     * @param message The error message
     */
    public NoAvailableNodesException(String message) {
        super(message);
    }

    /**
     * Create a new NoAvailableNodesException with a message and cause.
     *
     * @param message The error message
     * @param cause   The cause of the exception
     */
    public NoAvailableNodesException(String message, Throwable cause) {
        super(message, cause);
    }
}

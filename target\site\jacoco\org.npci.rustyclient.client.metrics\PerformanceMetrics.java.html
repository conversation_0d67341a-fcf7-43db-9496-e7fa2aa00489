<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PerformanceMetrics.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.metrics</a> &gt; <span class="el_source">PerformanceMetrics.java</span></div><h1>PerformanceMetrics.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.metrics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * Performance metrics collection for RustyCluster client operations.
 * This class provides thread-safe metrics collection for monitoring high-throughput performance.
 */
public class PerformanceMetrics {
<span class="nc" id="L17">    private static final Logger logger = LoggerFactory.getLogger(PerformanceMetrics.class);</span>
    
<span class="nc" id="L19">    private static final PerformanceMetrics INSTANCE = new PerformanceMetrics();</span>
    
    // Operation counters
<span class="nc" id="L22">    private final LongAdder totalOperations = new LongAdder();</span>
<span class="nc" id="L23">    private final LongAdder successfulOperations = new LongAdder();</span>
<span class="nc" id="L24">    private final LongAdder failedOperations = new LongAdder();</span>
<span class="nc" id="L25">    private final LongAdder retryOperations = new LongAdder();</span>
    
    // Latency tracking
<span class="nc" id="L28">    private final AtomicLong totalLatencyNanos = new AtomicLong();</span>
<span class="nc" id="L29">    private final AtomicLong minLatencyNanos = new AtomicLong(Long.MAX_VALUE);</span>
<span class="nc" id="L30">    private final AtomicLong maxLatencyNanos = new AtomicLong();</span>
    
    // Operation type counters
<span class="nc" id="L33">    private final ConcurrentHashMap&lt;String, LongAdder&gt; operationTypeCounters = new ConcurrentHashMap&lt;&gt;();</span>
<span class="nc" id="L34">    private final ConcurrentHashMap&lt;String, AtomicLong&gt; operationTypeLatencies = new ConcurrentHashMap&lt;&gt;();</span>
    
    // Connection pool metrics
<span class="nc" id="L37">    private final LongAdder connectionPoolBorrows = new LongAdder();</span>
<span class="nc" id="L38">    private final LongAdder connectionPoolReturns = new LongAdder();</span>
<span class="nc" id="L39">    private final LongAdder connectionPoolCreations = new LongAdder();</span>
<span class="nc" id="L40">    private final LongAdder connectionPoolDestructions = new LongAdder();</span>
    
    // Failover metrics
<span class="nc" id="L43">    private final LongAdder failoverEvents = new LongAdder();</span>
<span class="nc" id="L44">    private final ConcurrentHashMap&lt;String, LongAdder&gt; nodeFailures = new ConcurrentHashMap&lt;&gt;();</span>
    
    // Timing
<span class="nc" id="L47">    private final Instant startTime = Instant.now();</span>
    
<span class="nc" id="L49">    private PerformanceMetrics() {</span>
        // Private constructor for singleton
<span class="nc" id="L51">    }</span>
    
    public static PerformanceMetrics getInstance() {
<span class="nc" id="L54">        return INSTANCE;</span>
    }
    
    /**
     * Record a successful operation with its latency.
     */
    public void recordOperation(String operationType, Duration latency) {
<span class="nc" id="L61">        totalOperations.increment();</span>
<span class="nc" id="L62">        successfulOperations.increment();</span>
        
<span class="nc" id="L64">        long latencyNanos = latency.toNanos();</span>
<span class="nc" id="L65">        totalLatencyNanos.addAndGet(latencyNanos);</span>
        
        // Update min/max latency
<span class="nc" id="L68">        minLatencyNanos.updateAndGet(current -&gt; Math.min(current, latencyNanos));</span>
<span class="nc" id="L69">        maxLatencyNanos.updateAndGet(current -&gt; Math.max(current, latencyNanos));</span>
        
        // Update operation type metrics
<span class="nc" id="L72">        operationTypeCounters.computeIfAbsent(operationType, k -&gt; new LongAdder()).increment();</span>
<span class="nc" id="L73">        operationTypeLatencies.computeIfAbsent(operationType, k -&gt; new AtomicLong()).addAndGet(latencyNanos);</span>
<span class="nc" id="L74">    }</span>
    
    /**
     * Record a failed operation.
     */
    public void recordFailedOperation(String operationType) {
<span class="nc" id="L80">        totalOperations.increment();</span>
<span class="nc" id="L81">        failedOperations.increment();</span>
<span class="nc" id="L82">        operationTypeCounters.computeIfAbsent(operationType + &quot;_FAILED&quot;, k -&gt; new LongAdder()).increment();</span>
<span class="nc" id="L83">    }</span>
    
    /**
     * Record a retry operation.
     */
    public void recordRetry(String operationType) {
<span class="nc" id="L89">        retryOperations.increment();</span>
<span class="nc" id="L90">        operationTypeCounters.computeIfAbsent(operationType + &quot;_RETRY&quot;, k -&gt; new LongAdder()).increment();</span>
<span class="nc" id="L91">    }</span>
    
    /**
     * Record connection pool activity.
     */
    public void recordConnectionPoolBorrow() {
<span class="nc" id="L97">        connectionPoolBorrows.increment();</span>
<span class="nc" id="L98">    }</span>
    
    public void recordConnectionPoolReturn() {
<span class="nc" id="L101">        connectionPoolReturns.increment();</span>
<span class="nc" id="L102">    }</span>
    
    public void recordConnectionPoolCreation() {
<span class="nc" id="L105">        connectionPoolCreations.increment();</span>
<span class="nc" id="L106">    }</span>
    
    public void recordConnectionPoolDestruction() {
<span class="nc" id="L109">        connectionPoolDestructions.increment();</span>
<span class="nc" id="L110">    }</span>
    
    /**
     * Record a failover event.
     */
    public void recordFailover(String fromNode, String toNode) {
<span class="nc" id="L116">        failoverEvents.increment();</span>
<span class="nc" id="L117">        nodeFailures.computeIfAbsent(fromNode, k -&gt; new LongAdder()).increment();</span>
<span class="nc" id="L118">        logger.info(&quot;Failover recorded: {} -&gt; {}&quot;, fromNode, toNode);</span>
<span class="nc" id="L119">    }</span>
    
    /**
     * Get current performance statistics.
     */
    public PerformanceStats getStats() {
<span class="nc" id="L125">        long total = totalOperations.sum();</span>
<span class="nc" id="L126">        long successful = successfulOperations.sum();</span>
<span class="nc" id="L127">        long failed = failedOperations.sum();</span>
<span class="nc" id="L128">        long retries = retryOperations.sum();</span>
        
<span class="nc bnc" id="L130" title="All 2 branches missed.">        double successRate = total &gt; 0 ? (double) successful / total * 100 : 0;</span>
<span class="nc bnc" id="L131" title="All 2 branches missed.">        double avgLatencyMs = successful &gt; 0 ? </span>
<span class="nc" id="L132">            (double) totalLatencyNanos.get() / successful / 1_000_000 : 0;</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">        double minLatencyMs = minLatencyNanos.get() != Long.MAX_VALUE ? </span>
<span class="nc" id="L134">            (double) minLatencyNanos.get() / 1_000_000 : 0;</span>
<span class="nc" id="L135">        double maxLatencyMs = (double) maxLatencyNanos.get() / 1_000_000;</span>
        
<span class="nc" id="L137">        Duration uptime = Duration.between(startTime, Instant.now());</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">        double throughputOpsPerSec = total &gt; 0 ? </span>
<span class="nc" id="L139">            (double) total / uptime.toSeconds() : 0;</span>
        
<span class="nc" id="L141">        return new PerformanceStats(</span>
            total, successful, failed, retries, successRate,
            avgLatencyMs, minLatencyMs, maxLatencyMs, throughputOpsPerSec,
<span class="nc" id="L144">            connectionPoolBorrows.sum(), connectionPoolReturns.sum(),</span>
<span class="nc" id="L145">            connectionPoolCreations.sum(), connectionPoolDestructions.sum(),</span>
<span class="nc" id="L146">            failoverEvents.sum(), uptime</span>
        );
    }
    
    /**
     * Reset all metrics.
     */
    public void reset() {
<span class="nc" id="L154">        totalOperations.reset();</span>
<span class="nc" id="L155">        successfulOperations.reset();</span>
<span class="nc" id="L156">        failedOperations.reset();</span>
<span class="nc" id="L157">        retryOperations.reset();</span>
<span class="nc" id="L158">        totalLatencyNanos.set(0);</span>
<span class="nc" id="L159">        minLatencyNanos.set(Long.MAX_VALUE);</span>
<span class="nc" id="L160">        maxLatencyNanos.set(0);</span>
<span class="nc" id="L161">        operationTypeCounters.clear();</span>
<span class="nc" id="L162">        operationTypeLatencies.clear();</span>
<span class="nc" id="L163">        connectionPoolBorrows.reset();</span>
<span class="nc" id="L164">        connectionPoolReturns.reset();</span>
<span class="nc" id="L165">        connectionPoolCreations.reset();</span>
<span class="nc" id="L166">        connectionPoolDestructions.reset();</span>
<span class="nc" id="L167">        failoverEvents.reset();</span>
<span class="nc" id="L168">        nodeFailures.clear();</span>
<span class="nc" id="L169">        logger.info(&quot;Performance metrics reset&quot;);</span>
<span class="nc" id="L170">    }</span>
    
    /**
     * Log current performance statistics.
     */
    public void logStats() {
<span class="nc" id="L176">        PerformanceStats stats = getStats();</span>
<span class="nc" id="L177">        logger.info(&quot;Performance Stats: {} total ops, {:.2f}% success rate, {:.2f}ms avg latency, {:.2f} ops/sec throughput&quot;,</span>
<span class="nc" id="L178">            stats.totalOperations(), stats.successRate(), stats.avgLatencyMs(), stats.throughputOpsPerSec());</span>
<span class="nc" id="L179">    }</span>
    
    /**
     * Performance statistics record.
     */
<span class="nc" id="L184">    public record PerformanceStats(</span>
        long totalOperations,
        long successfulOperations,
        long failedOperations,
        long retryOperations,
        double successRate,
        double avgLatencyMs,
        double minLatencyMs,
        double maxLatencyMs,
        double throughputOpsPerSec,
        long connectionPoolBorrows,
        long connectionPoolReturns,
        long connectionPoolCreations,
        long connectionPoolDestructions,
        long failoverEvents,
        Duration uptime
    ) {}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
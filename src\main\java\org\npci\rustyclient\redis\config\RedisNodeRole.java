package org.npci.rustyclient.redis.config;

/**
 * Enum representing the role of a Redis node.
 */
public enum RedisNodeRole {
    /**
     * Primary node - first choice for operations.
     */
    PRIMARY(1),

    /**
     * Secondary node - used when primary is unavailable.
     */
    SECONDARY(2),

    /**
     * Tertiary node - used when primary and secondary are unavailable.
     */
    TERTIARY(3);

    private final int priority;

    RedisNodeRole(int priority) {
        this.priority = priority;
    }

    /**
     * Get the priority of the node role.
     * Lower number means higher priority.
     *
     * @return The priority value
     */
    public int getPriority() {
        return priority;
    }
}

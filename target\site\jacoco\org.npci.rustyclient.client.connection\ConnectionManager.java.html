<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">ConnectionManager.java</span></div><h1>ConnectionManager.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.exception.NoAvailableNodesException;
import rustycluster.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages connections to RustyCluster nodes, handling prioritization and failover.
 */
public class ConnectionManager implements AutoCloseable {
<span class="fc" id="L20">    private static final Logger logger = LoggerFactory.getLogger(ConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final FailbackManager failbackManager;

    /**
     * Create a new ConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public ConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="nc" id="L35">        this(config, new ConnectionPool(config, authenticationManager));</span>
<span class="nc" id="L36">    }</span>

    /**
     * Create a new ConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="fc" id="L44">    ConnectionManager(RustyClusterClientConfig config, ConnectionPool connectionPool) {</span>
<span class="fc" id="L45">        this.config = config;</span>
<span class="fc" id="L46">        this.connectionPool = connectionPool;</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="fc" id="L49">        this.sortedNodes = config.getNodes().stream()</span>
<span class="fc" id="L50">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="fc" id="L51">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="fc" id="L54">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

        // Initialize failback manager
<span class="fc" id="L57">        this.failbackManager = new FailbackManager(config, connectionPool, sortedNodes, currentNode);</span>
<span class="fc" id="L58">        this.failbackManager.start();</span>

<span class="fc" id="L60">        logger.info(&quot;ConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="fc" id="L61">    }</span>

    /**
     * Execute an operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(ClientOperation&lt;T&gt; operation) throws NoAvailableNodesException {
<span class="fc" id="L72">        return executeWithFailover(operation, OperationType.READ);</span>
    }

    /**
     * Execute an operation with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param &lt;T&gt;           The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(ClientOperation&lt;T&gt; operation, OperationType operationType) throws NoAvailableNodesException {
<span class="fc" id="L85">        return executeWithFailover(operation, operationType, false);</span>
    }

    /**
     * Execute a read operation with automatic failover that returns null on failure instead of throwing exceptions.
     * This method is designed for read operations where returning null is acceptable when all nodes are unavailable.
     *
     * @param operation The read operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation, or null if all nodes are unavailable
     */
    public &lt;T&gt; T executeWithFailoverSilent(ClientOperation&lt;T&gt; operation) {
<span class="nc" id="L97">        return executeWithFailover(operation, OperationType.READ, true);</span>
    }

    /**
     * Execute an operation with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param silent        If true, returns null on failure instead of throwing exceptions
     * @param &lt;T&gt;           The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available and silent is false
     */
    private &lt;T&gt; T executeWithFailover(ClientOperation&lt;T&gt; operation, OperationType operationType, boolean silent) {
<span class="fc" id="L111">        int retries = 0;</span>
<span class="fc" id="L112">        Exception lastException = null;</span>

        // Determine timeout based on operation type
<span class="pc bpc" id="L115" title="1 of 4 branches missed.">        long timeoutMs = switch (operationType) {</span>
<span class="fc" id="L116">            case READ -&gt; config.getReadTimeoutMs();</span>
<span class="fc" id="L117">            case WRITE -&gt; config.getWriteTimeoutMs();</span>
<span class="fc" id="L118">            case AUTH -&gt; config.getConnectionTimeoutMs();</span>
        };

<span class="fc bfc" id="L121" title="All 2 branches covered.">        while (retries &lt;= config.getMaxRetries()) {</span>
<span class="fc" id="L122">            NodeConfig node = currentNode.get();</span>
<span class="fc" id="L123">            KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;</span>

            try {
                // Ensure authentication before executing non-auth operations
<span class="fc bfc" id="L127" title="All 4 branches covered.">                if (operationType != OperationType.AUTH &amp;&amp; config.hasAuthentication() &amp;&amp;</span>
<span class="fc bfc" id="L128" title="All 2 branches covered.">                    !connectionPool.getAuthenticationManager().isAuthenticated()) {</span>
<span class="fc" id="L129">                    logger.debug(&quot;Authentication required before operation, attempting to authenticate&quot;);</span>
                    try {
<span class="fc" id="L131">                        boolean authResult = connectionPool.getAuthenticationManager().authenticate(</span>
<span class="fc" id="L132">                            connectionPool.borrowStub(node).withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS));</span>
<span class="pc bpc" id="L133" title="1 of 2 branches missed.">                        if (!authResult) {</span>
<span class="nc" id="L134">                            throw new RuntimeException(&quot;Authentication failed before operation&quot;);</span>
                        }
<span class="nc" id="L136">                    } catch (Exception authException) {</span>
<span class="nc" id="L137">                        logger.warn(&quot;Pre-operation authentication failed on node {}: {}&quot;, node, authException.getMessage());</span>
<span class="nc" id="L138">                        throw authException;</span>
<span class="fc" id="L139">                    }</span>
                }

<span class="fc" id="L142">                stub = connectionPool.borrowStub(node);</span>
                // Apply deadline per operation to avoid expired deadline issues
<span class="fc" id="L144">                KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline =</span>
<span class="fc" id="L145">                    stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
<span class="fc" id="L146">                return operation.execute(stubWithDeadline);</span>
<span class="fc" id="L147">            } catch (Exception e) {</span>
<span class="fc" id="L148">                lastException = e;</span>
<span class="fc" id="L149">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, e.getMessage());</span>

                // Check if this is an authentication error and try to re-authenticate
<span class="pc bpc" id="L152" title="2 of 6 branches missed.">                if (isAuthenticationError(e) &amp;&amp; operationType != OperationType.AUTH &amp;&amp; config.hasAuthentication()) {</span>
<span class="fc" id="L153">                    logger.info(&quot;Authentication error detected, clearing auth state and will retry&quot;);</span>
<span class="fc" id="L154">                    connectionPool.getAuthenticationManager().clearAuthentication();</span>
                }

                // Try to find the next available node
<span class="fc" id="L158">                var nextNode = findNextAvailableNode(node);</span>
<span class="fc bfc" id="L159" title="All 2 branches covered.">                if (nextNode != null) {</span>
<span class="fc" id="L160">                    currentNode.set(nextNode);</span>
<span class="fc" id="L161">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>

                    // Clear authentication state when switching nodes
                    // This will force re-authentication on the next operation
<span class="fc bfc" id="L165" title="All 2 branches covered.">                    if (config.hasAuthentication()) {</span>
<span class="fc" id="L166">                        connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="fc" id="L167">                        logger.debug(&quot;Cleared authentication state for node switch to: {}&quot;, nextNode);</span>
                    }
                } else {
<span class="fc" id="L170">                    logger.warn(&quot;No available nodes found after failure&quot;);</span>
                }

<span class="fc" id="L173">                retries++;</span>

<span class="fc bfc" id="L175" title="All 2 branches covered.">                if (retries &lt;= config.getMaxRetries()) {</span>
                    try {
<span class="fc" id="L177">                        Thread.sleep(config.getRetryDelayMs());</span>
<span class="nc" id="L178">                    } catch (InterruptedException ie) {</span>
<span class="nc" id="L179">                        Thread.currentThread().interrupt();</span>
<span class="nc" id="L180">                        throw new RuntimeException(&quot;Interrupted during retry delay&quot;, ie);</span>
<span class="fc" id="L181">                    }</span>
                }
            } finally {
<span class="fc bfc" id="L184" title="All 2 branches covered.">                if (stub != null) {</span>
<span class="fc" id="L185">                    connectionPool.returnStub(node, stub);</span>
                }
            }
<span class="fc" id="L188">        }</span>

<span class="pc bpc" id="L190" title="1 of 2 branches missed.">        if (silent) {</span>
<span class="nc" id="L191">            logger.warn(&quot;Read operation failed after {} retries, returning null. Last error: {}&quot;,</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">                       retries, lastException != null ? lastException.getMessage() : &quot;Unknown error&quot;);</span>
<span class="nc" id="L193">            return null;</span>
        } else {
<span class="fc" id="L195">            throw new NoAvailableNodesException(&quot;Operation failed after &quot; + retries + &quot; retries&quot;, lastException);</span>
        }
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="fc" id="L207">        var samePriorityNode = sortedNodes.stream()</span>
<span class="pc bpc" id="L208" title="1 of 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="fc" id="L209">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L210">                .findFirst();</span>

<span class="pc bpc" id="L212" title="1 of 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L213">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="fc" id="L217">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="fc bfc" id="L218" title="All 2 branches covered.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="fc" id="L219">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L220">                .findFirst();</span>

<span class="fc bfc" id="L222" title="All 2 branches covered.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="fc" id="L223">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="fc" id="L227">        return sortedNodes.stream()</span>
<span class="fc bfc" id="L228" title="All 2 branches covered.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="fc" id="L229">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L230">                .findFirst()</span>
<span class="fc" id="L231">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
<span class="fc" id="L241">        KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;</span>
        try {
<span class="fc" id="L243">            stub = connectionPool.borrowStub(node);</span>
            // In a real implementation, you might want to perform a health check
<span class="fc" id="L245">            return true;</span>
<span class="fc" id="L246">        } catch (Exception e) {</span>
<span class="fc" id="L247">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="fc" id="L248">            return false;</span>
        } finally {
<span class="fc bfc" id="L250" title="All 2 branches covered.">            if (stub != null) {</span>
<span class="fc" id="L251">                connectionPool.returnStub(node, stub);</span>
            }
        }
    }



    /**
     * Check if an exception indicates an authentication error.
     *
     * @param exception The exception to check
     * @return True if this is an authentication error, false otherwise
     */
    private boolean isAuthenticationError(Exception exception) {
<span class="pc bpc" id="L265" title="1 of 2 branches missed.">        if (exception == null) {</span>
<span class="nc" id="L266">            return false;</span>
        }

<span class="fc" id="L269">        String message = exception.getMessage();</span>
<span class="pc bpc" id="L270" title="1 of 2 branches missed.">        if (message == null) {</span>
<span class="nc" id="L271">            return false;</span>
        }

        // Check for common authentication error patterns
<span class="fc" id="L275">        String lowerMessage = message.toLowerCase();</span>
<span class="fc bfc" id="L276" title="All 2 branches covered.">        return lowerMessage.contains(&quot;unauthenticated&quot;) ||</span>
<span class="pc bpc" id="L277" title="1 of 2 branches missed.">               lowerMessage.contains(&quot;authentication failed&quot;) ||</span>
<span class="pc bpc" id="L278" title="1 of 2 branches missed.">               lowerMessage.contains(&quot;invalid token&quot;) ||</span>
<span class="pc bpc" id="L279" title="1 of 2 branches missed.">               lowerMessage.contains(&quot;unauthorized&quot;) ||</span>
<span class="pc bpc" id="L280" title="2 of 4 branches missed.">               lowerMessage.contains(&quot;permission denied&quot;) ||</span>
               (exception instanceof io.grpc.StatusRuntimeException &amp;&amp;
<span class="pc bnc" id="L282" title="All 2 branches missed.">                ((io.grpc.StatusRuntimeException) exception).getStatus().getCode() == io.grpc.Status.Code.UNAUTHENTICATED);</span>
    }

    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L290">        failbackManager.close();</span>
<span class="fc" id="L291">        connectionPool.close();</span>
<span class="fc" id="L292">        logger.info(&quot;ConnectionManager closed&quot;);</span>
<span class="fc" id="L293">    }</span>

    /**
     * Functional interface for client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface ClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return The result of the operation
         * @throws Exception If the operation fails
         */
        T execute(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
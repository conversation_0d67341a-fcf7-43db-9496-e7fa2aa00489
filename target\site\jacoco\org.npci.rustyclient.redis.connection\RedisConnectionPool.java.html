<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RedisConnectionPool.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.redis.connection</a> &gt; <span class="el_source">RedisConnectionPool.java</span></div><h1>RedisConnectionPool.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.redis.connection;

import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Manages connection pools for Redis nodes using Jedis.
 */
public class RedisConnectionPool implements AutoCloseable {
<span class="fc" id="L18">    private static final Logger logger = LoggerFactory.getLogger(RedisConnectionPool.class);</span>

    private final RedisClientConfig config;
    private final ConcurrentMap&lt;String, GenericObjectPool&lt;Jedis&gt;&gt; connectionPools;

    /**
     * Create a new RedisConnectionPool.
     *
     * @param config The Redis client configuration
     */
<span class="fc" id="L28">    public RedisConnectionPool(RedisClientConfig config) {</span>
<span class="fc" id="L29">        this.config = config;</span>
<span class="fc" id="L30">        this.connectionPools = new ConcurrentHashMap&lt;&gt;();</span>
<span class="fc" id="L31">        initializePools();</span>
<span class="fc" id="L32">    }</span>

    private void initializePools() {
<span class="fc" id="L35">        logger.info(&quot;Initializing Redis connection pools for {} nodes&quot;, config.getNodes().size());</span>

        // Initialize connection pools for each node
<span class="fc bfc" id="L38" title="All 2 branches covered.">        for (RedisNodeConfig nodeConfig : config.getNodes()) {</span>
<span class="fc" id="L39">            GenericObjectPoolConfig&lt;Jedis&gt; poolConfig = new GenericObjectPoolConfig&lt;&gt;();</span>
            
            // High-throughput optimizations
<span class="fc" id="L42">            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());</span>
<span class="fc" id="L43">            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());</span>
<span class="fc" id="L44">            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm</span>

            // Performance-oriented validation
<span class="fc" id="L47">            poolConfig.setTestOnBorrow(false); // Disable for performance</span>
<span class="fc" id="L48">            poolConfig.setTestOnReturn(false); // Disable expensive validation</span>
<span class="fc" id="L49">            poolConfig.setTestWhileIdle(true); // Only validate idle connections</span>
<span class="fc" id="L50">            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30));</span>

            // High-throughput settings
<span class="fc" id="L53">            poolConfig.setBlockWhenExhausted(false); // Fail fast instead of blocking</span>
<span class="fc" id="L54">            poolConfig.setMaxWait(java.time.Duration.ofMillis(100)); // Quick timeout</span>
<span class="fc" id="L55">            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer</span>
<span class="fc" id="L56">            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead</span>

            // JMX monitoring for production
<span class="fc" id="L59">            poolConfig.setJmxEnabled(true);</span>
<span class="fc" id="L60">            poolConfig.setJmxNamePrefix(&quot;RedisClient-&quot; + nodeConfig.getAddress());</span>

<span class="fc" id="L62">            GenericObjectPool&lt;Jedis&gt; pool = new GenericObjectPool&lt;&gt;(</span>
                new RedisConnectionFactory(nodeConfig, config), poolConfig);

<span class="fc" id="L65">            connectionPools.put(nodeConfig.getAddress(), pool);</span>
<span class="fc" id="L66">            logger.info(&quot;Created Redis connection pool for node: {}&quot;, nodeConfig);</span>
<span class="fc" id="L67">        }</span>
<span class="fc" id="L68">    }</span>

    /**
     * Borrow a Jedis connection from the pool for the specified node.
     *
     * @param nodeConfig The node configuration
     * @return A Jedis connection
     * @throws Exception If unable to borrow connection
     */
    public Jedis borrowConnection(RedisNodeConfig nodeConfig) throws Exception {
<span class="nc" id="L78">        GenericObjectPool&lt;Jedis&gt; pool = connectionPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L80">            throw new IllegalArgumentException(&quot;No pool found for node: &quot; + nodeConfig);</span>
        }
        
<span class="nc" id="L83">        return pool.borrowObject();</span>
    }

    /**
     * Return a Jedis connection to the pool.
     *
     * @param nodeConfig The node configuration
     * @param connection The connection to return
     */
    public void returnConnection(RedisNodeConfig nodeConfig, Jedis connection) {
<span class="nc" id="L93">        GenericObjectPool&lt;Jedis&gt; pool = connectionPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L94" title="All 4 branches missed.">        if (pool != null &amp;&amp; connection != null) {</span>
<span class="nc" id="L95">            pool.returnObject(connection);</span>
        }
<span class="nc" id="L97">    }</span>

    /**
     * Invalidate a connection (remove from pool).
     *
     * @param nodeConfig The node configuration
     * @param connection The connection to invalidate
     */
    public void invalidateConnection(RedisNodeConfig nodeConfig, Jedis connection) {
<span class="nc" id="L106">        GenericObjectPool&lt;Jedis&gt; pool = connectionPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L107" title="All 4 branches missed.">        if (pool != null &amp;&amp; connection != null) {</span>
            try {
<span class="nc" id="L109">                pool.invalidateObject(connection);</span>
<span class="nc" id="L110">            } catch (Exception e) {</span>
<span class="nc" id="L111">                logger.warn(&quot;Failed to invalidate connection for node: {}&quot;, nodeConfig, e);</span>
<span class="nc" id="L112">            }</span>
        }
<span class="nc" id="L114">    }</span>

    /**
     * Get pool statistics for a node.
     *
     * @param nodeConfig The node configuration
     * @return Pool statistics as a string
     */
    public String getPoolStats(RedisNodeConfig nodeConfig) {
<span class="nc" id="L123">        GenericObjectPool&lt;Jedis&gt; pool = connectionPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L125">            return &quot;No pool found for node: &quot; + nodeConfig;</span>
        }

<span class="nc" id="L128">        return String.format(&quot;Node: %s, Active: %d, Idle: %d, Total: %d, Borrowed: %d, Returned: %d&quot;,</span>
<span class="nc" id="L129">            nodeConfig.getAddress(),</span>
<span class="nc" id="L130">            pool.getNumActive(),</span>
<span class="nc" id="L131">            pool.getNumIdle(),</span>
<span class="nc" id="L132">            pool.getNumActive() + pool.getNumIdle(),</span>
<span class="nc" id="L133">            pool.getBorrowedCount(),</span>
<span class="nc" id="L134">            pool.getReturnedCount());</span>
    }

    /**
     * Test if a connection is valid.
     *
     * @param connection The connection to test
     * @return True if connection is valid
     */
    public boolean isConnectionValid(Jedis connection) {
<span class="nc bnc" id="L144" title="All 2 branches missed.">        if (connection == null) {</span>
<span class="nc" id="L145">            return false;</span>
        }

        try {
            // Simple ping test
<span class="nc" id="L150">            String response = connection.ping();</span>
<span class="nc" id="L151">            return &quot;PONG&quot;.equals(response);</span>
<span class="nc" id="L152">        } catch (Exception e) {</span>
<span class="nc" id="L153">            logger.debug(&quot;Connection validation failed&quot;, e);</span>
<span class="nc" id="L154">            return false;</span>
        }
    }

    @Override
    public void close() {
<span class="fc" id="L160">        logger.info(&quot;Closing Redis connection pools&quot;);</span>
        
<span class="fc bfc" id="L162" title="All 2 branches covered.">        for (GenericObjectPool&lt;Jedis&gt; pool : connectionPools.values()) {</span>
            try {
<span class="fc" id="L164">                pool.close();</span>
<span class="nc" id="L165">            } catch (Exception e) {</span>
<span class="nc" id="L166">                logger.warn(&quot;Error closing connection pool&quot;, e);</span>
<span class="fc" id="L167">            }</span>
<span class="fc" id="L168">        }</span>
        
<span class="fc" id="L170">        connectionPools.clear();</span>
<span class="fc" id="L171">        logger.info(&quot;Redis connection pools closed&quot;);</span>
<span class="fc" id="L172">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
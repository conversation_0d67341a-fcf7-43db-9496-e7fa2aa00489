<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NodeRole.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.config</a> &gt; <span class="el_source">NodeRole.java</span></div><h1>NodeRole.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.config;

/**
 * Enum representing the role of a node in the RustyCluster.
 */
<span class="fc" id="L6">public enum NodeRole {</span>
    /**
     * Primary node - first choice for operations.
     */
<span class="fc" id="L10">    PRIMARY(1),</span>

    /**
     * Secondary node - used when primary is unavailable.
     */
<span class="fc" id="L15">    SECONDARY(2),</span>

    /**
     * Tertiary node - used when primary and secondary are unavailable.
     */
<span class="fc" id="L20">    TERTIARY(3);</span>

    private final int priority;

<span class="fc" id="L24">    NodeRole(int priority) {</span>
<span class="fc" id="L25">        this.priority = priority;</span>
<span class="fc" id="L26">    }</span>

    /**
     * Get the priority of the node role.
     * Lower number means higher priority.
     *
     * @return The priority value
     */
    public int getPriority() {
<span class="fc" id="L35">        return priority;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
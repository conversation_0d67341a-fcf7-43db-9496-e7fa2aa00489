<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client</a> &gt; <span class="el_source">RustyClusterClient.java</span></div><h1>RustyClusterClient.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.AsyncConnectionManager;
import org.npci.rustyclient.client.connection.ConnectionManager;
import org.npci.rustyclient.client.connection.OperationType;
import rustycluster.Rustycluster;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Main client for interacting with RustyCluster.
 * This client provides both synchronous and asynchronous operations.
 *
 * Synchronous methods: set(), get(), delete(), hSet(), etc.
 * Asynchronous methods: setAsync(), getAsync(), deleteAsync(), hSetAsync(), etc.
 *
 * Example usage:
 * &lt;pre&gt;
 * RustyClusterClientConfig config = RustyClusterClientConfig.builder()
 *     .addNode(&quot;localhost&quot;, 50051, NodeRole.PRIMARY)
 *     .build();
 *
 * try (RustyClusterClient client = new RustyClusterClient(config)) {
 *     // Synchronous operations
 *     client.set(&quot;key1&quot;, &quot;value1&quot;);
 *     String value = client.get(&quot;key1&quot;);
 *
 *     // Asynchronous operations
 *     CompletableFuture&amp;lt;Boolean&amp;gt; setFuture = client.setAsync(&quot;key2&quot;, &quot;value2&quot;);
 *     CompletableFuture&amp;lt;String&amp;gt; getFuture = client.getAsync(&quot;key2&quot;);
 * }
 * &lt;/pre&gt;
 */
public class RustyClusterClient implements AutoCloseable {
<span class="fc" id="L41">    private static final Logger logger = LoggerFactory.getLogger(RustyClusterClient.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionManager connectionManager;
    private final AsyncConnectionManager asyncConnectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="nc" id="L53">    public RustyClusterClient(RustyClusterClientConfig config) {</span>
<span class="nc" id="L54">        this.config = config;</span>
<span class="nc" id="L55">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="nc" id="L56">        this.connectionManager = new ConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L57">        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L58">        logger.info(&quot;RustyClusterClient initialized with both sync and async capabilities&quot;);</span>
<span class="nc" id="L59">    }</span>

    /**
     * Create a new RustyClusterClient with a custom connection manager (for testing).
     *
     * @param config The client configuration
     * @param connectionManager The connection manager to use
     */
<span class="fc" id="L67">    RustyClusterClient(RustyClusterClientConfig config, ConnectionManager connectionManager) {</span>
<span class="fc" id="L68">        this.config = config;</span>
<span class="fc" id="L69">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="fc" id="L70">        this.connectionManager = connectionManager;</span>
<span class="fc" id="L71">        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="fc" id="L72">        logger.info(&quot;RustyClusterClient initialized with custom connection manager&quot;);</span>
<span class="fc" id="L73">    }</span>

    /**
     * Set a key-value pair.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value, boolean skipReplication) {
<span class="fc" id="L84">        logger.debug(&quot;Setting key: {}&quot;, key);</span>

        try {
<span class="fc" id="L87">            Rustycluster.SetRequest request = Rustycluster.SetRequest.newBuilder()</span>
<span class="fc" id="L88">                    .setKey(key)</span>
<span class="fc" id="L89">                    .setValue(value)</span>
<span class="fc" id="L90">                    .setSkipReplication(skipReplication)</span>
<span class="fc" id="L91">                    .setSkipSiteReplication(false)</span>
<span class="fc" id="L92">                    .build();</span>

<span class="fc" id="L94">            Rustycluster.SetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L95">                    stub.set(request), OperationType.WRITE);</span>

<span class="fc" id="L97">            return response.getSuccess();</span>
<span class="nc" id="L98">        } catch (Exception e) {</span>
<span class="nc" id="L99">            throw e;</span>
        }
    }

    /**
     * Set a key-value pair with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
<span class="fc" id="L111">        return set(key, value, false);</span>
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found or if all nodes are unavailable
     */
    public String get(String key) {
<span class="fc" id="L121">        logger.debug(&quot;Getting key: {}&quot;, key);</span>
<span class="fc" id="L122">        Rustycluster.GetRequest request = Rustycluster.GetRequest.newBuilder()</span>
<span class="fc" id="L123">                .setKey(key)</span>
<span class="fc" id="L124">                .build();</span>

<span class="fc" id="L126">        Rustycluster.GetResponse response = connectionManager.executeWithFailoverSilent(stub -&gt;</span>
<span class="fc" id="L127">                stub.get(request));</span>

<span class="fc bfc" id="L129" title="All 4 branches covered.">        return response != null &amp;&amp; response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Delete a key.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean delete(String key, boolean skipReplication) {
<span class="fc" id="L140">        logger.debug(&quot;Deleting key: {}&quot;, key);</span>
<span class="fc" id="L141">        Rustycluster.DeleteRequest request = Rustycluster.DeleteRequest.newBuilder()</span>
<span class="fc" id="L142">                .setKey(key)</span>
<span class="fc" id="L143">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L144">                .setSkipSiteReplication(false)</span>
<span class="fc" id="L145">                .build();</span>

<span class="fc" id="L147">        Rustycluster.DeleteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L148">                stub.delete(request), OperationType.WRITE);</span>

<span class="fc" id="L150">        return response.getSuccess();</span>
    }

    /**
     * Delete a key with default replication.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
<span class="fc" id="L160">        return delete(key, false);</span>
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key             The key
     * @param value           The value
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl, boolean skipReplication) {
<span class="fc" id="L173">        logger.debug(&quot;Setting key with expiry: {}, ttl: {}&quot;, key, ttl);</span>
<span class="fc" id="L174">        Rustycluster.SetExRequest request = Rustycluster.SetExRequest.newBuilder()</span>
<span class="fc" id="L175">                .setKey(key)</span>
<span class="fc" id="L176">                .setValue(value)</span>
<span class="fc" id="L177">                .setTtl(ttl)</span>
<span class="fc" id="L178">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L179">                .setSkipSiteReplication(false)</span>
<span class="fc" id="L180">                .build();</span>

<span class="fc" id="L182">        Rustycluster.SetExResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L183">                stub.setEx(request), OperationType.WRITE);</span>

<span class="fc" id="L185">        return response.getSuccess();</span>
    }

    /**
     * Set a key-value pair with expiration and default replication.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl) {
<span class="fc" id="L197">        return setEx(key, value, ttl, false);</span>
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key             The key
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl, boolean skipReplication) {
<span class="nc" id="L209">        logger.debug(&quot;Setting expiry on key: {}, ttl: {}&quot;, key, ttl);</span>
<span class="nc" id="L210">        rustycluster.Rustycluster.SetExpiryRequest request = rustycluster.Rustycluster.SetExpiryRequest.newBuilder()</span>
<span class="nc" id="L211">                .setKey(key)</span>
<span class="nc" id="L212">                .setTtl(ttl)</span>
<span class="nc" id="L213">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L214">                .build();</span>

<span class="nc" id="L216">        rustycluster.Rustycluster.SetExpiryResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L217">                stub.setExpiry(request), OperationType.WRITE);</span>

<span class="nc" id="L219">        return response.getSuccess();</span>
    }

    /**
     * Set expiration on an existing key with default replication.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl) {
<span class="nc" id="L230">        return setExpiry(key, ttl, false);</span>
    }

    /**
     * Increment a numeric value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long incrBy(String key, long value, boolean skipReplication) {
<span class="fc" id="L242">        logger.debug(&quot;Incrementing key: {} by {}&quot;, key, value);</span>
<span class="fc" id="L243">        rustycluster.Rustycluster.IncrByRequest request = rustycluster.Rustycluster.IncrByRequest.newBuilder()</span>
<span class="fc" id="L244">                .setKey(key)</span>
<span class="fc" id="L245">                .setValue(value)</span>
<span class="fc" id="L246">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L247">                .build();</span>

<span class="fc" id="L249">        rustycluster.Rustycluster.IncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L250">                stub.incrBy(request), OperationType.WRITE);</span>

<span class="fc" id="L252">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
<span class="fc" id="L263">        return incrBy(key, value, false);</span>
    }

    /**
     * Decrement a numeric value.
     *
     * @param key             The key
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long decrBy(String key, long value, boolean skipReplication) {
<span class="nc" id="L275">        logger.debug(&quot;Decrementing key: {} by {}&quot;, key, value);</span>
<span class="nc" id="L276">        rustycluster.Rustycluster.DecrByRequest request = rustycluster.Rustycluster.DecrByRequest.newBuilder()</span>
<span class="nc" id="L277">                .setKey(key)</span>
<span class="nc" id="L278">                .setValue(value)</span>
<span class="nc" id="L279">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L280">                .build();</span>

<span class="nc" id="L282">        rustycluster.Rustycluster.DecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L283">                stub.decrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L285">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric value with default replication.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
<span class="nc" id="L296">        return decrBy(key, value, false);</span>
    }

    /**
     * Increment a floating-point value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double incrByFloat(String key, double value, boolean skipReplication) {
<span class="nc" id="L308">        logger.debug(&quot;Incrementing key: {} by float {}&quot;, key, value);</span>
<span class="nc" id="L309">        rustycluster.Rustycluster.IncrByFloatRequest request = rustycluster.Rustycluster.IncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L310">                .setKey(key)</span>
<span class="nc" id="L311">                .setValue(value)</span>
<span class="nc" id="L312">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L313">                .build();</span>

<span class="nc" id="L315">        rustycluster.Rustycluster.IncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L316">                stub.incrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L318">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public double incrByFloat(String key, double value) {
<span class="nc" id="L329">        return incrByFloat(key, value, false);</span>
    }

    /**
     * Set a field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L342">        logger.debug(&quot;Setting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L343">        rustycluster.Rustycluster.HSetRequest request = rustycluster.Rustycluster.HSetRequest.newBuilder()</span>
<span class="nc" id="L344">                .setKey(key)</span>
<span class="nc" id="L345">                .setField(field)</span>
<span class="nc" id="L346">                .setValue(value)</span>
<span class="nc" id="L347">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L348">                .setSkipSiteReplication(false)</span>
<span class="nc" id="L349">                .build();</span>

<span class="nc" id="L351">        rustycluster.Rustycluster.HSetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L352">                stub.hSet(request), OperationType.WRITE);</span>

<span class="nc" id="L354">        return response.getSuccess();</span>
    }

    /**
     * Set a field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
<span class="nc" id="L366">        return hSet(key, field, value, false);</span>
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found or if all nodes are unavailable
     */
    public String hGet(String key, String field) {
<span class="fc" id="L377">        logger.debug(&quot;Getting hash field: {}.{}&quot;, key, field);</span>
<span class="fc" id="L378">        rustycluster.Rustycluster.HGetRequest request = rustycluster.Rustycluster.HGetRequest.newBuilder()</span>
<span class="fc" id="L379">                .setKey(key)</span>
<span class="fc" id="L380">                .setField(field)</span>
<span class="fc" id="L381">                .build();</span>

<span class="fc" id="L383">        rustycluster.Rustycluster.HGetResponse response = connectionManager.executeWithFailoverSilent(stub -&gt;</span>
<span class="nc" id="L384">                stub.hGet(request));</span>

<span class="pc bpc" id="L386" title="3 of 4 branches missed.">        return response != null &amp;&amp; response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map&lt;String, String&gt; hGetAll(String key) {
<span class="fc" id="L396">        logger.debug(&quot;Getting all hash fields for key: {}&quot;, key);</span>
<span class="fc" id="L397">        rustycluster.Rustycluster.HGetAllRequest request = rustycluster.Rustycluster.HGetAllRequest.newBuilder()</span>
<span class="fc" id="L398">                .setKey(key)</span>
<span class="fc" id="L399">                .build();</span>

<span class="fc" id="L401">        rustycluster.Rustycluster.HGetAllResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L402">                stub.hGetAll(request), OperationType.READ);</span>

<span class="fc" id="L404">        return response.getFieldsMap();</span>
    }

    /**
     * Increment a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L417">        logger.debug(&quot;Incrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L418">        rustycluster.Rustycluster.HIncrByRequest request = rustycluster.Rustycluster.HIncrByRequest.newBuilder()</span>
<span class="nc" id="L419">                .setKey(key)</span>
<span class="nc" id="L420">                .setField(field)</span>
<span class="nc" id="L421">                .setValue(value)</span>
<span class="nc" id="L422">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L423">                .setSkipSiteReplication(false)</span>
<span class="nc" id="L424">                .build();</span>

<span class="nc" id="L426">        rustycluster.Rustycluster.HIncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L427">                stub.hIncrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L429">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value) {
<span class="nc" id="L441">        return hIncrBy(key, field, value, false);</span>
    }

    /**
     * Decrement a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L454">        logger.debug(&quot;Decrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L455">        rustycluster.Rustycluster.HDecrByRequest request = rustycluster.Rustycluster.HDecrByRequest.newBuilder()</span>
<span class="nc" id="L456">                .setKey(key)</span>
<span class="nc" id="L457">                .setField(field)</span>
<span class="nc" id="L458">                .setValue(value)</span>
<span class="nc" id="L459">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L460">                .setSkipSiteReplication(false)</span>
<span class="nc" id="L461">                .build();</span>

<span class="nc" id="L463">        rustycluster.Rustycluster.HDecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L464">                stub.hDecrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L466">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value) {
<span class="nc" id="L478">        return hDecrBy(key, field, value, false);</span>
    }

    /**
     * Increment a floating-point field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value, boolean skipReplication) {
<span class="nc" id="L491">        logger.debug(&quot;Incrementing hash field: {}.{} by float {}&quot;, key, field, value);</span>
<span class="nc" id="L492">        rustycluster.Rustycluster.HIncrByFloatRequest request = rustycluster.Rustycluster.HIncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L493">                .setKey(key)</span>
<span class="nc" id="L494">                .setField(field)</span>
<span class="nc" id="L495">                .setValue(value)</span>
<span class="nc" id="L496">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L497">                .build();</span>

<span class="nc" id="L499">        rustycluster.Rustycluster.HIncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L500">                stub.hIncrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L502">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value) {
<span class="nc" id="L514">        return hIncrByFloat(key, field, value, false);</span>
    }

    /**
     * Execute a batch of operations.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations, boolean skipReplication) {
<span class="fc" id="L525">        logger.debug(&quot;Executing batch write with {} operations&quot;, operations.size());</span>
<span class="fc" id="L526">        rustycluster.Rustycluster.BatchWriteRequest request = rustycluster.Rustycluster.BatchWriteRequest.newBuilder()</span>
<span class="fc" id="L527">                .addAllOperations(operations)</span>
<span class="fc" id="L528">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L529">                .build();</span>

<span class="fc" id="L531">        rustycluster.Rustycluster.BatchWriteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L532">                stub.batchWrite(request), OperationType.WRITE);</span>

<span class="fc" id="L534">        return response.getOperationResultsList();</span>
    }

    /**
     * Execute a batch of operations with default replication.
     *
     * @param operations The list of operations to execute
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations) {
<span class="fc" id="L544">        return batchWrite(operations, false);</span>
    }

    /**
     * Authenticate with the RustyCluster server.
     * This method should be called before performing any operations if authentication is configured.
     *
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate() {
<span class="nc" id="L554">        logger.debug(&quot;Attempting to authenticate with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L556" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L557">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L558">            return true;</span>
        }

        try {
            // Get a stub from the connection manager and authenticate
<span class="nc" id="L563">            return connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L564">                authenticationManager.authenticate(stub), OperationType.AUTH);</span>
<span class="nc" id="L565">        } catch (Exception e) {</span>
<span class="nc" id="L566">            logger.error(&quot;Authentication failed&quot;, e);</span>
<span class="nc" id="L567">            return false;</span>
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="nc" id="L577">        return authenticationManager.isAuthenticated();</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="nc" id="L586">        return authenticationManager.getSessionToken();</span>
    }

    // ==================== NEW METHODS ====================

    /**
     * Set multiple fields in a hash.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map&lt;String, String&gt; fields, boolean skipReplication) {
<span class="nc" id="L600">        logger.debug(&quot;Setting multiple hash fields for key: {}, fields count: {}&quot;, key, fields.size());</span>

        try {
<span class="nc" id="L603">            rustycluster.Rustycluster.HMSetRequest request = rustycluster.Rustycluster.HMSetRequest.newBuilder()</span>
<span class="nc" id="L604">                    .setKey(key)</span>
<span class="nc" id="L605">                    .putAllFields(fields)</span>
<span class="nc" id="L606">                    .setSkipReplication(skipReplication)</span>
<span class="nc" id="L607">                    .setSkipSiteReplication(false)</span>
<span class="nc" id="L608">                    .build();</span>

<span class="nc" id="L610">            rustycluster.Rustycluster.HMSetResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L611">                    stub -&gt; stub.hMSet(request), OperationType.WRITE</span>
            );

<span class="nc" id="L614">            return response.getSuccess();</span>
<span class="nc" id="L615">        } catch (Exception e) {</span>
<span class="nc" id="L616">            logger.error(&quot;Error setting multiple hash fields for key: {}&quot;, key, e);</span>
<span class="nc" id="L617">            return false;</span>
        }
    }

    /**
     * Set multiple fields in a hash with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map&lt;String, String&gt; fields) {
<span class="nc" id="L629">        return hMSet(key, fields, false);</span>
    }

    /**
     * Check if a key exists.
     *
     * @param key The key to check
     * @return True if the key exists, false otherwise or if all nodes are unavailable
     */
    public boolean exists(String key) {
<span class="fc" id="L639">        logger.debug(&quot;Checking if key exists: {}&quot;, key);</span>

<span class="fc" id="L641">        rustycluster.Rustycluster.ExistsRequest request = rustycluster.Rustycluster.ExistsRequest.newBuilder()</span>
<span class="fc" id="L642">                .setKey(key)</span>
<span class="fc" id="L643">                .build();</span>

<span class="fc" id="L645">        rustycluster.Rustycluster.ExistsResponse response = connectionManager.executeWithFailoverSilent(</span>
<span class="nc" id="L646">                stub -&gt; stub.exists(request)</span>
        );

<span class="pc bpc" id="L649" title="3 of 4 branches missed.">        return response != null &amp;&amp; response.getExists();</span>
    }

    /**
     * Check if a hash field exists.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if field exists, false otherwise or if all nodes are unavailable
     */
    public boolean hExists(String key, String field) {
<span class="fc" id="L660">        logger.debug(&quot;Checking if hash field exists: {}.{}&quot;, key, field);</span>

<span class="fc" id="L662">        rustycluster.Rustycluster.HExistsRequest request = rustycluster.Rustycluster.HExistsRequest.newBuilder()</span>
<span class="fc" id="L663">                .setKey(key)</span>
<span class="fc" id="L664">                .setField(field)</span>
<span class="fc" id="L665">                .build();</span>

<span class="fc" id="L667">        rustycluster.Rustycluster.HExistsResponse response = connectionManager.executeWithFailoverSilent(</span>
<span class="nc" id="L668">                stub -&gt; stub.hExists(request)</span>
        );

<span class="pc bpc" id="L671" title="3 of 4 branches missed.">        return response != null &amp;&amp; response.getExists();</span>
    }

    /**
     * Set a key-value pair only if the key does not exist.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value, boolean skipReplication) {
<span class="nc" id="L683">        logger.debug(&quot;Setting key if not exists: {}&quot;, key);</span>

        try {
<span class="nc" id="L686">            rustycluster.Rustycluster.SetNXRequest request = rustycluster.Rustycluster.SetNXRequest.newBuilder()</span>
<span class="nc" id="L687">                    .setKey(key)</span>
<span class="nc" id="L688">                    .setValue(value)</span>
<span class="nc" id="L689">                    .setSkipReplication(skipReplication)</span>
<span class="nc" id="L690">                    .setSkipSiteReplication(false)</span>
<span class="nc" id="L691">                    .build();</span>

<span class="nc" id="L693">            rustycluster.Rustycluster.SetNXResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L694">                    stub -&gt; stub.setNX(request), OperationType.WRITE</span>
            );

<span class="nc" id="L697">            return response.getSuccess();</span>
<span class="nc" id="L698">        } catch (Exception e) {</span>
<span class="nc" id="L699">            logger.error(&quot;Error setting key if not exists: {}&quot;, key, e);</span>
<span class="nc" id="L700">            return false;</span>
        }
    }

    /**
     * Set a key-value pair only if the key does not exist with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value) {
<span class="nc" id="L712">        return setNX(key, value, false);</span>
    }



    /**
     * Load a Lua script and return its SHA hash.
     *
     * @param script The Lua script to load
     * @return The SHA hash of the loaded script, or null if failed
     */
    public String loadScript(String script) {
<span class="nc" id="L724">        logger.debug(&quot;Loading script, length: {}&quot;, script.length());</span>

        try {
<span class="nc" id="L727">            rustycluster.Rustycluster.LoadScriptRequest request = rustycluster.Rustycluster.LoadScriptRequest.newBuilder()</span>
<span class="nc" id="L728">                    .setScript(script)</span>
<span class="nc" id="L729">                    .build();</span>

<span class="nc" id="L731">            rustycluster.Rustycluster.LoadScriptResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L732">                    stub -&gt; stub.loadScript(request), OperationType.WRITE</span>
            );

<span class="nc bnc" id="L735" title="All 2 branches missed.">            return response.getSuccess() ? response.getSha() : null;</span>
<span class="nc" id="L736">        } catch (Exception e) {</span>
<span class="nc" id="L737">            logger.error(&quot;Error loading script&quot;, e);</span>
<span class="nc" id="L738">            return null;</span>
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List&lt;String&gt; keys, List&lt;String&gt; args, boolean skipReplication) {
<span class="nc" id="L752">        logger.debug(&quot;Executing script with SHA: {}, keys: {}, args: {}&quot;, sha, keys.size(), args.size());</span>

        try {
<span class="nc" id="L755">            rustycluster.Rustycluster.EvalShaRequest request = rustycluster.Rustycluster.EvalShaRequest.newBuilder()</span>
<span class="nc" id="L756">                    .setSha(sha)</span>
<span class="nc" id="L757">                    .addAllKeys(keys)</span>
<span class="nc" id="L758">                    .addAllArgs(args)</span>
<span class="nc" id="L759">                    .setSkipReplication(skipReplication)</span>
<span class="nc" id="L760">                    .setSkipSiteReplication(false)</span>
<span class="nc" id="L761">                    .build();</span>

<span class="nc" id="L763">            rustycluster.Rustycluster.EvalShaResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L764">                    stub -&gt; stub.evalSha(request), OperationType.WRITE</span>
            );

<span class="nc bnc" id="L767" title="All 2 branches missed.">            return response.getSuccess() ? response.getResult() : null;</span>
<span class="nc" id="L768">        } catch (Exception e) {</span>
<span class="nc" id="L769">            logger.error(&quot;Error executing script with SHA: {}&quot;, sha, e);</span>
<span class="nc" id="L770">            return null;</span>
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
<span class="nc" id="L783">        return evalSha(sha, keys, args, false);</span>
    }

    /**
     * Perform a health check on the cluster.
     *
     * @return True if healthy, false otherwise
     */
    public boolean healthCheck() {
<span class="nc" id="L792">        logger.debug(&quot;Performing health check&quot;);</span>

        // Use ping for now until HealthCheck RPC is available
        try {
<span class="nc" id="L796">            return ping();</span>
<span class="nc" id="L797">        } catch (Exception e) {</span>
<span class="nc" id="L798">            logger.warn(&quot;Health check failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L799">            return false;</span>
        }
    }

    /**
     * Ping the cluster to check connectivity.
     *
     * @return True if ping successful, false otherwise
     */
    public boolean ping() {
<span class="nc" id="L809">        logger.debug(&quot;Pinging cluster&quot;);</span>

        try {
<span class="nc" id="L812">            rustycluster.Rustycluster.PingRequest request = rustycluster.Rustycluster.PingRequest.newBuilder().build();</span>
<span class="nc" id="L813">            rustycluster.Rustycluster.PingResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L814">                    stub.ping(request), OperationType.READ);</span>
<span class="nc" id="L815">            return response.getSuccess();</span>
<span class="nc" id="L816">        } catch (Exception e) {</span>
<span class="nc" id="L817">            logger.warn(&quot;Ping failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L818">            return false;</span>
        }
    }

    // ==================== ASYNCHRONOUS METHODS ====================

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value, boolean skipReplication) {
<span class="nc" id="L833">        logger.debug(&quot;Setting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L834">        rustycluster.Rustycluster.SetRequest request = rustycluster.Rustycluster.SetRequest.newBuilder()</span>
<span class="nc" id="L835">                .setKey(key)</span>
<span class="nc" id="L836">                .setValue(value)</span>
<span class="nc" id="L837">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L838">                .build();</span>

<span class="nc" id="L840">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L841">                stub.set(request))</span>
<span class="nc" id="L842">                .thenApply(rustycluster.Rustycluster.SetResponse::getSuccess);</span>
    }

    /**
     * Set a key-value pair asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value) {
<span class="nc" id="L853">        return setAsync(key, value, false);</span>
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found or if all nodes are unavailable
     */
    public CompletableFuture&lt;String&gt; getAsync(String key) {
<span class="nc" id="L863">        logger.debug(&quot;Getting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L864">        rustycluster.Rustycluster.GetRequest request = rustycluster.Rustycluster.GetRequest.newBuilder()</span>
<span class="nc" id="L865">                .setKey(key)</span>
<span class="nc" id="L866">                .build();</span>

<span class="nc" id="L868">        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub -&gt;</span>
<span class="nc" id="L869">                stub.get(request))</span>
<span class="nc bnc" id="L870" title="All 4 branches missed.">                .thenApply(response -&gt; response != null &amp;&amp; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key, boolean skipReplication) {
<span class="nc" id="L881">        logger.debug(&quot;Deleting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L882">        rustycluster.Rustycluster.DeleteRequest request = rustycluster.Rustycluster.DeleteRequest.newBuilder()</span>
<span class="nc" id="L883">                .setKey(key)</span>
<span class="nc" id="L884">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L885">                .build();</span>

<span class="nc" id="L887">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L888">                stub.delete(request))</span>
<span class="nc" id="L889">                .thenApply(rustycluster.Rustycluster.DeleteResponse::getSuccess);</span>
    }

    /**
     * Delete a key asynchronously with default replication.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key) {
<span class="nc" id="L899">        return deleteAsync(key, false);</span>
    }

    /**
     * Execute a batch of operations asynchronously.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations, boolean skipReplication) {
<span class="nc" id="L910">        logger.debug(&quot;Executing batch write asynchronously with {} operations&quot;, operations.size());</span>
<span class="nc" id="L911">        rustycluster.Rustycluster.BatchWriteRequest request = rustycluster.Rustycluster.BatchWriteRequest.newBuilder()</span>
<span class="nc" id="L912">                .addAllOperations(operations)</span>
<span class="nc" id="L913">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L914">                .build();</span>

<span class="nc" id="L916">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L917">                stub.batchWrite(request))</span>
<span class="nc" id="L918">                .thenApply(rustycluster.Rustycluster.BatchWriteResponse::getOperationResultsList);</span>
    }

    /**
     * Execute a batch of operations asynchronously with default replication.
     *
     * @param operations The list of operations to execute
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations) {
<span class="nc" id="L928">        return batchWriteAsync(operations, false);</span>
    }

    /**
     * Increment a numeric value asynchronously.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value, boolean skipReplication) {
<span class="nc" id="L940">        logger.debug(&quot;Incrementing key asynchronously: {} by {}&quot;, key, value);</span>
<span class="nc" id="L941">        rustycluster.Rustycluster.IncrByRequest request = rustycluster.Rustycluster.IncrByRequest.newBuilder()</span>
<span class="nc" id="L942">                .setKey(key)</span>
<span class="nc" id="L943">                .setValue(value)</span>
<span class="nc" id="L944">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L945">                .build();</span>

<span class="nc" id="L947">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L948">                stub.incrBy(request))</span>
<span class="nc" id="L949">                .thenApply(rustycluster.Rustycluster.IncrByResponse::getNewValue);</span>
    }

    /**
     * Increment a numeric value asynchronously with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value) {
<span class="nc" id="L960">        return incrByAsync(key, value, false);</span>
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L973">        logger.debug(&quot;Setting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="nc" id="L974">        rustycluster.Rustycluster.HSetRequest request = rustycluster.Rustycluster.HSetRequest.newBuilder()</span>
<span class="nc" id="L975">                .setKey(key)</span>
<span class="nc" id="L976">                .setField(field)</span>
<span class="nc" id="L977">                .setValue(value)</span>
<span class="nc" id="L978">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L979">                .build();</span>

<span class="nc" id="L981">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L982">                stub.hSet(request))</span>
<span class="nc" id="L983">                .thenApply(rustycluster.Rustycluster.HSetResponse::getSuccess);</span>
    }

    /**
     * Set a hash field asynchronously with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value) {
<span class="nc" id="L995">        return hSetAsync(key, field, value, false);</span>
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found or if all nodes are unavailable
     */
    public CompletableFuture&lt;String&gt; hGetAsync(String key, String field) {
<span class="nc" id="L1006">        logger.debug(&quot;Getting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="nc" id="L1007">        rustycluster.Rustycluster.HGetRequest request = rustycluster.Rustycluster.HGetRequest.newBuilder()</span>
<span class="nc" id="L1008">                .setKey(key)</span>
<span class="nc" id="L1009">                .setField(field)</span>
<span class="nc" id="L1010">                .build();</span>

<span class="nc" id="L1012">        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub -&gt;</span>
<span class="nc" id="L1013">                stub.hGet(request))</span>
<span class="nc bnc" id="L1014" title="All 4 branches missed.">                .thenApply(response -&gt; response != null &amp;&amp; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Get all fields from a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture&lt;Map&lt;String, String&gt;&gt; hGetAllAsync(String key) {
<span class="nc" id="L1024">        logger.debug(&quot;Getting all hash fields asynchronously for key: {}&quot;, key);</span>
<span class="nc" id="L1025">        rustycluster.Rustycluster.HGetAllRequest request = rustycluster.Rustycluster.HGetAllRequest.newBuilder()</span>
<span class="nc" id="L1026">                .setKey(key)</span>
<span class="nc" id="L1027">                .build();</span>

<span class="nc" id="L1029">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L1030">                stub.hGetAll(request))</span>
<span class="nc" id="L1031">                .thenApply(rustycluster.Rustycluster.HGetAllResponse::getFieldsMap);</span>
    }

    // ==================== NEW ASYNC METHODS ====================

    /**
     * Set multiple fields in a hash asynchronously.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields, boolean skipReplication) {
<span class="nc" id="L1045">        logger.debug(&quot;Setting multiple hash fields asynchronously for key: {}, fields count: {}&quot;, key, fields.size());</span>

        // For now, we'll implement this using individual hSet calls until gRPC classes are regenerated
        // This is a temporary implementation
<span class="nc" id="L1049">        List&lt;CompletableFuture&lt;Boolean&gt;&gt; futures = fields.entrySet().stream()</span>
<span class="nc" id="L1050">                .map(entry -&gt; hSetAsync(key, entry.getKey(), entry.getValue(), skipReplication))</span>
<span class="nc" id="L1051">                .toList();</span>

<span class="nc" id="L1053">        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))</span>
<span class="nc" id="L1054">                .thenApply(v -&gt; futures.stream().allMatch(CompletableFuture::join));</span>
    }

    /**
     * Set multiple fields in a hash asynchronously with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields) {
<span class="nc" id="L1065">        return hMSetAsync(key, fields, false);</span>
    }

    /**
     * Check if a hash field exists asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with true if field exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hExistsAsync(String key, String field) {
<span class="nc" id="L1076">        logger.debug(&quot;Checking if hash field exists asynchronously: {}.{}&quot;, key, field);</span>

        // Temporary implementation using hGet until gRPC classes are regenerated
<span class="nc bnc" id="L1079" title="All 2 branches missed.">        return hGetAsync(key, field).thenApply(value -&gt; value != null);</span>
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if key exists, false otherwise or if all nodes are unavailable
     */
    public CompletableFuture&lt;Boolean&gt; existsAsync(String key) {
<span class="nc" id="L1089">        logger.debug(&quot;Checking if key exists asynchronously: {}&quot;, key);</span>

<span class="nc" id="L1091">        rustycluster.Rustycluster.ExistsRequest request = rustycluster.Rustycluster.ExistsRequest.newBuilder()</span>
<span class="nc" id="L1092">                .setKey(key)</span>
<span class="nc" id="L1093">                .build();</span>

<span class="nc" id="L1095">        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub -&gt;</span>
<span class="nc" id="L1096">                stub.exists(request))</span>
<span class="nc bnc" id="L1097" title="All 4 branches missed.">                .thenApply(response -&gt; response != null &amp;&amp; response.getExists());</span>
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value, boolean skipReplication) {
<span class="nc" id="L1109">        logger.debug(&quot;Setting key if not exists asynchronously: {}&quot;, key);</span>

        // Temporary implementation using exists check + set until gRPC classes are regenerated
<span class="nc" id="L1112">        return existsAsync(key).thenCompose(exists -&gt; {</span>
<span class="nc bnc" id="L1113" title="All 2 branches missed.">            if (exists) {</span>
<span class="nc" id="L1114">                return CompletableFuture.completedFuture(false); // Key already exists</span>
            }
<span class="nc" id="L1116">            return setAsync(key, value, skipReplication);</span>
        });
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value) {
<span class="nc" id="L1128">        return setNXAsync(key, value, false);</span>
    }

    /**
     * Load a Lua script and return its SHA hash asynchronously.
     *
     * @param script The Lua script to load
     * @return CompletableFuture that completes with the SHA hash of the loaded script, or null if failed
     */
    public CompletableFuture&lt;String&gt; loadScriptAsync(String script) {
<span class="nc" id="L1138">        logger.debug(&quot;Loading script asynchronously, length: {}&quot;, script.length());</span>

        // Temporary implementation - return a mock SHA until gRPC classes are regenerated
        // In real implementation, this would call the LoadScript RPC
<span class="nc" id="L1142">        logger.warn(&quot;loadScriptAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L1143">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args, boolean skipReplication) {
<span class="nc" id="L1156">        logger.debug(&quot;Executing script asynchronously with SHA: {}, keys: {}, args: {}&quot;, sha, keys.size(), args.size());</span>

        // Temporary implementation until gRPC classes are regenerated
<span class="nc" id="L1159">        logger.warn(&quot;evalShaAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L1160">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
<span class="nc" id="L1172">        return evalShaAsync(sha, keys, args, false);</span>
    }

    /**
     * Perform a health check on the cluster asynchronously.
     *
     * @return CompletableFuture that completes with true if healthy, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; healthCheckAsync() {
<span class="nc" id="L1181">        logger.debug(&quot;Performing health check asynchronously&quot;);</span>

        // Use ping for now until HealthCheck RPC is available
<span class="nc" id="L1184">        return pingAsync().exceptionally(throwable -&gt; {</span>
<span class="nc" id="L1185">            logger.warn(&quot;Health check failed: {}&quot;, throwable.getMessage());</span>
<span class="nc" id="L1186">            return false;</span>
        });
    }

    /**
     * Ping the cluster to check connectivity asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; pingAsync() {
<span class="nc" id="L1196">        logger.debug(&quot;Pinging cluster asynchronously&quot;);</span>

<span class="nc" id="L1198">        rustycluster.Rustycluster.PingRequest request = rustycluster.Rustycluster.PingRequest.newBuilder().build();</span>
<span class="nc" id="L1199">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L1200">                stub.ping(request))</span>
<span class="nc" id="L1201">                .thenApply(rustycluster.Rustycluster.PingResponse::getSuccess)</span>
<span class="nc" id="L1202">                .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L1203">                    logger.warn(&quot;Ping failed: {}&quot;, throwable.getMessage());</span>
<span class="nc" id="L1204">                    return false;</span>
                });
    }

    /**
     * Authenticate with the RustyCluster server asynchronously.
     *
     * @return CompletableFuture that completes with true if authentication was successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; authenticateAsync() {
<span class="nc" id="L1214">        logger.debug(&quot;Attempting to authenticate asynchronously with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L1216" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L1217">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L1218">            return CompletableFuture.completedFuture(true);</span>
        }

        // For async authentication, we need to use a different approach
        // since authenticate method expects a blocking stub
<span class="nc" id="L1223">        return CompletableFuture.supplyAsync(() -&gt; {</span>
            try {
                // This is a simplified approach - in a real implementation,
                // you'd want to create an async version of authenticate
<span class="nc" id="L1227">                return authenticationManager.isAuthenticated();</span>
<span class="nc" id="L1228">            } catch (Exception e) {</span>
<span class="nc" id="L1229">                logger.error(&quot;Authentication check failed&quot;, e);</span>
<span class="nc" id="L1230">                return false;</span>
            }
        });
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L1240">        authenticationManager.clearAuthentication();</span>
<span class="fc" id="L1241">        connectionManager.close();</span>
<span class="fc" id="L1242">        asyncConnectionManager.close();</span>
<span class="fc" id="L1243">        logger.info(&quot;RustyClusterClient closed&quot;);</span>
<span class="fc" id="L1244">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
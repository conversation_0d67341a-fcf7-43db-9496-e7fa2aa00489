import com.rustycluster.client.config.RustyClusterClientConfig;

public class test_optimizations {
    public static void main(String[] args) {
        System.out.println("Testing RustyCluster optimizations...");
        
        // Test default values
        RustyClusterClientConfig defaultConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .build();
        
        System.out.println("Default config:");
        System.out.println("  Max connections per node: " + defaultConfig.getMaxConnectionsPerNode());
        System.out.println("  Connection timeout: " + defaultConfig.getConnectionTimeoutMs() + "ms");
        System.out.println("  Read timeout: " + defaultConfig.getReadTimeoutMs() + "ms");
        System.out.println("  Write timeout: " + defaultConfig.getWriteTimeoutMs() + "ms");
        System.out.println("  Max retries: " + defaultConfig.getMaxRetries());
        System.out.println("  Retry delay: " + defaultConfig.getRetryDelayMs() + "ms");
        
        // Test high-throughput preset
        RustyClusterClientConfig highThroughputConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .highThroughputPreset()
                .build();
        
        System.out.println("\nHigh-throughput preset:");
        System.out.println("  Max connections per node: " + highThroughputConfig.getMaxConnectionsPerNode());
        System.out.println("  Connection timeout: " + highThroughputConfig.getConnectionTimeoutMs() + "ms");
        System.out.println("  Read timeout: " + highThroughputConfig.getReadTimeoutMs() + "ms");
        System.out.println("  Write timeout: " + highThroughputConfig.getWriteTimeoutMs() + "ms");
        System.out.println("  Max retries: " + highThroughputConfig.getMaxRetries());
        System.out.println("  Retry delay: " + highThroughputConfig.getRetryDelayMs() + "ms");
        
        // Test low-latency preset
        RustyClusterClientConfig lowLatencyConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .lowLatencyPreset()
                .build();
        
        System.out.println("\nLow-latency preset:");
        System.out.println("  Max connections per node: " + lowLatencyConfig.getMaxConnectionsPerNode());
        System.out.println("  Connection timeout: " + lowLatencyConfig.getConnectionTimeoutMs() + "ms");
        System.out.println("  Read timeout: " + lowLatencyConfig.getReadTimeoutMs() + "ms");
        System.out.println("  Write timeout: " + lowLatencyConfig.getWriteTimeoutMs() + "ms");
        System.out.println("  Max retries: " + lowLatencyConfig.getMaxRetries());
        System.out.println("  Retry delay: " + lowLatencyConfig.getRetryDelayMs() + "ms");
        
        // Test balanced preset
        RustyClusterClientConfig balancedConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .balancedPreset()
                .build();
        
        System.out.println("\nBalanced preset:");
        System.out.println("  Max connections per node: " + balancedConfig.getMaxConnectionsPerNode());
        System.out.println("  Connection timeout: " + balancedConfig.getConnectionTimeoutMs() + "ms");
        System.out.println("  Read timeout: " + balancedConfig.getReadTimeoutMs() + "ms");
        System.out.println("  Write timeout: " + balancedConfig.getWriteTimeoutMs() + "ms");
        System.out.println("  Max retries: " + balancedConfig.getMaxRetries());
        System.out.println("  Retry delay: " + balancedConfig.getRetryDelayMs() + "ms");
        
        System.out.println("\nAll optimizations are working correctly!");
    }
}

package org.npci.rustyclient.redis.config;

/**
 * Configuration for a Redis node.
 *
 * @param host The host address of the node
 * @param port The port number of the node
 * @param role The role of the node
 */
public record RedisNodeConfig(String host, int port, RedisNodeRole role) {
    /**
     * Get the address of the node in the format "host:port".
     *
     * @return The node address
     */
    public String getAddress() {
        return host + ":" + port;
    }
}

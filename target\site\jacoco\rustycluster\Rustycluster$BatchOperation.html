<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Rustycluster.BatchOperation</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">rustycluster</a> &gt; <span class="el_class">Rustycluster.BatchOperation</span></div><h1>Rustycluster.BatchOperation</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,110 of 1,281</td><td class="ctr2">13%</td><td class="bar">151 of 158</td><td class="ctr2">4%</td><td class="ctr1">132</td><td class="ctr2">145</td><td class="ctr1">260</td><td class="ctr2">310</td><td class="ctr1">53</td><td class="ctr2">66</td></tr></tfoot><tbody><tr><td id="a29"><a href="Rustycluster.java.html#L29118" class="el_method">getSerializedSize()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="209" alt="209"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="24" alt="24"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f1">13</td><td class="ctr2" id="g1">13</td><td class="ctr1" id="h0">44</td><td class="ctr2" id="i0">44</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a35"><a href="Rustycluster.java.html#L29234" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="119" height="10" title="208" alt="208"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="18" alt="18"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f3">10</td><td class="ctr2" id="g3">10</td><td class="ctr1" id="h1">40</td><td class="ctr2" id="i1">40</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="Rustycluster.java.html#L29183" class="el_method">equals(Object)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="164" alt="164"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="48" alt="48"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f0">25</td><td class="ctr2" id="g0">25</td><td class="ctr1" id="h2">39</td><td class="ctr2" id="i2">39</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a65"><a href="Rustycluster.java.html#L29077" class="el_method">writeTo(CodedOutputStream)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="69" height="10" title="121" alt="121"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="20" alt="20"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f2">11</td><td class="ctr2" id="g2">11</td><td class="ctr1" id="h3">25</td><td class="ctr2" id="i3">25</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a12"><a href="Rustycluster.java.html#L28889" class="el_method">getHashFieldsOrThrow(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="24" alt="24"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h9">6</td><td class="ctr2" id="i14">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a11"><a href="Rustycluster.java.html#L28874" class="el_method">getHashFieldsOrDefault(String, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="22" alt="22"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i16">4</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a27"><a href="Rustycluster.java.html#L29029" class="el_method">getScriptSha()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="20" alt="20"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a15"><a href="Rustycluster.java.html#L28612" class="el_method">getKeyBytes()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="18" alt="18"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a32"><a href="Rustycluster.java.html#L28651" class="el_method">getValueBytes()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="18" alt="18"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a6"><a href="Rustycluster.java.html#L28710" class="el_method">getFieldBytes()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="18" alt="18"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a28"><a href="Rustycluster.java.html#L29051" class="el_method">getScriptShaBytes()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="18" alt="18"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="Rustycluster.java.html#L28839" class="el_method">containsHashFields(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="13" alt="13"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h16">2</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a14"><a href="Rustycluster.java.html#L28594" class="el_method">getKey()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c10">45%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h11">4</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j53">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a31"><a href="Rustycluster.java.html#L28633" class="el_method">getValue()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">45%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">7</td><td class="ctr1" id="j54">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a5"><a href="Rustycluster.java.html#L28688" class="el_method">getField()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">45%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h13">4</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j55">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a41"><a href="Rustycluster.java.html#L28325" class="el_method">internalGetMapField(int)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="11" alt="11"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h14">3</td><td class="ctr2" id="i17">3</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a33"><a href="Rustycluster.java.html#L28676" class="el_method">hasField()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g17">2</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a36"><a href="Rustycluster.java.html#L28734" class="el_method">hasIntValue()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a34"><a href="Rustycluster.java.html#L28761" class="el_method">hasFloatValue()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a38"><a href="Rustycluster.java.html#L28788" class="el_method">hasTtl()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a40"><a href="Rustycluster.java.html#L28820" class="el_method">internalGetHashFields()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h15">3</td><td class="ctr2" id="i18">3</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a37"><a href="Rustycluster.java.html#L29017" class="el_method">hasScriptSha()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a46"><a href="Rustycluster.java.html#L29374" class="el_method">newBuilderForType(GeneratedMessageV3.BuilderParent)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h17">2</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a53"><a href="Rustycluster.java.html#L29294" class="el_method">parseFrom(ByteBuffer, ExtensionRegistryLite)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a55"><a href="Rustycluster.java.html#L29305" class="el_method">parseFrom(ByteString, ExtensionRegistryLite)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a51"><a href="Rustycluster.java.html#L29315" class="el_method">parseFrom(byte[], ExtensionRegistryLite)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a59"><a href="Rustycluster.java.html#L29326" class="el_method">parseFrom(InputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h18">2</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a49"><a href="Rustycluster.java.html#L29340" class="el_method">parseDelimitedFrom(InputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h19">2</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a57"><a href="Rustycluster.java.html#L29353" class="el_method">parseFrom(CodedInputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h20">2</td><td class="ctr2" id="i23">2</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a64"><a href="Rustycluster.java.html#L29367" class="el_method">toBuilder()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c9">61%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h56">0</td><td class="ctr2" id="i24">2</td><td class="ctr1" id="j56">0</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a39"><a href="Rustycluster.java.html#L28336" class="el_method">internalGetFieldAccessorTable()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h21">2</td><td class="ctr2" id="i25">2</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a9"><a href="Rustycluster.java.html#L28827" class="el_method">getHashFieldsCount()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a23"><a href="Rustycluster.java.html#L28935" class="el_method">getScriptKeys(int)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a24"><a href="Rustycluster.java.html#L28948" class="el_method">getScriptKeysBytes(int)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a19"><a href="Rustycluster.java.html#L28988" class="el_method">getScriptArgs(int)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a20"><a href="Rustycluster.java.html#L29001" class="el_method">getScriptArgsBytes(int)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a52"><a href="Rustycluster.java.html#L29288" class="el_method">parseFrom(ByteBuffer)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a54"><a href="Rustycluster.java.html#L29299" class="el_method">parseFrom(ByteString)</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a50"><a href="Rustycluster.java.html#L29309" class="el_method">parseFrom(byte[])</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a58"><a href="Rustycluster.java.html#L29319" class="el_method">parseFrom(InputStream)</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h22">2</td><td class="ctr2" id="i26">2</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a48"><a href="Rustycluster.java.html#L29332" class="el_method">parseDelimitedFrom(InputStream)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h23">2</td><td class="ctr2" id="i27">2</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a56"><a href="Rustycluster.java.html#L29346" class="el_method">parseFrom(CodedInputStream)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h24">2</td><td class="ctr2" id="i28">2</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a44"><a href="Rustycluster.java.html#L29363" class="el_method">newBuilder(Rustycluster.BatchOperation)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a42"><a href="Rustycluster.java.html#L29066" class="el_method">isInitialized()</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="13" alt="13"/></td><td class="ctr2" id="c8">76%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h57">0</td><td class="ctr2" id="i15">5</td><td class="ctr1" id="j57">0</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a47"><a href="Rustycluster.java.html#L28313" class="el_method">newInstance(GeneratedMessageV3.UnusedPrivateParameter)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a10"><a href="Rustycluster.java.html#L28859" class="el_method">getHashFieldsMap()</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a25"><a href="Rustycluster.java.html#L28923" class="el_method">getScriptKeysCount()</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a21"><a href="Rustycluster.java.html#L28976" class="el_method">getScriptArgsCount()</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a17"><a href="Rustycluster.java.html#L28574" class="el_method">getOperationTypeValue()</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a8"><a href="Rustycluster.java.html#L28848" class="el_method">getHashFields()</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a26"><a href="Rustycluster.java.html#L28912" class="el_method">getScriptKeysList()</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a22"><a href="Rustycluster.java.html#L28965" class="el_method">getScriptArgsList()</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a16"><a href="Rustycluster.java.html#L28581" class="el_method">getOperationType()</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c7">80%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h58">0</td><td class="ctr2" id="i29">2</td><td class="ctr1" id="j58">0</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a4"><a href="Rustycluster.java.html#L28318" class="el_method">getDescriptor()</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a45"><a href="Rustycluster.java.html#L29358" class="el_method">newBuilderForType()</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c61">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a2"><a href="Rustycluster.java.html#L30773" class="el_method">getDefaultInstance()</a></td><td class="bar" id="b55"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c62">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a60"><a href="Rustycluster.java.html#L30799" class="el_method">parser()</a></td><td class="bar" id="b56"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c63">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a18"><a href="Rustycluster.java.html#L30804" class="el_method">getParserForType()</a></td><td class="bar" id="b57"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c64">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i60">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a3"><a href="Rustycluster.java.html#L30809" class="el_method">getDefaultInstanceForType()</a></td><td class="bar" id="b58"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c65">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h55">1</td><td class="ctr2" id="i61">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a61"><a href="Rustycluster.java.html#L28297" class="el_method">Rustycluster.BatchOperation()</a></td><td class="bar" id="b59"><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="57" alt="57"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">0</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h59">0</td><td class="ctr2" id="i4">24</td><td class="ctr1" id="j59">0</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a62"><a href="Rustycluster.java.html#L28295" class="el_method">Rustycluster.BatchOperation(GeneratedMessageV3.Builder)</a></td><td class="bar" id="b60"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="37" alt="37"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f60">0</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h60">0</td><td class="ctr2" id="i5">15</td><td class="ctr1" id="j60">0</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a63"><a href="Rustycluster.java.html#L30769" class="el_method">static {...}</a></td><td class="bar" id="b61"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f61">0</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h61">0</td><td class="ctr2" id="i30">2</td><td class="ctr1" id="j61">0</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a13"><a href="Rustycluster.java.html#L28746" class="el_method">getIntValue()</a></td><td class="bar" id="b62"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f62">0</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h62">0</td><td class="ctr2" id="i62">1</td><td class="ctr1" id="j62">0</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a7"><a href="Rustycluster.java.html#L28773" class="el_method">getFloatValue()</a></td><td class="bar" id="b63"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f63">0</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h63">0</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j63">0</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a30"><a href="Rustycluster.java.html#L28800" class="el_method">getTtl()</a></td><td class="bar" id="b64"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f64">0</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h64">0</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j64">0</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a43"><a href="Rustycluster.java.html#L29360" class="el_method">newBuilder()</a></td><td class="bar" id="b65"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">0</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h65">0</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j65">0</td><td class="ctr2" id="k65">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
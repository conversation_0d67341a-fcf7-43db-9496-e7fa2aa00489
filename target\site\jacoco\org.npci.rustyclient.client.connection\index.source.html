<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.client.connection</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.client.connection</span></div><h1>org.npci.rustyclient.client.connection</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,056 of 3,383</td><td class="ctr2">39%</td><td class="bar">165 of 264</td><td class="ctr2">37%</td><td class="ctr1">195</td><td class="ctr2">286</td><td class="ctr1">522</td><td class="ctr2">855</td><td class="ctr1">95</td><td class="ctr2">152</td><td class="ctr1">8</td><td class="ctr2">18</td></tr></tfoot><tbody><tr><td id="a8"><a href="SharedConnectionManager.java.html" class="el_source">SharedConnectionManager.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="602" alt="602"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="42" alt="42"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f1">45</td><td class="ctr2" id="g2">45</td><td class="ctr1" id="h0">155</td><td class="ctr2" id="i0">155</td><td class="ctr1" id="j0">24</td><td class="ctr2" id="k2">24</td><td class="ctr1" id="l1">3</td><td class="ctr2" id="m1">3</td></tr><tr><td id="a0"><a href="AsyncConnectionManager.java.html" class="el_source">AsyncConnectionManager.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="432" alt="432"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="75" alt="75"/></td><td class="ctr2" id="c6">14%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="52" alt="52"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f0">49</td><td class="ctr2" id="g0">53</td><td class="ctr1" id="h1">95</td><td class="ctr2" id="i2">114</td><td class="ctr1" id="j1">22</td><td class="ctr2" id="k0">26</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a9"><a href="SharedConnectionPool.java.html" class="el_source">SharedConnectionPool.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="69" height="10" title="348" alt="348"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="20" alt="20"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">30</td><td class="ctr2" id="g4">30</td><td class="ctr1" id="h2">89</td><td class="ctr2" id="i5">89</td><td class="ctr1" id="j2">20</td><td class="ctr2" id="k3">20</td><td class="ctr1" id="l0">4</td><td class="ctr2" id="m0">4</td></tr><tr><td id="a4"><a href="ConnectionPool.java.html" class="el_source">ConnectionPool.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="233" alt="233"/></td><td class="ctr2" id="c7">1%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">14</td><td class="ctr2" id="g7">15</td><td class="ctr1" id="h3">58</td><td class="ctr2" id="i7">59</td><td class="ctr1" id="j3">10</td><td class="ctr2" id="k6">11</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">2</td></tr><tr><td id="a2"><a href="AsyncFailbackManager.java.html" class="el_source">AsyncFailbackManager.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="109" alt="109"/><img src="../jacoco-resources/greenbar.gif" width="70" height="10" title="352" alt="352"/></td><td class="ctr2" id="c3">76%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="17" alt="17"/></td><td class="ctr2" id="e2">60%</td><td class="ctr1" id="f4">15</td><td class="ctr2" id="g3">40</td><td class="ctr1" id="h5">27</td><td class="ctr2" id="i1">115</td><td class="ctr1" id="j6">4</td><td class="ctr2" id="k1">26</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m3">2</td></tr><tr><td id="a1"><a href="AsyncConnectionPool.java.html" class="el_source">AsyncConnectionPool.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="106" alt="106"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="143" alt="143"/></td><td class="ctr2" id="c4">57%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f6">10</td><td class="ctr2" id="g6">16</td><td class="ctr1" id="h6">27</td><td class="ctr2" id="i6">62</td><td class="ctr1" id="j4">8</td><td class="ctr2" id="k5">12</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m4">2</td></tr><tr><td id="a6"><a href="GrpcChannelFactory.java.html" class="el_source">GrpcChannelFactory.java</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="106" alt="106"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="20" alt="20"/></td><td class="ctr2" id="c5">15%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">6</td><td class="ctr2" id="g8">8</td><td class="ctr1" id="h4">36</td><td class="ctr2" id="i8">43</td><td class="ctr1" id="j5">5</td><td class="ctr2" id="k8">7</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a3"><a href="ConnectionManager.java.html" class="el_source">ConnectionManager.java</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="71" alt="71"/><img src="../jacoco-resources/greenbar.gif" width="76" height="10" title="386" alt="386"/></td><td class="ctr2" id="c2">84%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="86" height="10" title="46" alt="46"/></td><td class="ctr2" id="e1">71%</td><td class="ctr1" id="f3">18</td><td class="ctr2" id="g1">48</td><td class="ctr1" id="h8">16</td><td class="ctr2" id="i3">111</td><td class="ctr1" id="j7">2</td><td class="ctr2" id="k4">15</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a5"><a href="FailbackManager.java.html" class="el_source">FailbackManager.java</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="49" alt="49"/><img src="../jacoco-resources/greenbar.gif" width="64" height="10" title="326" alt="326"/></td><td class="ctr2" id="c1">86%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="32" alt="32"/></td><td class="ctr2" id="e0">80%</td><td class="ctr1" id="f7">8</td><td class="ctr2" id="g5">30</td><td class="ctr1" id="h7">19</td><td class="ctr2" id="i4">103</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k7">10</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a7"><a href="OperationType.java.html" class="el_source">OperationType.java</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="21" alt="21"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RedisClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.redis</a> &gt; <span class="el_source">RedisClient.java</span></div><h1>RedisClient.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.redis;

import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.connection.RedisConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Main client for interacting with Redis directly.
 * This client provides both synchronous and asynchronous operations.
 *
 * Synchronous methods: set(), get(), delete(), hSet(), etc.
 * Asynchronous methods: setAsync(), getAsync(), deleteAsync(), hSetAsync(), etc.
 *
 * Example usage:
 * &lt;pre&gt;
 * RedisClientConfig config = RedisClientConfig.builder()
 *     .addPrimaryNode(&quot;localhost&quot;, 6379)
 *     .authentication(&quot;username&quot;, &quot;password&quot;)
 *     .database(0)
 *     .build();
 *
 * try (RedisClient client = new RedisClient(config)) {
 *     // Synchronous operations
 *     client.set(&quot;key1&quot;, &quot;value1&quot;);
 *     String value = client.get(&quot;key1&quot;);
 *
 *     // Asynchronous operations
 *     CompletableFuture&amp;lt;Boolean&amp;gt; setFuture = client.setAsync(&quot;key2&quot;, &quot;value2&quot;);
 *     CompletableFuture&amp;lt;String&amp;gt; getFuture = client.getAsync(&quot;key2&quot;);
 * }
 * &lt;/pre&gt;
 */
public class RedisClient implements AutoCloseable {
<span class="fc" id="L40">    private static final Logger logger = LoggerFactory.getLogger(RedisClient.class);</span>

    private final RedisClientConfig config;
    private final RedisConnectionManager connectionManager;

    /**
     * Create a new RedisClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="fc" id="L50">    public RedisClient(RedisClientConfig config) {</span>
<span class="fc" id="L51">        this.config = config;</span>
<span class="fc" id="L52">        this.connectionManager = new RedisConnectionManager(config);</span>
<span class="fc" id="L53">        logger.info(&quot;RedisClient initialized with {} nodes&quot;, config.getNodes().size());</span>
<span class="fc" id="L54">    }</span>

    // ==================== STRING OPERATIONS ====================

    /**
     * Set a key-value pair.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
<span class="nc" id="L66">        logger.debug(&quot;Setting key: {}&quot;, key);</span>
        
        try {
<span class="nc" id="L69">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L70">                String result = connection.set(key, value);</span>
<span class="nc" id="L71">                return &quot;OK&quot;.equals(result);</span>
            });
<span class="nc" id="L73">        } catch (Exception e) {</span>
<span class="nc" id="L74">            logger.error(&quot;Error setting key: {}&quot;, key, e);</span>
<span class="nc" id="L75">            throw e;</span>
        }
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key The key
     * @param value The value
     * @param ttlSeconds The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttlSeconds) {
<span class="nc" id="L88">        logger.debug(&quot;Setting key with expiry: {}, ttl: {}&quot;, key, ttlSeconds);</span>
        
        try {
<span class="nc" id="L91">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L92">                String result = connection.setex(key, ttlSeconds, value);</span>
<span class="nc" id="L93">                return &quot;OK&quot;.equals(result);</span>
            });
<span class="nc" id="L95">        } catch (Exception e) {</span>
<span class="nc" id="L96">            logger.error(&quot;Error setting key with expiry: {}&quot;, key, e);</span>
<span class="nc" id="L97">            throw e;</span>
        }
    }

    /**
     * Set a key-value pair only if the key does not exist.
     *
     * @param key   The key
     * @param value The value
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value) {
<span class="nc" id="L109">        logger.debug(&quot;Setting key if not exists: {}&quot;, key);</span>
        
        try {
<span class="nc" id="L112">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L113">                Long result = connection.setnx(key, value);</span>
<span class="nc bnc" id="L114" title="All 4 branches missed.">                return result != null &amp;&amp; result == 1;</span>
            });
<span class="nc" id="L116">        } catch (Exception e) {</span>
<span class="nc" id="L117">            logger.error(&quot;Error setting key if not exists: {}&quot;, key, e);</span>
<span class="nc" id="L118">            throw e;</span>
        }
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found or if all nodes are unavailable
     */
    public String get(String key) {
<span class="nc" id="L129">        logger.debug(&quot;Getting key: {}&quot;, key);</span>
        
<span class="nc" id="L131">        return connectionManager.executeWithFailoverSilent(connection -&gt; </span>
<span class="nc" id="L132">            connection.get(key));</span>
    }

    /**
     * Delete a key.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
<span class="nc" id="L142">        logger.debug(&quot;Deleting key: {}&quot;, key);</span>
        
        try {
<span class="nc" id="L145">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L146">                Long result = connection.del(key);</span>
<span class="nc bnc" id="L147" title="All 4 branches missed.">                return result != null &amp;&amp; result &gt; 0;</span>
            });
<span class="nc" id="L149">        } catch (Exception e) {</span>
<span class="nc" id="L150">            logger.error(&quot;Error deleting key: {}&quot;, key, e);</span>
<span class="nc" id="L151">            throw e;</span>
        }
    }

    /**
     * Check if a key exists.
     *
     * @param key The key to check
     * @return True if the key exists, false otherwise or if all nodes are unavailable
     */
    public boolean exists(String key) {
<span class="nc" id="L162">        logger.debug(&quot;Checking if key exists: {}&quot;, key);</span>
        
<span class="nc" id="L164">        Boolean result = connectionManager.executeWithFailoverSilent(connection -&gt; </span>
<span class="nc" id="L165">            connection.exists(key));</span>
        
<span class="nc bnc" id="L167" title="All 4 branches missed.">        return result != null &amp;&amp; result;</span>
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key The key
     * @param ttlSeconds The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean expire(String key, long ttlSeconds) {
<span class="nc" id="L178">        logger.debug(&quot;Setting expiry on key: {}, ttl: {}&quot;, key, ttlSeconds);</span>
        
        try {
<span class="nc" id="L181">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L182">                Long result = connection.expire(key, ttlSeconds);</span>
<span class="nc bnc" id="L183" title="All 4 branches missed.">                return result != null &amp;&amp; result == 1;</span>
            });
<span class="nc" id="L185">        } catch (Exception e) {</span>
<span class="nc" id="L186">            logger.error(&quot;Error setting expiry on key: {}&quot;, key, e);</span>
<span class="nc" id="L187">            throw e;</span>
        }
    }

    /**
     * Increment a numeric value.
     *
     * @param key The key
     * @return The new value
     */
    public long incr(String key) {
<span class="nc" id="L198">        logger.debug(&quot;Incrementing key: {}&quot;, key);</span>
        
        try {
<span class="nc" id="L201">            return connectionManager.executeWithFailover(connection -&gt; </span>
<span class="nc" id="L202">                connection.incr(key));</span>
<span class="nc" id="L203">        } catch (Exception e) {</span>
<span class="nc" id="L204">            logger.error(&quot;Error incrementing key: {}&quot;, key, e);</span>
<span class="nc" id="L205">            throw e;</span>
        }
    }

    /**
     * Increment a numeric value by a specific amount.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
<span class="nc" id="L217">        logger.debug(&quot;Incrementing key: {} by {}&quot;, key, value);</span>
        
        try {
<span class="nc" id="L220">            return connectionManager.executeWithFailover(connection -&gt; </span>
<span class="nc" id="L221">                connection.incrBy(key, value));</span>
<span class="nc" id="L222">        } catch (Exception e) {</span>
<span class="nc" id="L223">            logger.error(&quot;Error incrementing key: {} by {}&quot;, key, value, e);</span>
<span class="nc" id="L224">            throw e;</span>
        }
    }

    /**
     * Decrement a numeric value.
     *
     * @param key The key
     * @return The new value
     */
    public long decr(String key) {
<span class="nc" id="L235">        logger.debug(&quot;Decrementing key: {}&quot;, key);</span>
        
        try {
<span class="nc" id="L238">            return connectionManager.executeWithFailover(connection -&gt; </span>
<span class="nc" id="L239">                connection.decr(key));</span>
<span class="nc" id="L240">        } catch (Exception e) {</span>
<span class="nc" id="L241">            logger.error(&quot;Error decrementing key: {}&quot;, key, e);</span>
<span class="nc" id="L242">            throw e;</span>
        }
    }

    /**
     * Decrement a numeric value by a specific amount.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
<span class="nc" id="L254">        logger.debug(&quot;Decrementing key: {} by {}&quot;, key, value);</span>
        
        try {
<span class="nc" id="L257">            return connectionManager.executeWithFailover(connection -&gt; </span>
<span class="nc" id="L258">                connection.decrBy(key, value));</span>
<span class="nc" id="L259">        } catch (Exception e) {</span>
<span class="nc" id="L260">            logger.error(&quot;Error decrementing key: {} by {}&quot;, key, value, e);</span>
<span class="nc" id="L261">            throw e;</span>
        }
    }

    // ==================== HASH OPERATIONS ====================

    /**
     * Set a field in a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
<span class="nc" id="L276">        logger.debug(&quot;Setting hash field: {}.{}&quot;, key, field);</span>
        
        try {
<span class="nc" id="L279">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L280">                Long result = connection.hset(key, field, value);</span>
<span class="nc bnc" id="L281" title="All 4 branches missed.">                return result != null &amp;&amp; result &gt;= 0;</span>
            });
<span class="nc" id="L283">        } catch (Exception e) {</span>
<span class="nc" id="L284">            logger.error(&quot;Error setting hash field: {}.{}&quot;, key, field, e);</span>
<span class="nc" id="L285">            throw e;</span>
        }
    }

    /**
     * Set multiple fields in a hash.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map&lt;String, String&gt; fields) {
<span class="nc" id="L297">        logger.debug(&quot;Setting multiple hash fields for key: {}, fields count: {}&quot;, key, fields.size());</span>
        
        try {
<span class="nc" id="L300">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L301">                String result = connection.hmset(key, fields);</span>
<span class="nc" id="L302">                return &quot;OK&quot;.equals(result);</span>
            });
<span class="nc" id="L304">        } catch (Exception e) {</span>
<span class="nc" id="L305">            logger.error(&quot;Error setting multiple hash fields for key: {}&quot;, key, e);</span>
<span class="nc" id="L306">            throw e;</span>
        }
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found or if all nodes are unavailable
     */
    public String hGet(String key, String field) {
<span class="nc" id="L318">        logger.debug(&quot;Getting hash field: {}.{}&quot;, key, field);</span>
        
<span class="nc" id="L320">        return connectionManager.executeWithFailoverSilent(connection -&gt; </span>
<span class="nc" id="L321">            connection.hget(key, field));</span>
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map&lt;String, String&gt; hGetAll(String key) {
<span class="nc" id="L331">        logger.debug(&quot;Getting all hash fields for key: {}&quot;, key);</span>
        
<span class="nc" id="L333">        return connectionManager.executeWithFailoverSilent(connection -&gt; </span>
<span class="nc" id="L334">            connection.hgetAll(key));</span>
    }

    /**
     * Check if a hash field exists.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if field exists, false otherwise or if all nodes are unavailable
     */
    public boolean hExists(String key, String field) {
<span class="nc" id="L345">        logger.debug(&quot;Checking if hash field exists: {}.{}&quot;, key, field);</span>
        
<span class="nc" id="L347">        Boolean result = connectionManager.executeWithFailoverSilent(connection -&gt; </span>
<span class="nc" id="L348">            connection.hexists(key, field));</span>
        
<span class="nc bnc" id="L350" title="All 4 branches missed.">        return result != null &amp;&amp; result;</span>
    }

    /**
     * Delete a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if successful, false otherwise
     */
    public boolean hDel(String key, String field) {
<span class="nc" id="L361">        logger.debug(&quot;Deleting hash field: {}.{}&quot;, key, field);</span>
        
        try {
<span class="nc" id="L364">            return connectionManager.executeWithFailover(connection -&gt; {</span>
<span class="nc" id="L365">                Long result = connection.hdel(key, field);</span>
<span class="nc bnc" id="L366" title="All 4 branches missed.">                return result != null &amp;&amp; result &gt; 0;</span>
            });
<span class="nc" id="L368">        } catch (Exception e) {</span>
<span class="nc" id="L369">            logger.error(&quot;Error deleting hash field: {}.{}&quot;, key, field, e);</span>
<span class="nc" id="L370">            throw e;</span>
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Ping the Redis server to check connectivity.
     *
     * @return True if ping successful, false otherwise
     */
    public boolean ping() {
<span class="nc" id="L382">        logger.debug(&quot;Pinging Redis server&quot;);</span>
        
        try {
<span class="nc" id="L385">            String result = connectionManager.executeWithFailover(connection -&gt; </span>
<span class="nc" id="L386">                connection.ping());</span>
<span class="nc" id="L387">            return &quot;PONG&quot;.equals(result);</span>
<span class="nc" id="L388">        } catch (Exception e) {</span>
<span class="nc" id="L389">            logger.warn(&quot;Ping failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L390">            return false;</span>
        }
    }

    /**
     * Perform a health check on the Redis cluster.
     *
     * @return True if healthy, false otherwise
     */
    public boolean healthCheck() {
<span class="nc" id="L400">        logger.debug(&quot;Performing health check&quot;);</span>
<span class="nc" id="L401">        return connectionManager.healthCheck();</span>
    }

    /**
     * Get connection pool statistics.
     *
     * @return Pool statistics for all nodes
     */
    public String getPoolStats() {
<span class="nc" id="L410">        return connectionManager.getPoolStats();</span>
    }

    // ==================== ASYNCHRONOUS OPERATIONS ====================

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value) {
<span class="nc" id="L423">        return CompletableFuture.supplyAsync(() -&gt; set(key, value));</span>
    }

    /**
     * Set a key-value pair with expiration asynchronously.
     *
     * @param key        The key
     * @param value      The value
     * @param ttlSeconds The time-to-live in seconds
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setExAsync(String key, String value, long ttlSeconds) {
<span class="nc" id="L435">        return CompletableFuture.supplyAsync(() -&gt; setEx(key, value, ttlSeconds));</span>
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture&lt;String&gt; getAsync(String key) {
<span class="nc" id="L445">        return CompletableFuture.supplyAsync(() -&gt; get(key));</span>
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key) {
<span class="nc" id="L455">        return CompletableFuture.supplyAsync(() -&gt; delete(key));</span>
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key to check
     * @return CompletableFuture that completes with true if the key exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; existsAsync(String key) {
<span class="nc" id="L465">        return CompletableFuture.supplyAsync(() -&gt; exists(key));</span>
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value) {
<span class="nc" id="L477">        return CompletableFuture.supplyAsync(() -&gt; hSet(key, field, value));</span>
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found
     */
    public CompletableFuture&lt;String&gt; hGetAsync(String key, String field) {
<span class="nc" id="L488">        return CompletableFuture.supplyAsync(() -&gt; hGet(key, field));</span>
    }

    /**
     * Get all hash fields asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture&lt;Map&lt;String, String&gt;&gt; hGetAllAsync(String key) {
<span class="nc" id="L498">        return CompletableFuture.supplyAsync(() -&gt; hGetAll(key));</span>
    }

    /**
     * Ping the Redis server asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; pingAsync() {
<span class="nc" id="L507">        return CompletableFuture.supplyAsync(this::ping);</span>
    }

    @Override
    public void close() {
<span class="fc" id="L512">        logger.info(&quot;Closing RedisClient&quot;);</span>
<span class="fc" id="L513">        connectionManager.close();</span>
<span class="fc" id="L514">        logger.info(&quot;RedisClient closed&quot;);</span>
<span class="fc" id="L515">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>
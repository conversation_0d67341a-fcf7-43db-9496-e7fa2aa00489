<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.redis.connection</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.redis.connection</span></div><h1>org.npci.rustyclient.redis.connection</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">689 of 899</td><td class="ctr2">23%</td><td class="bar">74 of 78</td><td class="ctr2">5%</td><td class="ctr1">59</td><td class="ctr2">70</td><td class="ctr1">165</td><td class="ctr2">219</td><td class="ctr1">22</td><td class="ctr2">31</td><td class="ctr1">1</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a1"><a href="RedisConnectionManager.java.html" class="el_source">RedisConnectionManager.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="298" alt="298"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="52" alt="52"/></td><td class="ctr2" id="c1">14%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="91" height="10" title="26" alt="26"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">24</td><td class="ctr2" id="g0">27</td><td class="ctr1" id="h0">67</td><td class="ctr2" id="i0">81</td><td class="ctr1" id="j0">11</td><td class="ctr2" id="k0">14</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a0"><a href="RedisConnectionFactory.java.html" class="el_source">RedisConnectionFactory.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="257" alt="257"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c2">4%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="34" alt="34"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">23</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h1">64</td><td class="ctr2" id="i1">69</td><td class="ctr1" id="j1">6</td><td class="ctr2" id="k2">8</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="RedisConnectionPool.java.html" class="el_source">RedisConnectionPool.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="134" alt="134"/><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="145" alt="145"/></td><td class="ctr2" id="c0">51%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">22%</td><td class="ctr1" id="f2">12</td><td class="ctr2" id="g2">18</td><td class="ctr1" id="h2">34</td><td class="ctr2" id="i2">69</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>